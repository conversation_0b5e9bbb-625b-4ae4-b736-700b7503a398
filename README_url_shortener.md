# URL Shortener

A simple URL shortener implementation with the following features:
- Convert long URLs to short ones
- Redirect from short URLs to original long URLs
- Track basic analytics (number of clicks)
- Set expiration for links
- Custom short codes

## Components

1. **url_shortener.py** - Core implementation of the URL shortener
2. **url_shortener_cli.py** - Command-line interface
3. **url_shortener_web.py** - Web interface using Flask
4. **test_url_shortener.py** - Unit tests

## Requirements

- Python 3.6+
- Flask (for web interface)

## Installation

```bash
pip install flask
```

## Usage

### Command-line Interface

Run the CLI:

```bash
python url_shortener_cli.py
```

Commands:
- `shorten <url> [--custom <code>] [--expires <days>]` - Shorten a URL
- `resolve <code>` - Resolve a shortened URL
- `stats <code>` - Get statistics for a shortened URL
- `list` - List all shortened URLs
- `help` - Show help message
- `exit` - Exit the program

### Web Interface

Run the web server:

```bash
python url_shortener_web.py
```

Then open your browser and navigate to `http://localhost:5000/`

### API Endpoints

- `POST /api/shorten` - Shorten a URL
  - Request body: `{"url": "https://example.com", "custom_code": "optional", "expiration_days": 30}`
  - Response: `{"shortened_url": "http://localhost:5000/abc123"}`

- `GET /<short_code>` - Redirect to the original URL

- `GET /api/stats/<short_code>` - Get statistics for a shortened URL
  - Response: `{"original_url": "https://example.com", "creation_date": "...", "expiration_date": "...", "clicks": 5, "expired": false}`

- `GET /api/urls` - List all URLs
  - Response: JSON object with all shortened URLs and their details

## Running Tests

```bash
python -m unittest test_url_shortener.py
```

## Future Enhancements

1. Persistent storage (database)
2. User authentication
3. More detailed analytics
4. Rate limiting
5. QR code generation for shortened URLs
