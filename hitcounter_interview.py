from collections import deque

class HitCounter:
    """
    We maintain a queue for this problem. The init and hit (append) method are quite straightforward. 
    The main operation is performed in getHits.
    In getHits, we keep popping elements from the queue (while queue exists), only if the difference
    between the current timestamp and the first element of the queue is greater than or equal to 300.
    What this does is that the queue would remove all elements that have timestamp difference of more 
    than or equal to 300. What we are left is the queue with the closest 300 timestamps to the current
    timestamp. If we encounter a difference of less than 300, then this operation stops. Finally, the 
    length of the queue is the number of "hits" at the current timestamp.
    """
    def __init__(self, time_window: int = 300):
        """Initialize HitCounter with configurable time window (default 5 minutes)"""
        self.queue = deque()
        self.time_window = time_window  # Now configurable in seconds

    def hit(self, timestamp: int) -> None:
        """Record a hit at the given timestamp."""
        # Remove any expired hits before adding new one
        while self.queue and timestamp - self.queue[0] >= self.time_window:
            self.queue.popleft()
        self.queue.append(timestamp)

    def getHits(self, timestamp: int) -> int:
        """Return the number of hits in the past time window."""
        # Remove any expired hits before counting
        while self.queue and timestamp - self.queue[0] >= self.time_window:
            self.queue.popleft()
        return len(self.queue)