from threading import Thread, Lock
from datetime import datetime
import time
import math

# token capacity/bucketsize  = 10 for Token bucket
# requests allowed  = 10 requests per second per user

userInstance = { }


class Ratelimit():
    def __init__(self) -> None:
        
        self.req_per_sec = 5
        self.lock = Lock()


class TokenBucket(Ratelimit):
    def __init__(self) -> None:
        super().__init__()

        self.capacity = 10
        self.tokens_per_interval = 5
        self.token = 10
        self.last_updated = datetime.now()

    def allowed(self):
        with self.lock:
            if self.token <=0:
                print("request is throttled")
            
            fill_tokens_dynamic = self.fill_rate()
            self.token = min(self.capacity, fill_tokens_dynamic + self.token)
            

            if self.token > 0:
                self.token-=1
                print("request is allowed")
                return True

            

    def fill_rate(self):
        gap = (datetime.now() - self.last_updated).total_seconds()
        print("gap", gap)
        fill_tokens_dynamic = gap* self.tokens_per_interval
        print("dynamic tokens", fill_tokens_dynamic)
        self.last_updated = datetime.now()
        return fill_tokens_dynamic


class FixedTimeWindow(Ratelimit):
    def __init__(self) -> None:
        super().__init__()

        self.interval = 1
        self.count = 0
        self.start_time = datetime.now()

    def allowed(self):
        with self.lock:
            
            if (datetime.now() - self.start_time).total_seconds() > self.interval:
                self.count = 0
                self.start_time = datetime.now()
            
            if self.count > self.req_per_sec:
                print("Fixed time algo, requests throttled")
                return False
            
            self.count+=1
            print("Fixed time algo, request is allowed")
            return True

class SlidingWindow(Ratelimit):
    def __init__(self) -> None:
        super().__init__()

        self.count = 0
        self.last_updated  = datetime.now()
        self.interval = 1
        self.logs = []

    def allowed(self):
        with self.lock:
            # if (datetime.now() - self.last_updated).total_seconds() < self.interval:
            #     if self.count > self.req_per_sec:
            #         print("sliding window, request throttled")
            #         return False
            #     self.count+=1
            #     print("sliding window, request allowed")
            #     return True
            # else:
            #     self.last_updated = datetime.now()
            #     self.count = 0
            curr = datetime.now()
            while len(self.logs)>0 and (curr-self.logs[0]).total_seconds()>self.interval:
                self.logs.pop(0)

            if len(self.logs)>=self.req_per_sec:
                print("sliding window, request throttled")
                return
            
            self.logs.append(curr)
            print("sliding window, request allowed")
            return True


            
            




            




userids = [7,8,9,7,8,9,7,8,9]         
for user in userids:
    if user not in userInstance:
        print("first time user {}".format(user))
        userInstance[user] = SlidingWindow()
    else:
        for i in range(20):
            print("user spotted again {}".format(user))
            res= Thread(target = userInstance[user].allowed)
            time.sleep(0.1)
            res.start()
            print("count at this point", userInstance[user].count)
            # print("Tokens at this point", userInstance[user].token)

        


# tb = TokenBucket()

# for i in range(20):
#     res= Thread(target = tb.allowed)
#     time.sleep(0.1)
#     res.start()
#     print("Tokens at this point", tb.token)


# time.sleep(1)
# while True:
#     res= Thread(target = tb.allowed)
#     time.sleep(0.1)
#     res.start()









    
