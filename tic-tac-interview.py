import numpy as np


class TicTacToe:

    def __init__(self, n: int):
        self.grid = np.zeros((n,n))

        self.players_dict = {}

        self.poss_array = [ [(0,0), (0,1), (0,2)] , [(0,0),(1,1),(2,2)] , [(2,0), 
                                    (1,1),(0,2)], [(0,0),(1,0),(2,0)],[(2,0),(2,1),(2,2)], [(0,2),(1,2),(2,2)] ]
                

    def move(self, row: int, col: int, player: int) -> int:
        self.grid[row, col] = player
        if player not in self.players_dict:
            self.players_dict.setdefault(player, [])
            self.players_dict[player].append((row,col))
        else:
            self.players_dict[player].append((row,col))

        print("player moves so far for player {}".format(player),self.players_dict[player])
        for i in self.poss_array:
            if all(a in self.players_dict[player] for a in i):
                if player == 1:
                    return 1
                elif player == 2:
                    return 2
        return 0
    

["TicTacToe","move","move","move","move","move","move","move"]
[[3],[0,0,1],[0,2,2],[2,2,1],[1,1,2],[2,0,1],[1,0,2],[2,1,1]]