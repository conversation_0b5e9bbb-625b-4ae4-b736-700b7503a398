from typing import Optional, List

class ListNode:
    def __init__(self, val=0):
        self.val = val
        self.next = None



def reverseList(elements: Optional[List]):
    cur = dummy = ListNode(0)
    for e in elements:
        cur.next = ListNode(e)
        cur = cur.next
    head = dummy.next


    # Initialize prev pointer as NULL...
    prev = None
    # Initialize the curr pointer as the head...
    curr = head
    # Run a loop till curr points to NULL...
    while curr:
        # Initialize next pointer as the next pointer of curr...
        next = curr.next
        # Now assign the prev pointer to curr’s next pointer.
        curr.next = prev
        # Assign curr to prev, next to curr...
        prev = curr
        curr = next
    return prev 
