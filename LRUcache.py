class LRUCache:

    def __init__(self, capacity: int):
        self.map = {}
        self.capacity  = capacity
        self.recent = None
        

    def get(self, key: int) -> int:
        if key not in self.map.keys():
            return -1
        else:
            self.recent = [key]
            return self.map[key]


        

    def put(self, key: int, value: int) -> None:
        if len(self.map) == self.capacity:
            to_delete = set(self.map.keys()).difference(self.recent)
            for d in to_delete:
                del self.map[d]
        
        self.map[key] = value
        self.recent = [key]

    def show(self):
        print("cache", self.map)
        


capacity =2
key=2
value=2
obj = LRUCache(capacity)
for i in range(0, 10):
    param_1 = obj.get(key)
    obj.put(key,value)
    obj.show()
    key = key+1
    value = value+1