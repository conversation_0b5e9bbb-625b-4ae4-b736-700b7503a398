"""
Command-line interface for the URL Shortener.
"""
from url_shortener import URLShortener
import sys

def print_help():
    """Print help information."""
    print("\nURL Shortener CLI")
    print("=================")
    print("Commands:")
    print("  shorten <url> [--custom <code>] [--expires <days>] - Shorten a URL")
    print("  resolve <code> - Resolve a shortened URL")
    print("  stats <code> - Get statistics for a shortened URL")
    print("  list - List all shortened URLs")
    print("  help - Show this help message")
    print("  exit - Exit the program")
    print()

def main():
    """Main function to run the CLI."""
    shortener = URLShortener()
    print("URL Shortener CLI")
    print("Type 'help' for available commands")
    
    while True:
        try:
            command = input("\n> ").strip()
            
            if command == "exit":
                print("Goodbye!")
                break
                
            elif command == "help":
                print_help()
                
            elif command.startswith("shorten "):
                parts = command.split()
                url = parts[1]
                
                custom_code = None
                expiration_days = None
                
                # Parse optional arguments
                i = 2
                while i < len(parts):
                    if parts[i] == "--custom" and i + 1 < len(parts):
                        custom_code = parts[i + 1]
                        i += 2
                    elif parts[i] == "--expires" and i + 1 < len(parts):
                        try:
                            expiration_days = int(parts[i + 1])
                            i += 2
                        except ValueError:
                            print("Error: Expiration days must be a number")
                            i += 2
                    else:
                        i += 1
                
                shortened_url = shortener.shorten_url(url, custom_code, expiration_days)
                print(f"Shortened URL: {shortened_url}")
                
            elif command.startswith("resolve "):
                code = command.split()[1]
                original_url = shortener.get_original_url(code)
                print(f"Original URL: {original_url}")
                
            elif command.startswith("stats "):
                code = command.split()[1]
                stats = shortener.get_url_stats(code)
                if isinstance(stats, str):
                    print(stats)
                else:
                    print("\nURL Statistics:")
                    print(f"Original URL: {stats['original_url']}")
                    print(f"Creation Date: {stats['creation_date']}")
                    print(f"Expiration Date: {stats['expiration_date']}")
                    print(f"Clicks: {stats['clicks']}")
                    print(f"Expired: {stats['expired']}")
                
            elif command == "list":
                urls = shortener.list_all_urls()
                if not urls:
                    print("No URLs have been shortened yet.")
                else:
                    print("\nAll Shortened URLs:")
                    for code, details in urls.items():
                        custom_codes = details.get('custom_codes', [])
                        custom_str = f" (Custom: {', '.join(custom_codes)})" if custom_codes else ""
                        print(f"{code}{custom_str}: {details['original_url']} - Clicks: {details['clicks']}")
                
            else:
                print("Unknown command. Type 'help' for available commands.")
                
        except Exception as e:
            print(f"Error: {e}")

if __name__ == "__main__":
    main()
