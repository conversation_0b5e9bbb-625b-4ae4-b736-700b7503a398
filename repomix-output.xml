This file is a merged representation of the entire codebase, combined into a single document by Repomix.

<file_summary>
This section contains a summary of this file.

<purpose>
This file contains a packed representation of the entire repository's contents.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.
</purpose>

<file_format>
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Repository files (if enabled)
4. Repository files, each consisting of:
  - File path as an attribute
  - Full contents of the file
</file_format>

<usage_guidelines>
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.
</usage_guidelines>

<notes>
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded
- Files are sorted by Git change count (files with more changes are at the bottom)
</notes>

<additional_info>

</additional_info>

</file_summary>

<directory_structure>
advanced_system_design_guide/
  01_Foundations/
    1.1_System_Design_First_Principles/
      examples/
        url_shortener/
          README.md
        e_commerce_example.py
      exercises/
        trade_offs_exercise.md
      interview_questions.md
      README.md
      theory.md
  curriculum.md
  getting_started.md
  README.md
system_design_guide/
  01_Foundation/
    04_Performance_Optimization/
      00_Overview.md
      01_Performance_Metrics.md
      02_Profiling_and_Bottlenecks.md
      03_Algorithmic_Optimization.md
      04_Database_Optimization.md
    code/
      fault_tolerant_api/
        api_client.py
        example_app.py
        README.md
        requirements.txt
        test_api_client.py
      scalable_counter/
        counter_api.py
        counter_service.py
        docker-compose.yml
        Dockerfile
        load_test.py
        README.md
        requirements.txt
      url_shortener/
        app.py
        README.md
        url_shortener.py
    01_Basic_System_Design_Principles.md
    02_Scalability_Fundamentals.md
    03_Reliability_and_Fault_Tolerance.md
  getting_started.md
  module_outline.md
  next_steps.md
  README.md
best_time_to_buysell_stock.py
design_snake_ladder.py
file_system_with_regexpy
file_system_with_size.py
file_system.py
hitcounter_interview.py
hitcounter.py
logger_ratelimiter.py
LRUcache.py
online_election.py
parkinglot.py
rate_limiter_interview.py
ratelimiting.py
README_url_shortener.md
remove_dups.py
rev_linkedlist.py
sample.py
snake_game_interview.py
snake_game.py
splitwise.py
test_url_shortener.py
tic-tac-interview.py
tic-tac.py
url_shortener_cli.py
url_shortener_web.py
url_shortener.py
voting_another_interview.py
voting.py
</directory_structure>

<files>
This section contains the contents of the repository's files.

<file path="advanced_system_design_guide/01_Foundations/1.1_System_Design_First_Principles/examples/url_shortener/README.md">
# URL Shortener System Design

This example demonstrates how to apply system design first principles to a common interview problem: designing a URL shortening service like bit.ly or TinyURL.

## Problem Statement

Design a service that:
1. Takes a long URL as input and returns a shortened URL
2. When users access the shortened URL, redirects them to the original long URL
3. Provides basic analytics on URL usage
4. Handles high traffic volumes efficiently

## Applying First Principles

### Separation of Concerns

We'll divide our system into distinct components, each with a specific responsibility:

1. **URL Shortening Service**: Handles the creation of short URLs
2. **URL Redirection Service**: Manages redirects from short URLs to original URLs
3. **Analytics Service**: Tracks and reports on URL usage
4. **Storage Service**: Manages persistent storage of URL mappings
5. **Cache Service**: Provides fast access to frequently accessed URLs

### Single Responsibility Principle

Each component has a single, well-defined responsibility:

- **URL Shortening Service**: Only concerned with generating unique short codes
- **URL Redirection Service**: Only concerned with looking up and redirecting URLs
- **Analytics Service**: Only concerned with collecting and processing usage data

### Design for Scale, Performance, and Reliability

- **Read-Heavy Workload**: The system will have many more redirects than new URL creations
- **Caching Strategy**: Frequently accessed URLs are cached to reduce database load
- **Horizontal Scaling**: Stateless services can be scaled independently
- **Database Sharding**: URL mappings can be sharded based on the short code
- **Redundancy**: Multiple instances of each service for high availability

### Trade-offs

1. **Short URL Length vs. Uniqueness**:
   - Shorter URLs are more user-friendly but limit the number of unique URLs
   - We choose a 7-character alphanumeric code, providing 62^7 ≈ 3.5 trillion possibilities

2. **Custom URLs vs. System Simplicity**:
   - Allowing custom short URLs adds complexity but improves user experience
   - We support custom URLs as an optional feature

3. **Analytics Depth vs. Performance**:
   - More detailed analytics increases storage and processing requirements
   - We implement basic analytics (clicks, referrers, timestamps) with an option for more detailed tracking

4. **Consistency vs. Availability**:
   - In the rare case of a conflict, we prioritize availability over consistency
   - URL creation can have eventual consistency, but redirects must be highly available

## System Architecture

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  API Gateway    │────▶│  Load Balancer  │────▶│  Web Servers    │
│                 │     │                 │     │                 │
└─────────────────┘     └─────────────────┘     └────────┬────────┘
                                                         │
                                                         ▼
┌─────────────────┐                           ┌─────────────────────┐
│                 │                           │                     │
│  Analytics      │◀──────────────────────────│  URL Services       │
│  Service        │                           │  - Shortening       │
│                 │                           │  - Redirection      │
└─────────────────┘                           │                     │
                                              └──────────┬──────────┘
                                                         │
                        ┌─────────────────┐             │
                        │                 │             │
                        │  Cache Layer    │◀────────────┘
                        │  (Redis)        │             │
                        │                 │             │
                        └────────┬────────┘             │
                                 │                      │
                                 ▼                      ▼
                        ┌─────────────────┐    ┌─────────────────┐
                        │                 │    │                 │
                        │  Database       │    │  Database       │
                        │  (Primary)      │    │  (Replica)      │
                        │                 │    │                 │
                        └─────────────────┘    └─────────────────┘
```

## Component Details

### URL Shortening Service

**Responsibility**: Generate short URLs from long URLs

**Key Functions**:
- Generate a unique short code for a given URL
- Check for collisions with existing codes
- Handle custom URL requests
- Store the mapping in the database

**Algorithm Options**:
1. **Counter-based**: Use an auto-incrementing counter and convert to base62
2. **Hash-based**: Generate a hash of the long URL and take the first 7 characters
3. **Random Generation**: Generate random 7-character strings and check for collisions

We choose a counter-based approach for guaranteed uniqueness and sequential generation.

### URL Redirection Service

**Responsibility**: Redirect users from short URLs to original URLs

**Key Functions**:
- Look up the original URL for a given short code
- Perform the HTTP redirect
- Record analytics data for the access
- Handle invalid or expired URLs

**Optimization**:
- Cache frequently accessed URLs in Redis
- Use read replicas for the database to handle high read loads

### Analytics Service

**Responsibility**: Track and analyze URL usage

**Key Functions**:
- Record access timestamps
- Track referrer information
- Count clicks per URL
- Generate usage reports

**Implementation**:
- Use asynchronous processing to avoid impacting redirect performance
- Aggregate data periodically for reporting

### Storage Service

**Responsibility**: Persist URL mappings and analytics data

**Schema**:
```
urls:
  - short_code (PK)
  - original_url
  - user_id (if applicable)
  - creation_date
  - expiration_date (if applicable)
  - custom (boolean)

clicks:
  - id (PK)
  - short_code (FK)
  - timestamp
  - referrer
  - user_agent
  - ip_address (anonymized)
```

**Database Choice**:
- Primary database: PostgreSQL or MySQL for ACID properties
- Consider NoSQL (like Cassandra) for very high scale

### Cache Service

**Responsibility**: Provide fast access to frequently accessed URLs

**Implementation**:
- Redis for in-memory caching
- LRU eviction policy
- TTL for cache entries

## Scaling Considerations

1. **Horizontal Scaling**:
   - All services are stateless and can be scaled horizontally
   - Database read replicas for handling high read loads

2. **Database Sharding**:
   - Shard based on the first character(s) of the short code
   - Consistent hashing for distribution

3. **Geographic Distribution**:
   - CDN for static assets
   - Regional deployments for lower latency

4. **Rate Limiting**:
   - Prevent abuse of the URL creation API
   - Token bucket algorithm for rate limiting

## Failure Handling

1. **Service Redundancy**:
   - Multiple instances of each service
   - Automatic failover

2. **Database Redundancy**:
   - Primary-replica setup
   - Regular backups

3. **Graceful Degradation**:
   - If analytics service is down, still allow redirects
   - If custom URL creation fails, fall back to automatic generation

## Implementation Example

See the accompanying code files for a simplified implementation of this design:

- `url_shortener.py`: Core URL shortening logic
- `redirection_service.py`: URL redirection handling
- `storage.py`: Database interaction
- `cache.py`: Caching implementation
- `analytics.py`: Basic analytics tracking

## Conclusion

This URL shortener design demonstrates the application of system design first principles:

1. **Separation of Concerns**: Clear boundaries between different system responsibilities
2. **Single Responsibility Principle**: Each component has one well-defined purpose
3. **Design for Scale**: Considerations for handling high traffic and growth
4. **Trade-offs**: Explicit decisions about competing concerns

By applying these principles, we've created a design that is scalable, maintainable, and reliable.
</file>

<file path="advanced_system_design_guide/01_Foundations/1.1_System_Design_First_Principles/examples/e_commerce_example.py">
"""
E-Commerce System Example: Applying First Principles

This example demonstrates how to apply Separation of Concerns (SoC) and
Single Responsibility Principle (SRP) to an e-commerce system.
"""

# -------------------------------------------------------------------------
# Separation of Concerns: Each class handles a distinct aspect of the system
# -------------------------------------------------------------------------

class ProductCatalog:
    """Manages product information and search functionality."""
    
    def __init__(self, database_connection):
        self.db = database_connection
        
    def get_product(self, product_id):
        """Retrieve a product by its ID."""
        # In a real system, this would query the database
        return self.db.query(f"SELECT * FROM products WHERE id = {product_id}")
    
    def search_products(self, query, filters=None):
        """Search for products matching the query and filters."""
        # Implementation would include search logic
        base_query = f"SELECT * FROM products WHERE name LIKE '%{query}%'"
        if filters:
            # Add filter conditions to the query
            pass
        return self.db.query(base_query)
    
    def get_product_recommendations(self, product_id):
        """Get recommended products based on a product."""
        # This might use a recommendation algorithm
        return self.db.query(f"SELECT * FROM product_recommendations WHERE product_id = {product_id}")


class ShoppingCart:
    """Handles shopping cart operations."""
    
    def __init__(self, user_id, database_connection):
        self.user_id = user_id
        self.db = database_connection
        
    def add_item(self, product_id, quantity=1):
        """Add an item to the cart."""
        # Check if product exists in cart already
        existing_item = self.db.query(
            f"SELECT * FROM cart_items WHERE user_id = {self.user_id} AND product_id = {product_id}"
        )
        
        if existing_item:
            # Update quantity
            self.db.execute(
                f"UPDATE cart_items SET quantity = quantity + {quantity} "
                f"WHERE user_id = {self.user_id} AND product_id = {product_id}"
            )
        else:
            # Insert new item
            self.db.execute(
                f"INSERT INTO cart_items (user_id, product_id, quantity) "
                f"VALUES ({self.user_id}, {product_id}, {quantity})"
            )
    
    def remove_item(self, product_id):
        """Remove an item from the cart."""
        self.db.execute(
            f"DELETE FROM cart_items WHERE user_id = {self.user_id} AND product_id = {product_id}"
        )
    
    def get_items(self):
        """Get all items in the cart."""
        return self.db.query(f"SELECT * FROM cart_items WHERE user_id = {self.user_id}")
    
    def clear(self):
        """Clear all items from the cart."""
        self.db.execute(f"DELETE FROM cart_items WHERE user_id = {self.user_id}")


class OrderProcessor:
    """Handles order creation and processing."""
    
    def __init__(self, database_connection, payment_service, inventory_service):
        self.db = database_connection
        self.payment_service = payment_service
        self.inventory_service = inventory_service
    
    def create_order(self, user_id, cart_items, shipping_address):
        """Create a new order from cart items."""
        # Start a transaction
        self.db.begin_transaction()
        
        try:
            # Create order record
            order_id = self.db.execute(
                f"INSERT INTO orders (user_id, status, shipping_address) "
                f"VALUES ({user_id}, 'pending', '{shipping_address}') RETURNING id"
            )
            
            # Add order items
            total_amount = 0
            for item in cart_items:
                product = self.db.query(f"SELECT price FROM products WHERE id = {item['product_id']}")
                item_total = product['price'] * item['quantity']
                total_amount += item_total
                
                self.db.execute(
                    f"INSERT INTO order_items (order_id, product_id, quantity, price) "
                    f"VALUES ({order_id}, {item['product_id']}, {item['quantity']}, {product['price']})"
                )
            
            # Update order with total
            self.db.execute(f"UPDATE orders SET total_amount = {total_amount} WHERE id = {order_id}")
            
            # Commit transaction
            self.db.commit_transaction()
            
            return order_id
            
        except Exception as e:
            # Rollback in case of error
            self.db.rollback_transaction()
            raise e
    
    def process_payment(self, order_id, payment_method):
        """Process payment for an order."""
        order = self.db.query(f"SELECT * FROM orders WHERE id = {order_id}")
        
        # Process payment through payment service
        payment_result = self.payment_service.process_payment(
            amount=order['total_amount'],
            payment_method=payment_method,
            order_id=order_id
        )
        
        if payment_result['success']:
            # Update order status
            self.db.execute(f"UPDATE orders SET status = 'paid' WHERE id = {order_id}")
            return True
        else:
            # Handle payment failure
            self.db.execute(
                f"UPDATE orders SET status = 'payment_failed', "
                f"payment_error = '{payment_result['error']}' WHERE id = {order_id}"
            )
            return False
    
    def fulfill_order(self, order_id):
        """Fulfill an order by reserving inventory and scheduling shipping."""
        order_items = self.db.query(f"SELECT * FROM order_items WHERE order_id = {order_id}")
        
        # Check inventory availability
        for item in order_items:
            inventory_result = self.inventory_service.reserve_inventory(
                product_id=item['product_id'],
                quantity=item['quantity']
            )
            
            if not inventory_result['success']:
                # Handle inventory shortage
                self.db.execute(
                    f"UPDATE orders SET status = 'inventory_shortage', "
                    f"fulfillment_error = '{inventory_result['error']}' WHERE id = {order_id}"
                )
                return False
        
        # Update order status
        self.db.execute(f"UPDATE orders SET status = 'fulfilled' WHERE id = {order_id}")
        return True


# -------------------------------------------------------------------------
# Single Responsibility Principle: Each class has one reason to change
# -------------------------------------------------------------------------

# Instead of a monolithic UserService, we have separate services:

class AuthenticationService:
    """Handles user authentication."""
    
    def __init__(self, database_connection):
        self.db = database_connection
    
    def login(self, email, password):
        """Authenticate a user and create a session."""
        user = self.db.query(f"SELECT * FROM users WHERE email = '{email}'")
        
        if not user:
            return {'success': False, 'error': 'User not found'}
        
        # In a real system, we would use proper password hashing
        if user['password_hash'] != hash(password):
            return {'success': False, 'error': 'Invalid password'}
        
        # Create session
        session_id = self._create_session(user['id'])
        
        return {
            'success': True,
            'user_id': user['id'],
            'session_id': session_id
        }
    
    def logout(self, session_id):
        """End a user session."""
        self.db.execute(f"UPDATE sessions SET active = FALSE WHERE id = '{session_id}'")
        return {'success': True}
    
    def verify_session(self, session_id):
        """Verify if a session is valid."""
        session = self.db.query(
            f"SELECT * FROM sessions WHERE id = '{session_id}' AND active = TRUE "
            f"AND expires_at > CURRENT_TIMESTAMP"
        )
        
        return {'valid': bool(session), 'user_id': session['user_id'] if session else None}
    
    def _create_session(self, user_id):
        """Create a new session for a user."""
        # Generate a unique session ID
        import uuid
        session_id = str(uuid.uuid4())
        
        # Set expiration (e.g., 24 hours from now)
        import datetime
        expires_at = datetime.datetime.now() + datetime.timedelta(hours=24)
        
        # Store session in database
        self.db.execute(
            f"INSERT INTO sessions (id, user_id, active, expires_at) "
            f"VALUES ('{session_id}', {user_id}, TRUE, '{expires_at}')"
        )
        
        return session_id


class ProfileService:
    """Manages user profile information."""
    
    def __init__(self, database_connection):
        self.db = database_connection
    
    def get_profile(self, user_id):
        """Get a user's profile information."""
        return self.db.query(f"SELECT * FROM user_profiles WHERE user_id = {user_id}")
    
    def update_profile(self, user_id, profile_data):
        """Update a user's profile information."""
        # Build SET clause for SQL update
        set_clause = ", ".join([f"{key} = '{value}'" for key, value in profile_data.items()])
        
        self.db.execute(
            f"UPDATE user_profiles SET {set_clause} WHERE user_id = {user_id}"
        )
        
        return {'success': True}
    
    def get_addresses(self, user_id):
        """Get a user's saved addresses."""
        return self.db.query(f"SELECT * FROM user_addresses WHERE user_id = {user_id}")
    
    def add_address(self, user_id, address_data):
        """Add a new address for a user."""
        # Extract address fields
        fields = ", ".join(address_data.keys())
        values = ", ".join([f"'{value}'" for value in address_data.values()])
        
        self.db.execute(
            f"INSERT INTO user_addresses (user_id, {fields}) VALUES ({user_id}, {values})"
        )
        
        return {'success': True}


class PermissionService:
    """Handles role-based access control."""
    
    def __init__(self, database_connection):
        self.db = database_connection
    
    def get_user_roles(self, user_id):
        """Get all roles assigned to a user."""
        return self.db.query(f"SELECT role FROM user_roles WHERE user_id = {user_id}")
    
    def has_permission(self, user_id, permission):
        """Check if a user has a specific permission."""
        # Get user roles
        roles = self.get_user_roles(user_id)
        
        # Check if any role has the required permission
        for role in roles:
            role_permissions = self.db.query(
                f"SELECT * FROM role_permissions WHERE role = '{role}' AND permission = '{permission}'"
            )
            if role_permissions:
                return True
        
        return False
    
    def assign_role(self, user_id, role):
        """Assign a role to a user."""
        self.db.execute(
            f"INSERT INTO user_roles (user_id, role) VALUES ({user_id}, '{role}')"
        )
        
        return {'success': True}
    
    def revoke_role(self, user_id, role):
        """Revoke a role from a user."""
        self.db.execute(
            f"DELETE FROM user_roles WHERE user_id = {user_id} AND role = '{role}'"
        )
        
        return {'success': True}


# -------------------------------------------------------------------------
# Example usage: How these components work together
# -------------------------------------------------------------------------

def example_usage():
    """Demonstrate how these components work together."""
    # This is a simplified example - in a real system, you would use a proper DB connection
    db_connection = MockDatabaseConnection()
    
    # Initialize services
    product_catalog = ProductCatalog(db_connection)
    auth_service = AuthenticationService(db_connection)
    profile_service = ProfileService(db_connection)
    permission_service = PermissionService(db_connection)
    
    # User logs in
    login_result = auth_service.login("<EMAIL>", "password123")
    if not login_result['success']:
        return "Login failed: " + login_result['error']
    
    user_id = login_result['user_id']
    session_id = login_result['session_id']
    
    # Check if user has permission to place orders
    if not permission_service.has_permission(user_id, "place_orders"):
        return "User does not have permission to place orders"
    
    # Get user's addresses
    addresses = profile_service.get_addresses(user_id)
    shipping_address = addresses[0] if addresses else None
    
    if not shipping_address:
        return "User has no shipping address"
    
    # Search for products
    search_results = product_catalog.search_products("smartphone")
    
    # Add a product to cart
    cart = ShoppingCart(user_id, db_connection)
    cart.add_item(search_results[0]['id'], quantity=1)
    
    # Create and process order
    payment_service = MockPaymentService()
    inventory_service = MockInventoryService()
    order_processor = OrderProcessor(db_connection, payment_service, inventory_service)
    
    cart_items = cart.get_items()
    order_id = order_processor.create_order(user_id, cart_items, shipping_address)
    
    # Process payment
    payment_success = order_processor.process_payment(order_id, "credit_card")
    if not payment_success:
        return "Payment failed"
    
    # Fulfill order
    fulfillment_success = order_processor.fulfill_order(order_id)
    if not fulfillment_success:
        return "Order fulfillment failed"
    
    # Clear cart after successful order
    cart.clear()
    
    # User logs out
    auth_service.logout(session_id)
    
    return "Order placed successfully"


# Mock classes for demonstration purposes
class MockDatabaseConnection:
    """Mock database connection for demonstration."""
    def query(self, sql):
        # This would normally execute SQL and return results
        print(f"Executing query: {sql}")
        # Return mock data based on the query
        if "products WHERE name LIKE" in sql:
            return [{'id': 1, 'name': 'Smartphone X', 'price': 999.99}]
        elif "cart_items WHERE user_id" in sql:
            return [{'product_id': 1, 'quantity': 1}]
        elif "user_profiles WHERE user_id" in sql:
            return {'name': 'John Doe', 'email': '<EMAIL>'}
        elif "user_addresses WHERE user_id" in sql:
            return [{'street': '123 Main St', 'city': 'Anytown', 'zip': '12345'}]
        elif "users WHERE email" in sql:
            return {'id': 1, 'password_hash': hash("password123")}
        elif "role_permissions WHERE role" in sql:
            return [{'permission': 'place_orders'}]
        elif "user_roles WHERE user_id" in sql:
            return [{'role': 'customer'}]
        return None
    
    def execute(self, sql):
        # This would normally execute SQL and modify data
        print(f"Executing statement: {sql}")
        if "RETURNING id" in sql:
            return 1  # Return mock order ID
        return True
    
    def begin_transaction(self):
        print("Beginning transaction")
    
    def commit_transaction(self):
        print("Committing transaction")
    
    def rollback_transaction(self):
        print("Rolling back transaction")


class MockPaymentService:
    """Mock payment service for demonstration."""
    def process_payment(self, amount, payment_method, order_id):
        print(f"Processing {payment_method} payment of ${amount} for order {order_id}")
        return {'success': True}


class MockInventoryService:
    """Mock inventory service for demonstration."""
    def reserve_inventory(self, product_id, quantity):
        print(f"Reserving {quantity} units of product {product_id}")
        return {'success': True}


if __name__ == "__main__":
    result = example_usage()
    print(result)
</file>

<file path="advanced_system_design_guide/01_Foundations/1.1_System_Design_First_Principles/exercises/trade_offs_exercise.md">
# Exercise: Analyzing Trade-offs in Real-world Systems

## Objective

In this exercise, you will analyze trade-offs in real-world systems and practice applying system design first principles to make informed decisions.

## Instructions

### Part 1: Analyzing Existing Systems

Choose two different systems from the following list:

1. A social media platform (e.g., Twitter, Instagram)
2. An e-commerce website (e.g., Amazon, eBay)
3. A video streaming service (e.g., Netflix, YouTube)
4. A ride-sharing application (e.g., Uber, Lyft)
5. A cloud storage service (e.g., Dropbox, Google Drive)

For each system:

1. Identify at least three key trade-offs the system designers likely had to make
2. Explain why these trade-offs were necessary
3. Describe how the system balances these competing concerns
4. Discuss how these trade-offs align with the system's primary goals and user expectations

**Example Trade-offs to Consider:**
- Consistency vs. Availability
- Latency vs. Throughput
- Performance vs. Maintainability
- Simplicity vs. Flexibility
- Cost vs. Reliability
- Security vs. Usability

### Part 2: Applying Separation of Concerns

Choose one of the systems you analyzed in Part 1 and:

1. Identify at least 5 major concerns that should be separated
2. For each concern:
   - Describe its primary responsibility
   - Explain why it should be separated from other concerns
   - Identify potential interfaces with other concerns
   - Discuss the benefits of this separation

### Part 3: Designing with Trade-offs

You are designing a new mobile payment application that allows users to:
- Send money to friends and family
- Pay merchants for goods and services
- Save payment methods and manage a digital wallet
- View transaction history and generate reports

For this application:

1. Identify at least 5 key trade-offs you would need to consider
2. For each trade-off:
   - Describe the competing concerns
   - Explain which side of the trade-off you would prioritize and why
   - Discuss how your decision aligns with the application's goals
   - Describe how you would mitigate the downsides of your choice

3. Apply the Single Responsibility Principle to identify at least 6 distinct components of the system
4. For each component:
   - Define its single responsibility
   - List its key functions
   - Describe how it would interact with other components

## Deliverables

For this exercise, create a document with the following sections:

1. **Existing Systems Analysis**
   - System 1 Trade-offs
   - System 2 Trade-offs

2. **Separation of Concerns Analysis**
   - System Components and Responsibilities
   - Component Interfaces
   - Benefits of Separation

3. **Mobile Payment App Design**
   - Trade-off Decisions and Rationale
   - Component Breakdown (applying SRP)
   - Component Interactions

## Evaluation Criteria

Your analysis will be evaluated based on:

1. **Depth of Understanding**: How well you identify and explain relevant trade-offs
2. **Practical Application**: How effectively you apply first principles to real-world scenarios
3. **Reasoning**: How clearly you justify your design decisions
4. **Completeness**: How thoroughly you address all parts of the exercise
5. **System Thinking**: How well you consider the system as a whole while analyzing its parts

## Extension (Optional)

If you want to challenge yourself further:

1. Create a high-level architecture diagram for your mobile payment application
2. Identify potential scaling challenges and how your design addresses them
3. Discuss how your design would evolve as the user base grows from 1,000 to 1 million to 100 million users
</file>

<file path="advanced_system_design_guide/01_Foundations/1.1_System_Design_First_Principles/interview_questions.md">
# System Design First Principles: Interview Questions

This document contains common interview questions related to system design first principles, along with guidance on how to approach them effectively.

## Conceptual Questions

### 1. What are the key principles you consider when designing a scalable system?

**Approach:** This is a broad question that allows you to demonstrate your understanding of system design fundamentals. Cover:

- Separation of concerns
- Single responsibility principle
- Designing for scale from the beginning
- Considering trade-offs explicitly
- Planning for failure
- Keeping components loosely coupled
- Making data access efficient

**Example Answer Structure:**
1. Start with a brief overview of why principles matter
2. Discuss each principle with a brief explanation
3. Provide a concrete example of applying one or two principles
4. Mention how these principles work together

### 2. Explain the concept of "separation of concerns" and why it's important in system design.

**Approach:** Define the principle, explain its benefits, and provide examples.

**Key Points to Cover:**
- Definition: Dividing a system into distinct sections, each addressing a separate concern
- Benefits: Improved maintainability, testability, and parallel development
- Examples: Layered architecture, microservices, MVC pattern
- Trade-offs: Potential overhead from additional interfaces and complexity

### 3. How do you balance competing concerns like performance, reliability, and cost in system design?

**Approach:** Discuss your framework for making trade-off decisions.

**Key Points to Cover:**
- Understanding business requirements and priorities
- Identifying which qualities are most important for the specific system
- Quantifying trade-offs where possible (e.g., cost vs. performance improvements)
- Considering the full system lifecycle, not just initial development
- Iterative approach: start simple, measure, then optimize where needed

### 4. What is the Single Responsibility Principle, and how does it apply to system design?

**Approach:** Define SRP, explain how it scales from classes to components and services, and discuss its benefits and challenges.

**Key Points to Cover:**
- Definition: A component should have only one reason to change
- Application at different levels: methods, classes, modules, services
- Benefits: Improved maintainability, testability, and reusability
- Challenges: Determining the right granularity, avoiding over-fragmentation
- Examples: Breaking down a monolithic service into focused microservices

## System Design Scenarios

### 5. Design a URL shortening service (like bit.ly).

**Approach:** Use this as an opportunity to demonstrate how you apply first principles to a concrete problem.

**Key Points to Cover:**
- Separation of concerns: API service, URL generation, storage, analytics
- Trade-offs: URL length vs. collision probability
- Scalability considerations: Read-heavy workload
- Reliability: Ensuring shortened URLs work consistently
- Performance: Fast redirects for user experience

**Example Structure:**
1. Clarify requirements and constraints
2. Identify key components and their responsibilities
3. Discuss data model and storage options
4. Address scalability and performance
5. Highlight trade-offs in your design decisions

### 6. How would you design a distributed cache system?

**Approach:** Focus on applying first principles to this specific distributed systems problem.

**Key Points to Cover:**
- Separation of concerns: Cache API, eviction policy, distribution strategy
- Trade-offs: Consistency vs. availability, memory usage vs. hit rate
- Single responsibility: Each component has a clear purpose
- Scalability: How the system grows with more nodes
- Reliability: Handling node failures

**Example Structure:**
1. Clarify requirements (size, latency requirements, consistency needs)
2. Break down into components with single responsibilities
3. Discuss distribution strategy and consistency model
4. Address failure scenarios and recovery
5. Explain trade-offs in your design choices

### 7. Design a news feed system for a social media platform.

**Approach:** Demonstrate how you handle a complex system with multiple concerns and significant trade-offs.

**Key Points to Cover:**
- Separation of concerns: User service, content service, feed generation, notification system
- Trade-offs: Real-time updates vs. system load
- Performance considerations: Read-heavy, personalized content
- Scalability: Handling millions of users and posts
- Data model: How to efficiently store and retrieve feed items

**Example Structure:**
1. Clarify requirements (feed freshness, personalization, scale)
2. Identify key components and their responsibilities
3. Discuss feed generation approaches (push vs. pull)
4. Address data storage and access patterns
5. Explain how your design handles scale and performance

## Behavioral Questions

### 8. Tell me about a time when you had to make a significant trade-off decision in a system you designed.

**Approach:** Use the STAR method (Situation, Task, Action, Result) to structure your response.

**Key Points to Cover:**
- The specific trade-off you faced
- How you evaluated the options
- The decision-making process and stakeholders involved
- The outcome and what you learned
- How you would approach it differently today (if applicable)

### 9. Describe a situation where you applied the principle of separation of concerns to improve a system.

**Approach:** Choose a specific example where you refactored or designed a system using this principle.

**Key Points to Cover:**
- The initial state of the system and its problems
- How you identified the concerns that needed separation
- The specific changes you made
- The benefits that resulted from the separation
- Any challenges you encountered in the process

### 10. How do you ensure that your designs adhere to the Single Responsibility Principle?

**Approach:** Discuss your practical approach to applying SRP in real-world systems.

**Key Points to Cover:**
- How you identify when a component has multiple responsibilities
- Your process for breaking down complex components
- How you balance granularity with practical considerations
- Tools or techniques you use to evaluate adherence to SRP
- Examples of how you've applied this in your work

## Tips for Answering System Design First Principles Questions

1. **Start with requirements:** Always begin by clarifying what the system needs to achieve.

2. **Think aloud:** Interviewers want to understand your thought process, not just your final answer.

3. **Justify trade-offs:** Explicitly state why you're making certain decisions and what you're trading off.

4. **Draw diagrams:** Visual representations help communicate your design clearly.

5. **Consider scale:** Think about how your design would work at different scales.

6. **Address failure modes:** Demonstrate that you design for reliability by discussing how your system handles failures.

7. **Be iterative:** Start with a simple design and then refine it, rather than trying to design the perfect system immediately.

8. **Connect to experience:** Where possible, relate your answers to real systems you've worked with.

9. **Ask clarifying questions:** This shows thoughtfulness and ensures you're solving the right problem.

10. **Acknowledge alternatives:** Mention other approaches you considered and why you didn't choose them.
</file>

<file path="advanced_system_design_guide/01_Foundations/1.1_System_Design_First_Principles/README.md">
# System Design First Principles

## Overview

System design first principles are the fundamental concepts that guide how we approach designing scalable, reliable, and maintainable systems. These principles help us make informed decisions when faced with trade-offs and constraints.

## Learning Objectives

By the end of this module, you will be able to:

1. Understand and apply key system design principles
2. Identify appropriate trade-offs based on requirements
3. Evaluate existing systems through the lens of these principles
4. Apply these principles to your own system designs
5. Communicate design decisions effectively using these principles

## Topics Covered

### Separation of Concerns

Separation of Concerns (SoC) is a design principle that advocates dividing a system into distinct sections, each addressing a separate concern. This principle:

- Improves maintainability by isolating changes
- Enhances readability by organizing related code
- Facilitates testing by creating clear boundaries
- Enables parallel development by different teams

### Single Responsibility Principle

The Single Responsibility Principle (SRP) states that a class or module should have only one reason to change. This principle:

- Reduces coupling between components
- Makes code more maintainable and testable
- Simplifies understanding of each component's purpose
- Facilitates code reuse

### Design for Scale, Performance, and Reliability

Designing systems with scale, performance, and reliability in mind from the beginning helps avoid costly redesigns later. This involves:

- Anticipating growth patterns
- Identifying potential bottlenecks
- Planning for failure scenarios
- Establishing monitoring and observability
- Defining clear performance metrics

### Trade-offs in System Design

Every system design involves trade-offs. Understanding these trade-offs helps make informed decisions:

- Consistency vs. Availability
- Latency vs. Throughput
- Performance vs. Maintainability
- Simplicity vs. Flexibility
- Cost vs. Reliability

## Key Takeaways

1. There is no "perfect" system design—only designs that best meet specific requirements
2. First principles provide a framework for making and justifying design decisions
3. Different contexts may require different prioritization of principles
4. The ability to articulate trade-offs is as important as the design itself
5. System design is iterative—principles guide evolution over time

## Next Steps

1. Read the detailed explanation in `theory.md`
2. Study the examples in the `examples/` directory
3. Complete the exercises in the `exercises/` directory
4. Practice with the interview questions in `interview_questions.md`
5. Move on to the next module: Scalability Fundamentals

## Additional Resources

- "Designing Data-Intensive Applications" by Martin Kleppmann
- "Clean Architecture" by Robert C. Martin
- "System Design Interview" by Alex Xu
- "Fundamentals of Software Architecture" by Mark Richards and Neal Ford
</file>

<file path="advanced_system_design_guide/01_Foundations/1.1_System_Design_First_Principles/theory.md">
# System Design First Principles: Detailed Theory

## Introduction

First principles thinking involves breaking down complex problems into their most fundamental elements and then reassembling them from the ground up. In system design, this approach helps us create solutions that are tailored to specific requirements rather than blindly following established patterns.

## Core Principles

### Separation of Concerns (SoC)

#### Concept
Separation of Concerns is about dividing a system into distinct features with minimal overlap. Each part of the system should address a specific aspect of the functionality.

#### Implementation Approaches
1. **Layered Architecture**: Organizing code into layers (presentation, business logic, data access)
2. **Microservices**: Dividing a system into independent services based on business capabilities
3. **MVC/MVVM Patterns**: Separating data, business logic, and user interface
4. **Aspect-Oriented Programming**: Separating cross-cutting concerns like logging or security

#### Benefits
- Changes to one area have minimal impact on others
- Different teams can work on different concerns simultaneously
- Testing becomes more focused and manageable
- Components can be reused across different parts of the system

#### Practical Example
Consider an e-commerce application:
- **Product Catalog**: Manages product information and search
- **Shopping Cart**: Handles item selection and quantity
- **User Management**: Deals with authentication and profiles
- **Order Processing**: Manages checkout and payment
- **Shipping**: Handles delivery logistics

Each of these concerns can be developed, tested, and maintained independently.

### Single Responsibility Principle (SRP)

#### Concept
The Single Responsibility Principle states that a class or module should have only one reason to change. This is often interpreted as having only one job or responsibility.

#### Implementation Approaches
1. **Small, Focused Classes**: Creating classes that do one thing well
2. **Function Composition**: Building complex behavior from simple, single-purpose functions
3. **Command Pattern**: Encapsulating requests as objects
4. **Service Classes**: Creating services that handle specific business operations

#### Benefits
- Code is easier to understand and maintain
- Changes are localized to relevant components
- Testing is simplified as each unit has a clear purpose
- Reuse is facilitated as components have well-defined responsibilities

#### Practical Example
Instead of a monolithic `UserService` that handles authentication, profile management, and permissions, we might have:
- `AuthenticationService`: Handles login, logout, and session management
- `ProfileService`: Manages user profile information
- `PermissionService`: Handles role-based access control

### Design for Scale, Performance, and Reliability

#### Concept
Systems should be designed with future growth in mind, considering how they will perform under increased load and how they will handle failures.

#### Implementation Approaches
1. **Horizontal Scaling**: Adding more machines to distribute load
2. **Caching Strategies**: Reducing database load and improving response times
3. **Asynchronous Processing**: Handling time-consuming tasks outside the main request flow
4. **Load Balancing**: Distributing traffic across multiple instances
5. **Circuit Breakers**: Preventing cascading failures
6. **Redundancy**: Eliminating single points of failure

#### Benefits
- Systems can grow with increasing demand
- Performance remains acceptable under varying loads
- Failures in one component don't bring down the entire system
- Maintenance can be performed with minimal downtime

#### Practical Example
A social media platform might:
- Use a CDN to cache and serve static content
- Implement read replicas for database queries
- Process image uploads asynchronously
- Use message queues for activity feed updates
- Implement circuit breakers for external API calls
- Deploy across multiple availability zones

### Trade-offs in System Design

#### Concept
Every design decision involves trade-offs. Understanding these trade-offs helps make informed decisions based on specific requirements.

#### Common Trade-offs
1. **Consistency vs. Availability**: As per the CAP theorem, distributed systems must choose
2. **Latency vs. Throughput**: Optimizing for one often comes at the expense of the other
3. **Performance vs. Maintainability**: Highly optimized code is often harder to maintain
4. **Simplicity vs. Flexibility**: Simple designs are easier to understand but may be less adaptable
5. **Cost vs. Reliability**: Increasing reliability often requires additional resources

#### Decision Framework
When facing trade-offs, consider:
1. **Business Requirements**: What does the business value most?
2. **User Expectations**: What do users expect from the system?
3. **Growth Projections**: How will needs change over time?
4. **Resource Constraints**: What budget and team constraints exist?
5. **Risk Tolerance**: What level of risk is acceptable?

#### Practical Example
For a payment processing system:
- **Consistency** might be prioritized over availability (can't afford double-payments)
- **Security** might be prioritized over performance (thorough validation is worth the extra milliseconds)
- **Reliability** might be prioritized over cost (downtime is extremely expensive)

For a content delivery system:
- **Availability** might be prioritized over consistency (temporary inconsistency is acceptable)
- **Performance** might be prioritized over security for public content (speed is critical for user experience)
- **Cost** might be balanced with reliability (some downtime is acceptable if it significantly reduces costs)

## Applying First Principles in Practice

### System Design Process

1. **Understand Requirements**: Clearly define functional and non-functional requirements
2. **Identify Components**: Break down the system into components based on SoC
3. **Define Interfaces**: Establish how components will interact
4. **Consider Scale**: Anticipate growth and design accordingly
5. **Plan for Failure**: Identify potential failure points and mitigation strategies
6. **Evaluate Trade-offs**: Make deliberate choices based on requirements
7. **Iterate**: Refine the design based on feedback and changing requirements

### Common Pitfalls

1. **Premature Optimization**: Optimizing before understanding actual bottlenecks
2. **Over-engineering**: Adding unnecessary complexity for anticipated future needs
3. **Ignoring Non-functional Requirements**: Focusing solely on features without considering performance, security, etc.
4. **Rigid Adherence to Patterns**: Applying design patterns without considering if they fit the problem
5. **Neglecting Operational Concerns**: Designing systems that are difficult to deploy, monitor, or maintain

## Conclusion

System design first principles provide a foundation for creating systems that meet specific requirements while remaining scalable, reliable, and maintainable. By understanding these principles and the trade-offs involved, you can make informed decisions that lead to successful system designs.

Remember that system design is both an art and a science—principles guide us, but creativity and experience help us apply them effectively to unique problems.
</file>

<file path="advanced_system_design_guide/curriculum.md">
# Advanced System Design Curriculum

This curriculum is designed to be completed over approximately 12 weeks, with daily practice sessions. Each module builds upon previous concepts and includes theoretical knowledge, practical examples, coding exercises, and interview preparation materials.

## 01. Foundations (Weeks 1-2)

### 1.1 System Design First Principles
- Separation of Concerns
- Single Responsibility Principle
- Design for Scale, Performance, and Reliability
- Trade-offs in System Design
- Exercise: Analyzing Trade-offs in Real-world Systems

### 1.2 Scalability Fundamentals
- Vertical vs. Horizontal Scaling
- Load Balancing Basics
- Stateless Services
- Database Scaling Introduction
- Exercise: Designing a Scalable Web Service

### 1.3 Performance Engineering
- Performance Metrics and SLOs
- Latency vs. Throughput
- Profiling and Bottleneck Identification
- Optimization Strategies
- Exercise: Performance Analysis of a REST API

### 1.4 Distributed Systems Basics
- CAP Theorem
- Eventual Consistency
- Failure Modes
- Distributed Tracing
- Exercise: Implementing a Fault-Tolerant Service

### 1.5 Data Modeling and Storage Paradigms
- Relational vs. Non-relational Models
- Document, Key-Value, and Graph Stores
- Time-Series Data
- Storage Hierarchy and Access Patterns
- Exercise: Choosing the Right Database for Different Scenarios

## 02. Core Components (Weeks 3-4)

### 2.1 API Design and Patterns
- RESTful API Design
- GraphQL
- gRPC and Protocol Buffers
- API Versioning and Evolution
- Exercise: Designing a Versioned API

### 2.2 Microservices Architecture
- Service Decomposition Strategies
- Inter-service Communication
- Service Discovery
- API Gateways
- Exercise: Breaking Down a Monolith

### 2.3 Message Queues and Event-Driven Design
- Synchronous vs. Asynchronous Processing
- Kafka, RabbitMQ, and SQS
- Event Sourcing
- CQRS Pattern
- Exercise: Implementing an Event-Driven Workflow

### 2.4 Caching Strategies
- Cache Levels (Client, CDN, Application, Database)
- Cache Invalidation Strategies
- Redis and Memcached
- Cache-Aside, Write-Through, and Write-Behind Patterns
- Exercise: Implementing a Multi-level Caching System

### 2.5 Load Balancing and Service Discovery
- Load Balancing Algorithms
- Health Checks and Circuit Breakers
- Service Mesh Concepts
- DNS-based Service Discovery
- Exercise: Implementing a Simple Service Mesh

## 03. Data Systems (Weeks 5-6)

### 3.1 Database Selection and Design
- OLTP vs. OLAP
- Polyglot Persistence
- Data Access Patterns
- Connection Pooling
- Exercise: Designing a Multi-database Architecture

### 3.2 SQL vs. NoSQL Deep Dive
- Indexing Strategies
- Query Optimization
- Transaction Isolation Levels
- NoSQL Data Modeling
- Exercise: Optimizing Database Queries

### 3.3 Data Partitioning and Sharding
- Horizontal vs. Vertical Partitioning
- Sharding Strategies
- Consistent Hashing
- Rebalancing Challenges
- Exercise: Implementing a Sharded Database

### 3.4 Replication Strategies
- Master-Slave Replication
- Multi-Master Replication
- Read Replicas
- Replication Lag Management
- Exercise: Setting Up Database Replication

### 3.5 Consistency Models and CAP Theorem
- Strong vs. Eventual Consistency
- ACID vs. BASE
- Quorum-based Systems
- Conflict Resolution
- Exercise: Implementing a Distributed Counter

## 04. ML Systems Design (Weeks 7-8)

### 4.1 ML System Architecture Patterns
- Training vs. Inference Architectures
- Batch vs. Real-time Prediction
- Model Deployment Strategies
- A/B Testing Infrastructure
- Exercise: Designing an ML System Architecture

### 4.2 Feature Stores and Data Pipelines
- Feature Engineering at Scale
- Online vs. Offline Features
- Data Validation and Quality
- Feature Store Architecture
- Exercise: Building a Simple Feature Store

### 4.3 Model Serving Infrastructure
- Model Serialization
- Serving Frameworks (TensorFlow Serving, TorchServe)
- Prediction Caching
- Batch Prediction Systems
- Exercise: Deploying a Model Serving API

### 4.4 Online Learning Systems
- Stream Processing for ML
- Incremental Learning Algorithms
- Concept Drift Detection
- Model Updating Strategies
- Exercise: Implementing an Online Learning Pipeline

### 4.5 ML Monitoring and Observability
- Model Performance Metrics
- Data Drift Detection
- Explainability Tools
- ML-specific Logging
- Exercise: Building an ML Monitoring Dashboard

## 05. Advanced Topics (Weeks 9-10)

### 5.1 Consensus Algorithms
- Paxos and Raft
- Leader Election
- Byzantine Fault Tolerance
- Distributed Locks
- Exercise: Implementing a Distributed Lock

### 5.2 Distributed Transactions
- Two-Phase Commit
- Saga Pattern
- Outbox Pattern
- Idempotency in Distributed Systems
- Exercise: Implementing the Saga Pattern

### 5.3 Rate Limiting and Throttling
- Token Bucket Algorithm
- Leaky Bucket Algorithm
- Distributed Rate Limiting
- Backpressure Mechanisms
- Exercise: Implementing a Distributed Rate Limiter

### 5.4 Authorization and Authentication at Scale
- OAuth and OpenID Connect
- JWT and Token Management
- Role-Based Access Control
- Attribute-Based Access Control
- Exercise: Designing a Scalable Auth System

### 5.5 Global Distribution and Edge Computing
- Content Delivery Networks
- Geo-routing and Anycast
- Edge Computing Patterns
- Global Consistency Challenges
- Exercise: Designing a Globally Distributed Application

## 06. Case Studies (Weeks 11-12)

### 6.1 Social Network Feed System
- Data Modeling for Social Graphs
- Feed Generation Algorithms
- Real-time Updates
- Content Delivery Optimization
- Exercise: Designing a Twitter-like Feed

### 6.2 E-commerce Platform
- Inventory Management
- Order Processing Pipeline
- Payment Systems Integration
- Recommendation Engines
- Exercise: Designing an E-commerce System

### 6.3 Video Streaming Service
- Content Delivery and Caching
- Adaptive Bitrate Streaming
- Recommendation Systems
- Analytics Pipeline
- Exercise: Designing a Video Streaming Platform

### 6.4 Ride-sharing Application
- Geospatial Indexing
- Real-time Matching Algorithms
- Dynamic Pricing
- ETA Prediction
- Exercise: Designing a Ride-sharing System

### 6.5 Large-scale ML Platform
- Training Infrastructure
- Experiment Tracking
- Model Registry
- Feature Management
- Exercise: Designing an ML Platform

## Interview Preparation

Each module includes:
- System Design Interview Questions
- Common Pitfalls and How to Avoid Them
- Communication Strategies
- Whiteboarding Techniques
- Mock Interview Scenarios
</file>

<file path="advanced_system_design_guide/getting_started.md">
# Getting Started with the Advanced System Design Guide

Welcome to your journey toward mastering system design! This guide is designed to help you develop the skills needed for staff/principal/architect-level roles and interviews, with a special focus on the intersection of backend engineering and machine learning.

## Prerequisites

To get the most out of this guide, you should have:

1. **Programming Experience**: Proficiency in at least one backend language (Python, Java, Go, etc.)
2. **Basic System Knowledge**: Understanding of basic web architecture, APIs, and databases
3. **Some ML Experience**: Familiarity with basic ML concepts and workflows
4. **Problem-Solving Mindset**: Willingness to tackle complex problems and explore trade-offs

## Learning Approach

This guide follows a systematic approach:

1. **Learn the Theory**: Each topic starts with core concepts and principles
2. **Study the Examples**: Analyze real-world examples and code snippets
3. **Practice with Exercises**: Implement the concepts in coding exercises
4. **Apply to Interview Problems**: Practice with interview-style questions
5. **Reflect and Review**: Consolidate your learning before moving on

## Daily Learning Routine

For optimal progress, we recommend:

1. **Consistent Schedule**: Set aside 1-2 hours daily
2. **Active Learning**: Don't just read—implement and experiment
3. **Incremental Progress**: Complete one section before moving to the next
4. **Review and Reflection**: Periodically review previous topics
5. **Real-world Application**: Relate concepts to systems you've worked with

## Tools and Environment Setup

To complete the exercises in this guide, you'll need:

1. **Development Environment**: Your preferred IDE/editor
2. **Docker**: For containerized applications and services
3. **Git**: For version control
4. **Database Systems**: Local installations of PostgreSQL, MongoDB, Redis
5. **Cloud Account (Optional)**: AWS/GCP/Azure free tier for some advanced exercises

## How to Navigate This Guide

1. Start with the foundations in `01_Foundations`
2. Follow the curriculum order in `curriculum.md`
3. Each topic folder contains:
   - `README.md`: Topic overview
   - `theory.md`: Detailed explanation of concepts
   - `examples/`: Code examples and snippets
   - `exercises/`: Hands-on coding exercises
   - `interview_questions.md`: Practice problems

## First Steps

1. Review the complete curriculum in `curriculum.md`
2. Begin with `01_Foundations/1.1_System_Design_First_Principles`
3. Complete the exercises before moving to the next topic
4. Use the interview questions to test your understanding

## Tracking Your Progress

Consider keeping a learning journal to:
- Track concepts you've mastered
- Note questions or areas of confusion
- Record insights and connections between topics
- Document your solutions to exercises and interview questions

## Let's Begin!

Your first module is `01_Foundations/1.1_System_Design_First_Principles`. This will establish the core principles that guide all system design decisions.

Happy learning!
</file>

<file path="advanced_system_design_guide/README.md">
# Advanced System Design Guide

## A Comprehensive Learning Path for Backend and ML Engineers

Welcome to the Advanced System Design Guide, a systematic, incremental, and engaging curriculum designed to take you from foundational concepts to advanced system design principles. This guide is specifically tailored for experienced engineers looking to prepare for staff/principal/architect-level interviews and roles, with a special focus on the intersection of backend engineering and machine learning.

## Purpose

This guide aims to:

1. Provide a structured learning path for system design mastery
2. Bridge the gap between traditional backend systems and ML infrastructure
3. Offer practical, hands-on examples with real-world applications
4. Prepare you for technical interviews at top-tier companies
5. Develop the architectural thinking required for staff+ engineering roles

## How to Use This Guide

This curriculum is designed to be worked through sequentially, with each module building upon the previous ones. However, if you're already familiar with certain topics, feel free to jump to specific sections.

For optimal learning:

1. **Daily Practice**: Spend 1-2 hours daily on the materials
2. **Hands-on Implementation**: Complete all coding exercises and projects
3. **Reflection**: After each module, reflect on how the concepts apply to systems you've worked with
4. **Interview Preparation**: Use the included interview questions and system design exercises to practice

## Learning Path

The guide is organized into several major sections that progress from foundational concepts to advanced topics. Each section contains theoretical knowledge, practical examples, coding exercises, and interview preparation materials.

### 1. Foundations (Weeks 1-2)
- System Design First Principles
- Scalability Fundamentals
- Performance Engineering
- Distributed Systems Basics
- Data Modeling and Storage Paradigms

### 2. Core Components (Weeks 3-4)
- API Design and Patterns
- Microservices Architecture
- Message Queues and Event-Driven Design
- Caching Strategies
- Load Balancing and Service Discovery

### 3. Data Systems (Weeks 5-6)
- Database Selection and Design
- SQL vs. NoSQL Deep Dive
- Data Partitioning and Sharding
- Replication Strategies
- Consistency Models and CAP Theorem

### 4. ML Systems Design (Weeks 7-8)
- ML System Architecture Patterns
- Feature Stores and Data Pipelines
- Model Serving Infrastructure
- Online Learning Systems
- ML Monitoring and Observability

### 5. Advanced Topics (Weeks 9-10)
- Consensus Algorithms
- Distributed Transactions
- Rate Limiting and Throttling
- Authorization and Authentication at Scale
- Global Distribution and Edge Computing

### 6. Case Studies (Weeks 11-12)
- Social Network Feed System
- E-commerce Platform
- Video Streaming Service
- Ride-sharing Application
- Large-scale ML Platform

See the `curriculum.md` file for a detailed breakdown of all modules and topics.

## Structure of Each Module

Each module follows a consistent structure:

1. **README.md**: Overview of the topic and learning objectives
2. **theory.md**: Detailed explanation of concepts with examples
3. **examples/**: Code examples demonstrating the concepts
4. **exercises/**: Hands-on exercises to apply what you've learned
5. **interview_questions.md**: Common interview questions and how to approach them

## Getting Started

1. Begin with the `getting_started.md` file
2. Follow the recommended learning path in `curriculum.md`
3. Complete each module before moving to the next
4. Implement the coding exercises in the `code` directories
5. Practice with the interview questions in each module

## Melting Pot of Software Engineering and AI

This guide is uniquely designed to bridge traditional backend system design with modern ML engineering practices. You'll learn:

- How to design systems that effectively integrate ML components
- The unique challenges of ML systems (training, inference, monitoring)
- Patterns for feature engineering and management at scale
- Techniques for deploying and serving ML models efficiently
- Approaches for building systems that can learn and adapt over time

## LeetCode-Style Questions

Throughout the guide, you'll find algorithmic challenges that reinforce system design principles. These questions are carefully selected to demonstrate how fundamental algorithms and data structures support larger system design decisions.

## Daily Learning Plan

This guide is designed for daily engagement over several months:

1. **Week 1-2**: Foundations - Learn the core principles that guide all system design
2. **Week 3-4**: Core Components - Master the building blocks of modern systems
3. **Week 5-6**: Data Systems - Understand how to store, retrieve, and process data at scale
4. **Week 7-8**: ML Systems - Learn the unique aspects of ML system design
5. **Week 9-10**: Advanced Topics - Dive deep into complex distributed systems concepts
6. **Week 11-12**: Case Studies - Apply your knowledge to real-world system designs

## Contributing

This is a living document that will evolve over time. If you have suggestions for improvements or additional topics, please feel free to contribute.

## License

This educational content is provided for personal use and interview preparation.
</file>

<file path="system_design_guide/01_Foundation/04_Performance_Optimization/00_Overview.md">
# Performance Optimization: Overview

## Introduction

Performance optimization is the process of improving a system's efficiency, responsiveness, and resource utilization. It's a critical aspect of system design that directly impacts user experience, operational costs, and system scalability.

This module explores key performance optimization concepts, techniques, and best practices across different layers of a system.

## Why Performance Optimization Matters

Performance optimization is essential for several reasons:

1. **User Experience**: Faster systems lead to better user satisfaction and engagement. Research shows that even small delays (100-400ms) can negatively impact user experience.

2. **Cost Efficiency**: Optimized systems require fewer resources, reducing infrastructure costs and energy consumption.

3. **Scalability**: Well-optimized systems can handle more load with the same resources, making scaling more cost-effective.

4. **Competitive Advantage**: Better performance can be a key differentiator in competitive markets.

5. **Mobile and Low-Bandwidth Users**: Performance optimizations are especially important for users on mobile devices or with limited connectivity.

## Performance Optimization Areas

This module is divided into five key areas of performance optimization:

### 1. [Performance Metrics and Measurement](01_Performance_Metrics.md)

Understanding what to measure and how to measure it is the foundation of performance optimization:

- Key performance indicators (KPIs)
- Latency, throughput, and utilization metrics
- Percentiles and distribution analysis
- Benchmarking methodologies
- Monitoring and observability

### 2. [Profiling and Bottleneck Identification](02_Profiling_and_Bottlenecks.md)

Before optimizing, you need to identify what's causing performance issues:

- Profiling techniques and tools
- CPU, memory, disk, and network profiling
- Identifying bottlenecks in distributed systems
- Common performance anti-patterns
- Root cause analysis

### 3. [Algorithmic Optimization](03_Algorithmic_Optimization.md)

Improving the efficiency of algorithms and data structures:

- Computational complexity analysis
- Memory vs. CPU tradeoffs
- Data structure selection
- Algorithm optimization techniques
- Parallelization and concurrency

### 4. [Database Optimization](04_Database_Optimization.md)

Databases are often the bottleneck in many applications:

- Query optimization
- Indexing strategies
- Schema design for performance
- Database caching
- Connection pooling
- Read/write splitting

### 5. [Network Optimization](05_Network_Optimization.md)

Reducing network overhead and latency:

- Protocol optimization
- Compression techniques
- Caching and CDNs
- Connection management
- Batching and multiplexing

## Performance Optimization Process

Effective performance optimization follows a systematic process:

1. **Establish Baselines**: Measure current performance to establish a baseline.

2. **Set Goals**: Define clear, measurable performance objectives.

3. **Identify Bottlenecks**: Use profiling and monitoring to find the most significant constraints.

4. **Optimize Strategically**: Focus on the highest-impact areas first (follow the 80/20 rule).

5. **Measure Impact**: Quantify the improvement from each optimization.

6. **Iterate**: Continue the process, focusing on the next bottleneck.

7. **Monitor Continuously**: Implement ongoing monitoring to catch performance regressions.

## Key Principles

Throughout this module, keep these principles in mind:

1. **Measure, Don't Guess**: Always base optimization decisions on data, not assumptions.

2. **Optimize Where It Matters**: Focus on the critical path and high-impact areas.

3. **Consider Tradeoffs**: Performance optimizations often involve tradeoffs with other factors like code readability, maintainability, or development time.

4. **Premature Optimization Is Costly**: Don't optimize before you have evidence of a performance issue.

5. **Test Under Realistic Conditions**: Benchmark with realistic data volumes and usage patterns.

## Practical Implementation

This module includes a practical implementation that demonstrates key performance optimization techniques:

- [Optimized API Service](code/optimized_api/README.md): A sample API service that incorporates various performance optimizations.

## Next Steps

Start by exploring the [Performance Metrics and Measurement](01_Performance_Metrics.md) section to understand how to effectively measure and analyze system performance.
</file>

<file path="system_design_guide/01_Foundation/04_Performance_Optimization/01_Performance_Metrics.md">
# Performance Metrics and Measurement

## Introduction

Effective performance optimization begins with proper measurement. As the saying goes, "You can't improve what you don't measure." This section explores the key metrics for evaluating system performance and the methodologies for collecting and analyzing these metrics.

## Key Performance Metrics

### 1. Latency

**Definition**: The time it takes to complete a single operation or request.

**Key Aspects**:
- **Response Time**: Time from request initiation to response completion
- **Processing Time**: Time spent actually processing the request (excluding network and queue time)
- **Wait Time**: Time spent waiting (in queues, for resources, etc.)

**Measurement Units**: Milliseconds (ms) or microseconds (μs)

**Example**:
```
API Request Latency:
- P50 (median): 45ms
- P95: 120ms
- P99: 250ms
```

### 2. Throughput

**Definition**: The number of operations or requests a system can handle per unit of time.

**Key Aspects**:
- **Requests per Second (RPS)**: Number of requests processed per second
- **Transactions per Second (TPS)**: Number of transactions completed per second
- **Queries per Second (QPS)**: Number of database queries executed per second

**Measurement Units**: Operations per second, requests per minute, etc.

**Example**:
```
Database Throughput:
- 5,000 queries per second
- 500 writes per second
```

### 3. Resource Utilization

**Definition**: The percentage of available resources being used.

**Key Resources**:
- **CPU**: Processing power utilization
- **Memory**: RAM usage
- **Disk**: Storage I/O and capacity
- **Network**: Bandwidth consumption

**Measurement Units**: Percentage, bytes/second, operations/second

**Example**:
```
Server Utilization:
- CPU: 65%
- Memory: 4.2GB / 8GB (52.5%)
- Disk I/O: 120MB/s read, 45MB/s write
- Network: 180Mbps inbound, 220Mbps outbound
```

### 4. Error Rate

**Definition**: The frequency of failed operations.

**Key Aspects**:
- **System Errors**: Failures due to system issues
- **Application Errors**: Failures due to application logic
- **Timeout Errors**: Failures due to operations taking too long

**Measurement Units**: Percentage, errors per second

**Example**:
```
Error Rates:
- Overall: 0.5% of requests
- Timeout Errors: 0.3% of requests
- 5xx Errors: 0.1% of requests
- 4xx Errors: 0.1% of requests
```

### 5. Saturation

**Definition**: How "full" your service is or how close it is to its capacity limit.

**Key Aspects**:
- **Queue Length**: Number of requests waiting to be processed
- **Thread Pool Utilization**: Percentage of worker threads in use
- **Connection Pool Saturation**: Percentage of database connections in use

**Measurement Units**: Percentage, queue length

**Example**:
```
System Saturation:
- Request Queue: 15 requests waiting
- Thread Pool: 45/50 threads in use (90%)
- Database Connections: 85/100 connections in use (85%)
```

## Statistical Analysis of Performance Data

### 1. Percentiles (Quantiles)

**Definition**: Values that divide a dataset into 100 equal parts.

**Key Percentiles**:
- **P50 (Median)**: 50% of values are below this point
- **P90**: 90% of values are below this point
- **P95**: 95% of values are below this point
- **P99**: 99% of values are below this point
- **P99.9**: 99.9% of values are below this point

**Why They Matter**: Percentiles provide insight into the distribution of performance and help identify outliers that affect user experience.

**Example**:
```python
import numpy as np

# Sample latency data (in ms)
latencies = [45, 48, 52, 42, 58, 60, 47, 55, 120, 180, 45, 46, 49, 51, 53]

# Calculate percentiles
p50 = np.percentile(latencies, 50)  # Median
p90 = np.percentile(latencies, 90)
p95 = np.percentile(latencies, 95)
p99 = np.percentile(latencies, 99)

print(f"P50 (Median): {p50}ms")
print(f"P90: {p90}ms")
print(f"P95: {p95}ms")
print(f"P99: {p99}ms")
```

### 2. Histograms

**Definition**: A representation of the distribution of data across value ranges.

**Benefits**:
- Visualize the distribution of performance metrics
- Identify multimodal distributions (multiple peaks)
- Spot outliers and anomalies

**Example**:
```python
import matplotlib.pyplot as plt

# Create histogram of latency data
plt.figure(figsize=(10, 6))
plt.hist(latencies, bins=20, alpha=0.7, color='blue')
plt.title('API Request Latency Distribution')
plt.xlabel('Latency (ms)')
plt.ylabel('Frequency')
plt.axvline(p95, color='red', linestyle='dashed', linewidth=2, label=f'P95: {p95}ms')
plt.legend()
plt.grid(True, alpha=0.3)
plt.show()
```

### 3. Moving Averages

**Definition**: Average of data points over a sliding window of time.

**Types**:
- **Simple Moving Average (SMA)**: Equal weight to all data points
- **Exponential Moving Average (EMA)**: More weight to recent data points

**Benefits**:
- Smooth out short-term fluctuations
- Highlight longer-term trends
- Reduce the impact of outliers

**Example**:
```python
import pandas as pd

# Create a time series of latency data
timestamps = pd.date_range(start='2023-01-01', periods=len(latencies), freq='1min')
latency_series = pd.Series(latencies, index=timestamps)

# Calculate moving averages
sma_5 = latency_series.rolling(window=5).mean()
ema_5 = latency_series.ewm(span=5).mean()

# Plot original data and moving averages
plt.figure(figsize=(12, 6))
plt.plot(latency_series, label='Raw Latency')
plt.plot(sma_5, label='5-point SMA', linewidth=2)
plt.plot(ema_5, label='5-point EMA', linewidth=2)
plt.title('Latency with Moving Averages')
plt.xlabel('Time')
plt.ylabel('Latency (ms)')
plt.legend()
plt.grid(True, alpha=0.3)
plt.show()
```

## Measurement Methodologies

### 1. Synthetic Monitoring (Active Monitoring)

**Definition**: Proactively testing a system by simulating user behavior.

**Approaches**:
- **Ping Tests**: Basic connectivity and response time checks
- **Synthetic Transactions**: Simulated user journeys
- **API Checks**: Regular calls to APIs to verify performance

**Benefits**:
- Proactive detection of issues
- Consistent baseline measurements
- Testing from multiple geographic locations

**Example Tool**: Pingdom, New Relic Synthetics

### 2. Real User Monitoring (RUM)

**Definition**: Capturing actual user interactions and their performance.

**Metrics Collected**:
- **Page Load Time**: Time to fully load a page
- **Time to First Byte (TTFB)**: Time until the first byte is received
- **First Contentful Paint (FCP)**: Time until the first content is displayed
- **Time to Interactive (TTI)**: Time until the page becomes interactive

**Benefits**:
- Real-world performance data
- Insight into user experience
- Geographic and device-specific insights

**Example Tool**: Google Analytics, Datadog RUM

### 3. Application Performance Monitoring (APM)

**Definition**: Monitoring the internal operations of applications.

**Key Features**:
- **Transaction Tracing**: Following requests through the system
- **Code-level Visibility**: Identifying slow methods or functions
- **Dependency Monitoring**: Tracking external service performance
- **Error Tracking**: Capturing and analyzing exceptions

**Benefits**:
- Deep visibility into application behavior
- Root cause analysis
- Performance regression detection

**Example Tool**: New Relic, Datadog APM, Dynatrace

### 4. Load Testing

**Definition**: Simulating heavy load to evaluate system performance under stress.

**Types**:
- **Stress Testing**: Testing beyond normal operational capacity
- **Spike Testing**: Sudden, large increases in load
- **Soak Testing**: Sustained load over an extended period
- **Scalability Testing**: Gradually increasing load to find limits

**Benefits**:
- Identify breaking points
- Validate scaling capabilities
- Uncover performance bottlenecks

**Example Tool**: Apache JMeter, Locust, k6

**Example JMeter Test Plan**:
```xml
<?xml version="1.0" encoding="UTF-8"?>
<jmeterTestPlan version="1.2" properties="5.0">
  <hashTree>
    <TestPlan guiclass="TestPlanGui" testclass="TestPlan" testname="API Load Test">
      <elementProp name="TestPlan.user_defined_variables" elementType="Arguments">
        <collectionProp name="Arguments.arguments"/>
      </elementProp>
    </TestPlan>
    <hashTree>
      <ThreadGroup guiclass="ThreadGroupGui" testclass="ThreadGroup" testname="Users">
        <elementProp name="ThreadGroup.main_controller" elementType="LoopController">
          <boolProp name="LoopController.continue_forever">false</boolProp>
          <intProp name="LoopController.loops">10</intProp>
        </elementProp>
        <stringProp name="ThreadGroup.num_threads">100</stringProp>
        <stringProp name="ThreadGroup.ramp_time">30</stringProp>
      </ThreadGroup>
      <hashTree>
        <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="API Request">
          <elementProp name="HTTPsampler.Arguments" elementType="Arguments">
            <collectionProp name="Arguments.arguments"/>
          </elementProp>
          <stringProp name="HTTPSampler.domain">api.example.com</stringProp>
          <stringProp name="HTTPSampler.path">/users</stringProp>
          <stringProp name="HTTPSampler.method">GET</stringProp>
        </HTTPSamplerProxy>
        <hashTree/>
      </hashTree>
    </hashTree>
  </hashTree>
</jmeterTestPlan>
```

## Benchmarking

### 1. Benchmarking Principles

**Key Principles**:
- **Reproducibility**: Tests should produce consistent results
- **Isolation**: Minimize external factors affecting results
- **Relevance**: Tests should reflect real-world usage
- **Comparability**: Results should be comparable across runs

### 2. Benchmarking Methodologies

**Common Approaches**:
- **Microbenchmarks**: Testing specific components in isolation
- **Macrobenchmarks**: Testing entire systems or workflows
- **Comparative Benchmarks**: Comparing different implementations
- **Baseline Benchmarks**: Measuring against a known standard

**Example Microbenchmark (Java with JMH)**:
```java
@BenchmarkMode(Mode.AverageTime)
@OutputTimeUnit(TimeUnit.MICROSECONDS)
@Warmup(iterations = 5, time = 1, timeUnit = TimeUnit.SECONDS)
@Measurement(iterations = 5, time = 1, timeUnit = TimeUnit.SECONDS)
@Fork(1)
public class StringConcatenationBenchmark {

    @Benchmark
    public String stringConcatenationWithPlus() {
        String result = "";
        for (int i = 0; i < 1000; i++) {
            result += i;
        }
        return result;
    }

    @Benchmark
    public String stringConcatenationWithStringBuilder() {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < 1000; i++) {
            sb.append(i);
        }
        return sb.toString();
    }
}
```

## Monitoring and Observability

### 1. The Three Pillars of Observability

**Metrics**: Numerical data about system performance
**Logs**: Detailed records of events and actions
**Traces**: Following requests through distributed systems

### 2. Monitoring Infrastructure

**Key Components**:
- **Data Collection**: Agents, exporters, and instrumentation
- **Data Storage**: Time-series databases, log storage
- **Visualization**: Dashboards and charts
- **Alerting**: Notifications for threshold violations

**Example Stack**:
- Prometheus for metrics collection
- Grafana for visualization
- Elasticsearch for log storage
- Jaeger for distributed tracing

### 3. Effective Dashboards

**Principles**:
- **Hierarchy of Information**: Most important metrics first
- **Correlation**: Related metrics grouped together
- **Context**: Include baselines and thresholds
- **Actionability**: Information that leads to decisions

**Example Dashboard Structure**:
```
Service Dashboard
├── Summary
│   ├── Service Status
│   ├── Error Rate
│   ├── Request Volume
│   └── Latency (P50, P95, P99)
├── Resources
│   ├── CPU Usage
│   ├── Memory Usage
│   ├── Disk I/O
│   └── Network Traffic
├── Dependencies
│   ├── Database Performance
│   ├── External API Calls
│   └── Cache Hit Rate
└── Business Metrics
    ├── Conversion Rate
    ├── User Sessions
    └── Transaction Volume
```

## Performance Testing in CI/CD

### 1. Continuous Performance Testing

**Benefits**:
- Early detection of performance regressions
- Preventing performance issues in production
- Historical performance data

**Implementation**:
- Automated performance tests in CI pipeline
- Performance budgets and thresholds
- Comparison with baseline metrics

### 2. Performance Budgets

**Definition**: Quantifiable limits on performance metrics.

**Examples**:
- Page load time < 2 seconds
- API response time < 200ms
- JavaScript bundle size < 250KB

**Implementation in CI**:
```yaml
# Example GitHub Actions workflow with performance testing
name: Performance Tests

on:
  pull_request:
    branches: [ main ]

jobs:
  performance:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      
      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '16'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Build application
        run: npm run build
        
      - name: Run performance tests
        run: npm run test:performance
        
      - name: Check performance budgets
        run: |
          if grep -q "FAIL" performance-report.json; then
            echo "Performance budget exceeded!"
            exit 1
          fi
```

## Case Study: E-commerce API Performance

### Scenario
An e-commerce platform's product API is experiencing performance issues during peak traffic.

### Metrics Collection
1. **Latency**: P95 response time increased from 150ms to 450ms
2. **Throughput**: Handling 200 RPS, down from normal 500 RPS
3. **Error Rate**: 5% of requests failing with 500 errors
4. **Database Utilization**: 95% CPU, 85% memory
5. **Connection Pool**: 98% utilized (49/50 connections)

### Analysis
1. High database utilization and connection pool saturation indicate a database bottleneck
2. Latency increase correlates with database saturation
3. Error rate increases when connection pool is fully utilized

### Measurement-Driven Optimization
1. Increased connection pool size from 50 to 100
2. Added database read replicas
3. Implemented query optimization
4. Added caching for frequently accessed products

### Results
1. **Latency**: P95 response time reduced to 120ms
2. **Throughput**: Now handling 800 RPS
3. **Error Rate**: Reduced to 0.1%
4. **Database Utilization**: Reduced to 60% CPU, 50% memory
5. **Connection Pool**: 60% utilized (60/100 connections)

## Interview Questions

### Question 1: How would you approach measuring the performance of a new microservice?

**Key Points to Address**:

1. **Define Key Metrics**:
   - Latency (P50, P95, P99)
   - Throughput (requests per second)
   - Error rate
   - Resource utilization (CPU, memory, I/O)

2. **Implement Instrumentation**:
   - Add metrics collection to the service
   - Implement distributed tracing
   - Set up structured logging

3. **Establish Baselines**:
   - Run load tests to establish performance baselines
   - Document expected performance under different loads
   - Set performance budgets

4. **Monitor in Production**:
   - Set up dashboards for key metrics
   - Implement alerting for performance degradation
   - Collect real user metrics if applicable

5. **Continuous Improvement**:
   - Regular performance testing in CI/CD
   - Periodic review of performance metrics
   - Comparison against baselines and SLOs

### Question 2: What's the difference between average and percentile metrics, and why does it matter?

**Key Points**:

1. **Average (Mean)**:
   - Sum of all values divided by the number of values
   - Affected by outliers
   - Can hide performance issues affecting a subset of users

2. **Percentiles**:
   - Values below which a certain percentage of observations fall
   - P50 (median): 50% of values are below this point
   - P95: 95% of values are below this point
   - P99: 99% of values are below this point

3. **Why Percentiles Matter**:
   - Better represent user experience
   - Highlight performance issues affecting a small percentage of users
   - More resistant to outliers than averages
   - Help identify the "long tail" of performance

4. **Example Scenario**:
   - Average response time: 100ms
   - P95 response time: 500ms
   - This indicates that while most requests are fast, 5% of users experience significant delays

5. **Industry Practice**:
   - SLAs often defined in terms of percentiles
   - Focus on "high percentile" performance (P95, P99)
   - Different percentiles for different types of services

### Question 3: How would you diagnose a sudden increase in API response times?

**Key Points**:

1. **Gather Data**:
   - Check monitoring dashboards for correlated events
   - Look at system metrics (CPU, memory, disk, network)
   - Examine database performance metrics
   - Review recent code or configuration changes

2. **Isolate the Problem**:
   - Determine if all endpoints are affected or just specific ones
   - Check if all users/regions are experiencing the issue
   - Look for patterns in slow requests

3. **Check Dependencies**:
   - Examine performance of downstream services
   - Look at database query times
   - Check external API call latencies
   - Verify cache hit rates

4. **Analyze Traffic Patterns**:
   - Check for traffic spikes or changes in usage patterns
   - Look for unusual client behavior
   - Verify if the issue correlates with specific times or events

5. **Investigate Resource Constraints**:
   - Check for resource saturation (CPU, memory, connections)
   - Look for contention issues (locks, queues)
   - Verify if autoscaling is functioning properly

## Practical Exercise

### Exercise: Implement a Performance Monitoring Dashboard

Design and implement a performance monitoring dashboard for a web application that:

1. Collects key performance metrics (latency, throughput, error rate)
2. Visualizes metrics with appropriate charts
3. Calculates and displays percentile values
4. Sets up alerts for performance degradation
5. Provides historical comparison

**Suggested Tools**:
- Prometheus for metrics collection
- Grafana for visualization
- AlertManager for alerting

## AI/ML Integration

### Performance Metrics for ML Systems

1. **Inference Latency**:
   - Time to generate predictions
   - Batch vs. single-prediction performance
   - Cold start vs. warm start times

2. **Throughput**:
   - Predictions per second
   - Batch size optimization
   - GPU utilization efficiency

3. **Resource Utilization**:
   - Memory footprint
   - GPU memory usage
   - CPU/GPU utilization

4. **Model-Specific Metrics**:
   - Prediction accuracy over time
   - Feature calculation time
   - Model loading time

### Measuring ML Model Performance

**Example Monitoring Setup**:
```python
import time
import numpy as np
from prometheus_client import start_http_server, Summary, Histogram, Gauge

# Create metrics
PREDICTION_TIME = Summary('model_prediction_seconds', 'Time spent processing prediction')
PREDICTION_TIME_HIST = Histogram('model_prediction_seconds_histogram', 
                                'Histogram of prediction time in seconds', 
                                buckets=(0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1, 2.5, 5, 10))
MODEL_ACCURACY = Gauge('model_accuracy', 'Current model accuracy')

# Start up the server to expose the metrics
start_http_server(8000)

# Function to be monitored
@PREDICTION_TIME.time()
def predict(features):
    # Record the prediction time with the histogram
    start = time.time()
    
    # Actual prediction code
    result = model.predict(features)
    
    # Record the prediction time
    prediction_time = time.time() - start
    PREDICTION_TIME_HIST.observe(prediction_time)
    
    return result

# Update accuracy metric periodically
def update_accuracy(accuracy_value):
    MODEL_ACCURACY.set(accuracy_value)
```

## Next Steps

In the next section, we'll explore [Profiling and Bottleneck Identification](02_Profiling_and_Bottlenecks.md), which builds on these measurement techniques to identify specific performance issues in your system.

## Resources

1. [Site Reliability Engineering (SRE) Book](https://sre.google/sre-book/monitoring-distributed-systems/)
2. [Prometheus Documentation](https://prometheus.io/docs/introduction/overview/)
3. [Brendan Gregg's Systems Performance](http://www.brendangregg.com/systems-performance-2nd-edition-book.html)
4. [Google Web Vitals](https://web.dev/vitals/)
5. [JMeter User Manual](https://jmeter.apache.org/usermanual/index.html)
</file>

<file path="system_design_guide/01_Foundation/04_Performance_Optimization/02_Profiling_and_Bottlenecks.md">
# Profiling and Bottleneck Identification

## Introduction

Profiling is the process of analyzing a system's behavior to identify performance bottlenecks. Before you can optimize a system, you need to understand where the performance issues lie. This section covers techniques and tools for profiling applications and identifying bottlenecks across different system components.

## Understanding Bottlenecks

A bottleneck is a component or resource that limits the overall system performance. Like the narrow neck of a bottle that restricts flow, a performance bottleneck restricts the throughput of your entire system.

### Types of Bottlenecks

1. **CPU Bottlenecks**: When processing power limits performance
   - Computationally intensive operations
   - Inefficient algorithms
   - High context switching

2. **Memory Bottlenecks**: When memory access or capacity limits performance
   - Memory leaks
   - Excessive garbage collection
   - Insufficient physical memory
   - Poor cache utilization

3. **I/O Bottlenecks**: When input/output operations limit performance
   - Disk read/write operations
   - Network communication
   - Database queries

4. **Concurrency Bottlenecks**: When thread management or synchronization limits performance
   - Lock contention
   - Thread pool saturation
   - Inefficient synchronization

5. **Resource Contention**: When multiple processes compete for the same resources
   - Database connection limits
   - Thread starvation
   - Shared resource locks

## Profiling Techniques

### 1. CPU Profiling

**Purpose**: Identify code sections consuming excessive CPU time.

**What to Look For**:
- Hot methods (methods consuming significant CPU time)
- Call frequencies
- Time spent in specific functions
- CPU utilization patterns

**Common Tools**:
- Java: VisualVM, JProfiler, async-profiler
- Python: cProfile, py-spy
- Node.js: clinic.js, v8-profiler
- Go: pprof

**Example (Python):**
```python
import cProfile
import pstats
from io import StringIO

def cpu_intensive_function():
    result = 0
    for i in range(10000000):
        result += i
    return result

# Profile the function
profiler = cProfile.Profile()
profiler.enable()
cpu_intensive_function()
profiler.disable()

# Print sorted stats
s = StringIO()
ps = pstats.Stats(profiler, stream=s).sort_stats('cumulative')
ps.print_stats(10)  # Print top 10 functions
print(s.getvalue())
```

### 2. Memory Profiling

**Purpose**: Identify memory usage patterns and potential memory leaks.

**What to Look For**:
- Memory allocation patterns
- Object retention
- Garbage collection frequency and duration
- Memory growth over time

**Common Tools**:
- Java: VisualVM, JProfiler, Eclipse Memory Analyzer (MAT)
- Python: memory_profiler, objgraph
- Node.js: heapdump, memwatch
- Go: pprof

**Example (Python):**
```python
from memory_profiler import profile

@profile
def memory_intensive_function():
    # Create a large list
    large_list = [i for i in range(10000000)]
    # Do something with the list
    sum_value = sum(large_list)
    return sum_value

# Run the function
memory_intensive_function()
```

### 3. I/O Profiling

**Purpose**: Identify bottlenecks in disk or network operations.

**What to Look For**:
- Frequency of I/O operations
- Time spent in I/O
- I/O operation sizes
- I/O wait times

**Common Tools**:
- iostat, iotop (Linux)
- DTrace (macOS, Solaris)
- Windows Performance Monitor
- Application-specific database profilers

**Example (Linux Shell):**
```bash
# Monitor disk I/O statistics every 2 seconds
iostat -xz 2

# Monitor I/O usage by process
iotop -o

# Trace file system calls for a specific process
strace -f -e trace=file -p <PID>
```

### 4. Network Profiling

**Purpose**: Identify network-related bottlenecks.

**What to Look For**:
- Network latency
- Packet loss
- Connection establishment time
- Data transfer rates
- Protocol overhead

**Common Tools**:
- Wireshark
- tcpdump
- netstat
- Application-level network profilers

**Example (Linux Shell):**
```bash
# Capture network traffic on port 80
sudo tcpdump -i any port 80 -w capture.pcap

# Display active network connections
netstat -tunapl

# Monitor network traffic by process
nethogs
```

### 5. Database Profiling

**Purpose**: Identify slow queries and database performance issues.

**What to Look For**:
- Slow queries
- Query execution plans
- Index usage
- Lock contention
- Connection patterns

**Common Tools**:
- MySQL: EXPLAIN, slow query log, Performance Schema
- PostgreSQL: EXPLAIN ANALYZE, pg_stat_statements
- MongoDB: Database Profiler, explain()
- Application-level ORM profilers

**Example (SQL):**
```sql
-- Analyze query execution plan in PostgreSQL
EXPLAIN ANALYZE 
SELECT * FROM users 
JOIN orders ON users.id = orders.user_id 
WHERE users.status = 'active' 
ORDER BY orders.created_at DESC 
LIMIT 100;

-- Find slow queries in MySQL
SELECT * FROM mysql.slow_log 
WHERE query_time > 1 
ORDER BY query_time DESC 
LIMIT 10;
```

## Profiling in Different Environments

### 1. Development Environment

**Approach**:
- Detailed, intrusive profiling
- Focus on specific components
- Reproduce issues in isolation
- Use heavyweight profiling tools

**Example Workflow**:
1. Identify suspicious code areas
2. Apply targeted profiling
3. Analyze results in development tools
4. Test optimizations immediately

### 2. Staging/Test Environment

**Approach**:
- System-wide profiling under realistic load
- Performance testing with production-like data
- Comparison with baselines
- Less intrusive profiling

**Example Workflow**:
1. Deploy application with profiling enabled
2. Run load tests to simulate production traffic
3. Collect and analyze profiling data
4. Verify optimizations under realistic conditions

### 3. Production Environment

**Approach**:
- Minimal-overhead profiling
- Sampling-based approaches
- Targeted, time-limited profiling sessions
- Focus on real user impact

**Example Workflow**:
1. Enable lightweight profiling during off-peak hours
2. Collect samples from problematic instances
3. Analyze data offline
4. Deploy and monitor optimizations carefully

**Example (Java Production Profiling):**
```bash
# Attach async-profiler to a running Java process with minimal overhead
./profiler.sh -d 30 -f profile.html <PID>
```

## Bottleneck Identification Strategies

### 1. Top-Down Approach

**Process**:
1. Start with high-level system metrics
2. Identify components with highest utilization or latency
3. Drill down into specific components
4. Pinpoint specific code or resource issues

**Example**:
- Notice high overall API latency
- Identify database as the slowest component
- Find specific slow queries
- Analyze query execution plans
- Identify missing indexes

### 2. Bottom-Up Approach

**Process**:
1. Profile individual components
2. Identify inefficiencies in each component
3. Assess impact on overall system performance
4. Prioritize optimizations based on impact

**Example**:
- Profile each microservice independently
- Identify inefficient algorithms in service A
- Discover connection pool issues in service B
- Calculate impact of each issue on end-to-end performance
- Prioritize fixes based on overall impact

### 3. Critical Path Analysis

**Process**:
1. Identify the sequence of operations that determine overall response time
2. Measure time spent in each operation on the critical path
3. Focus optimization efforts on the slowest operations

**Example**:
```
Request Timeline:
[Auth: 15ms] → [Business Logic: 45ms] → [Database Query: 200ms] → [Response Generation: 30ms]

Critical Path Analysis:
- Total Time: 290ms
- Database Query: 69% of total time (primary bottleneck)
- Business Logic: 16% of total time
- Response Generation: 10% of total time
- Auth: 5% of total time
```

### 4. Resource Saturation Analysis

**Process**:
1. Monitor utilization of all system resources
2. Identify resources approaching 100% utilization
3. Analyze the impact of resource saturation on performance
4. Address the most constrained resources first

**Example**:
```
Resource Utilization:
- CPU: 45%
- Memory: 60%
- Disk I/O: 95%
- Network: 30%
- Database Connections: 98%

Analysis:
- Database connection pool is saturated (primary bottleneck)
- Disk I/O is near saturation (secondary bottleneck)
- Other resources have adequate capacity
```

## Common Bottlenecks and Detection Patterns

### 1. N+1 Query Problem

**Description**: Making N additional queries to fetch related data for N results from an initial query.

**Detection Signs**:
- High number of similar database queries
- Database queries increasing linearly with result size
- ORM logs showing repeated queries

**Example**:
```java
// Inefficient code with N+1 problem
List<Order> orders = orderRepository.findAll();  // 1 query
for (Order order : orders) {
    // This generates N additional queries, one per order
    List<OrderItem> items = orderItemRepository.findByOrderId(order.getId());
    order.setItems(items);
}

// Efficient code avoiding N+1 problem
List<Order> orders = orderRepository.findAllWithItems();  // 1 query that joins orders and items
```

### 2. Memory Leaks

**Description**: Memory that is no longer needed is not released, causing gradual memory growth.

**Detection Signs**:
- Increasing memory usage over time
- Garbage collection becoming more frequent and taking longer
- OutOfMemoryError exceptions
- Specific object counts growing continuously

**Example (Java):**
```java
public class LeakyClass {
    // Static collection that keeps growing
    private static final List<Object> leakyList = new ArrayList<>();
    
    public void processData(Object data) {
        // Process the data
        // ...
        
        // Add to the static list but never remove
        leakyList.add(data);
    }
}
```

### 3. Connection Pool Exhaustion

**Description**: All available connections in a pool are in use, causing new requests to wait.

**Detection Signs**:
- Increasing wait times for connections
- Timeout exceptions
- Thread blocks waiting for connections
- Connection pool metrics showing high utilization

**Example (Connection Pool Metrics):**
```
Connection Pool Stats:
- Max Pool Size: 20
- Active Connections: 20
- Idle Connections: 0
- Waiting Threads: 15
- Avg Wait Time: 500ms
```

### 4. Lock Contention

**Description**: Multiple threads competing for the same locks, causing serialization of operations.

**Detection Signs**:
- High thread wait times
- CPU not fully utilized despite high load
- Increasing latency under concurrent load
- Thread dumps showing many threads in BLOCKED state

**Example (Thread Dump Analysis):**
```
"Thread-1" #12 prio=5 os_prio=0 tid=0x00007f6c0c9b7000 nid=0x6b5d waiting for monitor entry [0x00007f6c12345000]
   java.lang.Thread.State: BLOCKED (on object monitor)
    at com.example.SharedResource.updateValue(SharedResource.java:25)
    - waiting to lock <0x00000007d58ff1e8> (a com.example.SharedResource)
    
"Thread-2" #13 prio=5 os_prio=0 tid=0x00007f6c0c9c8000 nid=0x6b5e waiting for monitor entry [0x00007f6c12346000]
   java.lang.Thread.State: BLOCKED (on object monitor)
    at com.example.SharedResource.updateValue(SharedResource.java:25)
    - waiting to lock <0x00000007d58ff1e8> (a com.example.SharedResource)
```

### 5. Inefficient Algorithms

**Description**: Using algorithms with poor time or space complexity for the given problem.

**Detection Signs**:
- Processing time growing non-linearly with input size
- CPU usage spikes during specific operations
- Methods with high CPU time in profiler
- Memory usage growing rapidly with input size

**Example (Complexity Analysis):**
```java
// Inefficient algorithm - O(n²) time complexity
public boolean containsDuplicate(int[] nums) {
    for (int i = 0; i < nums.length; i++) {
        for (int j = i + 1; j < nums.length; j++) {
            if (nums[i] == nums[j]) {
                return true;
            }
        }
    }
    return false;
}

// Efficient algorithm - O(n) time complexity
public boolean containsDuplicate(int[] nums) {
    Set<Integer> seen = new HashSet<>();
    for (int num : nums) {
        if (seen.contains(num)) {
            return true;
        }
        seen.add(num);
    }
    return false;
}
```

## Profiling Tools Deep Dive

### 1. Java Profiling with Async-Profiler

**Key Features**:
- Low overhead sampling profiler
- CPU, allocation, and lock profiling
- Flame graph visualization
- Can attach to running JVMs

**Example Usage**:
```bash
# CPU profiling
./profiler.sh -d 30 -f cpu-profile.html <PID>

# Memory allocation profiling
./profiler.sh -e alloc -d 30 -f alloc-profile.html <PID>

# Lock contention profiling
./profiler.sh -e lock -d 30 -f lock-profile.html <PID>
```

**Flame Graph Interpretation**:
- X-axis: Stack population (not time)
- Y-axis: Stack depth
- Each rectangle: Function in the call stack
- Width: Proportion of samples
- Colors: Can represent different threads or states

### 2. Python Profiling with py-spy

**Key Features**:
- Sampling profiler that doesn't require code changes
- Can profile running Python processes
- Low overhead
- Supports flame graphs

**Example Usage**:
```bash
# Record CPU profile for 30 seconds
py-spy record -o profile.svg --duration 30 -p <PID>

# Live top-like view of Python process
py-spy top -p <PID>

# Dump current call stacks
py-spy dump -p <PID>
```

### 3. Database Profiling with Postgres

**Key Features**:
- EXPLAIN ANALYZE for query execution plans
- pg_stat_statements for query statistics
- auto_explain for logging execution plans
- pg_stat_activity for current activity

**Example Usage**:
```sql
-- Enable query statistics collection
CREATE EXTENSION pg_stat_statements;

-- Analyze a specific query
EXPLAIN ANALYZE 
SELECT * FROM users 
WHERE last_login > current_date - interval '7 days' 
ORDER BY last_login DESC;

-- Find the slowest queries
SELECT query, calls, total_time, mean_time, rows
FROM pg_stat_statements
ORDER BY mean_time DESC
LIMIT 10;

-- Check for active queries and their state
SELECT pid, age(clock_timestamp(), query_start), usename, query
FROM pg_stat_activity
WHERE state != 'idle'
ORDER BY query_start;
```

## Case Study: Web Application Performance Investigation

### Scenario
A web application is experiencing increasing response times during peak hours. Users report slow page loads and occasional timeouts.

### Initial Metrics
- Average response time: 2.5 seconds (up from 800ms)
- 95th percentile response time: 5.8 seconds
- Error rate: 3% (up from 0.5%)
- CPU utilization: 65%
- Memory utilization: 80%

### Profiling Approach

**Step 1: System-Wide Profiling**
- Collected system metrics during peak hours
- Identified high database CPU usage (90%)
- Network and application server metrics were normal

**Step 2: Database Profiling**
- Enabled slow query logging
- Found several queries taking >500ms
- Identified a specific query pattern in the product search feature

**Step 3: Application Profiling**
- Used APM tool to trace requests
- Discovered N+1 query pattern in product listing
- Found excessive object creation in search result processing

**Step 4: Code-Level Profiling**
- Profiled the search service specifically
- Identified inefficient string manipulation in search query building
- Found unnecessary data being loaded for each product

### Root Causes Identified
1. N+1 query pattern loading product attributes
2. Missing index on product search fields
3. Inefficient string concatenation in search query builder
4. Excessive object creation for temporary results

### Optimizations Applied
1. Replaced N+1 queries with a single join query
2. Added appropriate indexes to product search fields
3. Refactored query builder to use StringBuilder
4. Implemented object pooling for temporary results

### Results
- Average response time: 600ms (76% improvement)
- 95th percentile response time: 1.2 seconds (79% improvement)
- Error rate: 0.2% (93% improvement)
- Database CPU utilization: 40% (56% reduction)

## Interview Questions

### Question 1: How would you identify the root cause of a performance issue in a production system?

**Key Points to Address**:

1. **Systematic Approach**:
   - Start with high-level metrics to identify affected components
   - Use monitoring data to narrow down the timeframe and conditions
   - Apply targeted profiling to suspected components
   - Analyze logs and traces for patterns

2. **Tools and Techniques**:
   - Use APM tools for distributed tracing
   - Analyze metrics dashboards for correlations
   - Apply lightweight production profiling
   - Examine database query performance
   - Review recent changes that might have caused regression

3. **Bottleneck Identification**:
   - Look for resource saturation (CPU, memory, I/O)
   - Check for contention points (locks, connection pools)
   - Analyze critical path to find the slowest components
   - Examine scaling patterns as load increases

4. **Validation**:
   - Reproduce the issue in a controlled environment
   - Test hypotheses with targeted experiments
   - Verify findings with additional data sources
   - Implement potential fixes and measure impact

### Question 2: What are the challenges of profiling in production, and how would you address them?

**Key Points**:

1. **Performance Overhead**:
   - Challenge: Profiling can impact production performance
   - Solution: Use sampling-based profilers with configurable overhead
   - Solution: Profile subset of requests or servers
   - Solution: Schedule intensive profiling during off-peak hours

2. **Data Volume**:
   - Challenge: Production generates massive amounts of profiling data
   - Solution: Use filtering and aggregation
   - Solution: Focus on specific time windows or conditions
   - Solution: Implement adaptive sampling based on anomalies

3. **Privacy and Security**:
   - Challenge: Profiling may expose sensitive data
   - Solution: Anonymize or redact sensitive information
   - Solution: Implement access controls for profiling data
   - Solution: Ensure compliance with data protection regulations

4. **Minimal Disruption**:
   - Challenge: Cannot restart or modify running services easily
   - Solution: Use tools that can attach to running processes
   - Solution: Implement feature flags for profiling capabilities
   - Solution: Design for zero-downtime profiling enablement

5. **Correlation with User Experience**:
   - Challenge: Connecting profiling data to actual user impact
   - Solution: Combine profiling with real user monitoring
   - Solution: Trace requests end-to-end
   - Solution: Correlate profiling data with business metrics

### Question 3: Explain how you would diagnose and fix a memory leak in a long-running application.

**Key Points**:

1. **Detection**:
   - Monitor memory usage patterns over time
   - Look for steadily increasing memory usage that doesn't plateau
   - Check garbage collection metrics for increasing frequency and duration
   - Use memory profilers to take heap snapshots at intervals

2. **Analysis**:
   - Compare heap snapshots to identify growing object collections
   - Analyze object retention paths to find what's preventing garbage collection
   - Look for classes with unexpectedly high instance counts
   - Examine reference patterns (especially static collections)

3. **Common Causes**:
   - Unclosed resources (file handles, connections, streams)
   - Objects added to collections but never removed
   - Event listeners not being unregistered
   - Caches without size limits or expiration policies
   - Thread-local variables in long-lived threads

4. **Resolution Strategies**:
   - Fix resource leaks with proper closing in finally blocks or try-with-resources
   - Implement weak references for caches and listeners
   - Add size limits and expiration policies to caches
   - Use memory-bounded collections
   - Implement proper cleanup in object lifecycle methods

5. **Verification**:
   - Run the application with the fix under load
   - Monitor memory usage over an extended period
   - Verify garbage collection patterns return to normal
   - Perform additional heap analysis to ensure the leak is resolved

## Practical Exercise

### Exercise: Profile and Optimize a Slow API Endpoint

**Scenario**: An API endpoint that retrieves and processes user activity data is experiencing high latency. Your task is to profile the endpoint, identify bottlenecks, and optimize its performance.

**Requirements**:
1. Use appropriate profiling tools to identify performance bottlenecks
2. Document your findings and the evidence supporting them
3. Implement optimizations to address the identified bottlenecks
4. Measure and report the performance improvement

**Suggested Approach**:
1. Establish baseline performance metrics
2. Apply CPU and memory profiling to identify hot spots
3. Use database profiling to analyze query performance
4. Implement optimizations based on findings
5. Measure the impact of each optimization

## AI/ML Integration

### Profiling ML Model Training and Inference

1. **Training Performance Profiling**:
   - GPU utilization and memory usage
   - Data loading and preprocessing bottlenecks
   - Batch size optimization
   - Gradient computation and backpropagation time

2. **Inference Performance Profiling**:
   - Model loading time
   - Feature extraction and preprocessing overhead
   - Inference latency breakdown by layer
   - Memory consumption during inference

**Example PyTorch Profiling**:
```python
import torch
from torch.profiler import profile, record_function, ProfilerActivity

model = MyNeuralNetwork().cuda()
inputs = torch.randn(32, 3, 224, 224).cuda()

with profile(activities=[ProfilerActivity.CPU, ProfilerActivity.CUDA],
             record_shapes=True,
             profile_memory=True) as prof:
    with record_function("model_inference"):
        model(inputs)

print(prof.key_averages().table(sort_by="cuda_time_total", row_limit=10))
```

### Common ML Bottlenecks

1. **Data Loading Bottlenecks**:
   - Slow disk I/O
   - Inefficient data formats
   - Insufficient prefetching
   - CPU-bound preprocessing

2. **Model Architecture Bottlenecks**:
   - Excessive parameter count
   - Inefficient layer configurations
   - Unoptimized activation functions
   - Memory-intensive operations

3. **Hardware Utilization Bottlenecks**:
   - Poor GPU utilization
   - CPU-GPU transfer overhead
   - Memory bandwidth limitations
   - Inefficient kernel implementations

**Example TensorFlow Profiling**:
```python
import tensorflow as tf

# Load the model
model = tf.keras.models.load_model('my_model')

# Prepare sample data
data = tf.random.normal([32, 224, 224, 3])

# Profile the model
tf.profiler.experimental.start('logdir')
model(data)
tf.profiler.experimental.stop()

# View results with TensorBoard
# !tensorboard --logdir=logdir
```

## Next Steps

In the next section, we'll explore [Algorithmic Optimization](03_Algorithmic_Optimization.md), which builds on the bottleneck identification techniques covered here to implement specific optimizations for algorithms and data structures.

## Resources

1. [Java Flight Recorder (JFR) and Mission Control](https://docs.oracle.com/javacomponents/jmc-5-4/jfr-runtime-guide/about.htm)
2. [Async-Profiler GitHub](https://github.com/jvm-profiling-tools/async-profiler)
3. [Brendan Gregg's Performance Analysis](http://www.brendangregg.com/methodology.html)
4. [Python Profilers Documentation](https://docs.python.org/3/library/profile.html)
5. [PostgreSQL Performance Optimization](https://www.postgresql.org/docs/current/performance-tips.html)
</file>

<file path="system_design_guide/01_Foundation/04_Performance_Optimization/03_Algorithmic_Optimization.md">
# Algorithmic Optimization

## Introduction

Algorithmic optimization focuses on improving the efficiency of algorithms and data structures to reduce computational complexity and resource usage. Even with powerful hardware, inefficient algorithms can lead to poor performance, especially as data volumes grow.

## Understanding Algorithmic Complexity

### Time Complexity

Time complexity measures how the runtime of an algorithm grows as the input size increases.

**Common Complexity Classes**:
- **O(1)** - Constant time: Runtime doesn't depend on input size
- **O(log n)** - Logarithmic time: Runtime grows logarithmically with input size
- **O(n)** - Linear time: Runtime grows linearly with input size
- **O(n log n)** - Linearithmic time: Common for efficient sorting algorithms
- **O(n²)** - Quadratic time: Runtime grows with the square of input size
- **O(2ⁿ)** - Exponential time: Runtime doubles with each additional input element

**Complexity Comparison**:

| Input Size (n) | O(1) | O(log n) | O(n) | O(n log n) | O(n²) | O(2ⁿ) |
|----------------|------|----------|------|------------|-------|--------|
| 10             | 1    | 3        | 10   | 30         | 100   | 1024   |
| 100            | 1    | 7        | 100  | 700        | 10K   | 2¹⁰⁰   |
| 1,000          | 1    | 10       | 1K   | 10K        | 1M    | 2¹⁰⁰⁰  |
| 1,000,000      | 1    | 20       | 1M   | 20M        | 1T    | 2¹⁰⁰⁰⁰⁰⁰|

### Space Complexity

Space complexity measures how memory usage grows with input size.

**Key Considerations**:
- Auxiliary space (extra space used by the algorithm)
- Input space (space required to store the input)
- Total space (auxiliary + input)

## Core Optimization Strategies

### 1. Choose the Right Algorithm

Selecting an appropriate algorithm is often the most impactful optimization.

**Example: Finding an Element in a Collection**

```python
# O(n) - Linear search (unordered data)
def linear_search(arr, target):
    for i, item in enumerate(arr):
        if item == target:
            return i
    return -1

# O(log n) - Binary search (ordered data)
def binary_search(arr, target):
    left, right = 0, len(arr) - 1
    while left <= right:
        mid = (left + right) // 2
        if arr[mid] == target:
            return mid
        elif arr[mid] < target:
            left = mid + 1
        else:
            right = mid - 1
    return -1
```

**When to Use**:
- Linear search: Unordered data, small datasets
- Binary search: Ordered data, larger datasets

### 2. Use Appropriate Data Structures

Choosing the right data structure can dramatically improve performance.

**Common Data Structures and Their Strengths**:

| Operation | Array | Linked List | Hash Table | Binary Search Tree | Heap |
|-----------|-------|-------------|------------|-------------------|------|
| Access    | O(1)  | O(n)        | O(1)*      | O(log n)          | O(1) |
| Search    | O(n)  | O(n)        | O(1)*      | O(log n)          | O(n) |
| Insert    | O(n)  | O(1)        | O(1)*      | O(log n)          | O(log n) |
| Delete    | O(n)  | O(1)        | O(1)*      | O(log n)          | O(log n) |

*Average case for hash tables; worst case can be O(n)

**Example: Counting Word Frequencies**

```python
# Inefficient: Using list - O(n²)
def count_words_list(text):
    words = text.lower().split()
    result = []
    
    for word in words:
        found = False
        for entry in result:
            if entry[0] == word:
                entry[1] += 1
                found = True
                break
        if not found:
            result.append([word, 1])
    
    return result

# Efficient: Using dictionary (hash table) - O(n)
def count_words_dict(text):
    words = text.lower().split()
    result = {}
    
    for word in words:
        if word in result:
            result[word] += 1
        else:
            result[word] = 1
    
    return result
```

### 3. Avoid Redundant Computations

Eliminating repeated calculations can significantly improve performance.

**Techniques**:
- Caching/memoization
- Precomputation
- Early termination

**Example: Fibonacci with Memoization**

```python
# Inefficient: O(2ⁿ) - Exponential time
def fibonacci_recursive(n):
    if n <= 1:
        return n
    return fibonacci_recursive(n-1) + fibonacci_recursive(n-2)

# Efficient: O(n) - Linear time with memoization
def fibonacci_memoized(n, memo={}):
    if n in memo:
        return memo[n]
    if n <= 1:
        return n
    memo[n] = fibonacci_memoized(n-1, memo) + fibonacci_memoized(n-2, memo)
    return memo[n]
```

### 4. Optimize Loops

Loops often dominate execution time in algorithms.

**Optimization Techniques**:
- Reduce work inside loops
- Minimize loop iterations
- Unroll loops for performance
- Use appropriate loop constructs

**Example: Matrix Multiplication Optimization**

```java
// Less efficient: Poor cache locality
public static int[][] multiplyMatrices(int[][] A, int[][] B) {
    int n = A.length;
    int[][] C = new int[n][n];
    
    for (int i = 0; i < n; i++) {
        for (int j = 0; j < n; j++) {
            for (int k = 0; k < n; k++) {
                C[i][j] += A[i][k] * B[k][j];
            }
        }
    }
    
    return C;
}

// More efficient: Better cache locality
public static int[][] multiplyMatricesOptimized(int[][] A, int[][] B) {
    int n = A.length;
    int[][] C = new int[n][n];
    
    for (int i = 0; i < n; i++) {
        for (int k = 0; k < n; k++) {
            for (int j = 0; j < n; j++) {
                C[i][j] += A[i][k] * B[k][j];
            }
        }
    }
    
    return C;
}
```

## Common Algorithmic Optimizations

### 1. String Manipulation

String operations are often performance bottlenecks.

**Optimization Techniques**:
- Use StringBuilder/StringBuffer for concatenation
- Minimize string creation in loops
- Use efficient string matching algorithms

**Example: String Concatenation**

```java
// Inefficient: Creates many intermediate strings - O(n²)
String buildString(int n) {
    String result = "";
    for (int i = 0; i < n; i++) {
        result += "item" + i;
    }
    return result;
}

// Efficient: Uses StringBuilder - O(n)
String buildStringOptimized(int n) {
    StringBuilder result = new StringBuilder();
    for (int i = 0; i < n; i++) {
        result.append("item").append(i);
    }
    return result.toString();
}
```

### 2. Collection Processing

Efficient collection handling is crucial for data-intensive applications.

**Optimization Techniques**:
- Use bulk operations
- Filter early to reduce data volume
- Process streams efficiently
- Consider lazy evaluation

**Example: Java Stream Processing**

```java
// Less efficient: Multiple passes over the data
List<Integer> getEvenSquares(List<Integer> numbers) {
    List<Integer> filtered = new ArrayList<>();
    for (Integer num : numbers) {
        if (num % 2 == 0) {
            filtered.add(num);
        }
    }
    
    List<Integer> result = new ArrayList<>();
    for (Integer num : filtered) {
        result.add(num * num);
    }
    
    return result;
}

// More efficient: Single pass with streams
List<Integer> getEvenSquaresOptimized(List<Integer> numbers) {
    return numbers.stream()
                 .filter(num -> num % 2 == 0)
                 .map(num -> num * num)
                 .collect(Collectors.toList());
}
```

### 3. Sorting and Searching

Choosing the right sorting or searching algorithm can have a major impact.

**Sorting Algorithm Selection**:
- **Quick Sort**: General-purpose, in-place sorting - O(n log n) average
- **Merge Sort**: Stable sorting with guaranteed O(n log n) performance
- **Heap Sort**: In-place sorting with O(n log n) worst-case
- **Insertion Sort**: Efficient for small or nearly sorted data - O(n²)
- **Counting/Radix Sort**: Linear time for specific data types - O(n)

**Example: Choosing the Right Sort**

```python
# For small arrays or nearly sorted data
def insertion_sort(arr):
    for i in range(1, len(arr)):
        key = arr[i]
        j = i - 1
        while j >= 0 and arr[j] > key:
            arr[j + 1] = arr[j]
            j -= 1
        arr[j + 1] = key
    return arr

# For general-purpose sorting
def quick_sort(arr):
    if len(arr) <= 1:
        return arr
    pivot = arr[len(arr) // 2]
    left = [x for x in arr if x < pivot]
    middle = [x for x in arr if x == pivot]
    right = [x for x in arr if x > pivot]
    return quick_sort(left) + middle + quick_sort(right)

# For integer data with limited range
def counting_sort(arr, max_val):
    count = [0] * (max_val + 1)
    for num in arr:
        count[num] += 1
    
    sorted_arr = []
    for i in range(max_val + 1):
        sorted_arr.extend([i] * count[i])
    
    return sorted_arr
```

## Space-Time Tradeoffs

Many optimizations involve trading memory for speed or vice versa.

### 1. Caching and Memoization

Storing results of expensive computations for reuse.

**Example: API Response Caching**

```python
# Simple in-memory cache
cache = {}

def get_user_data(user_id):
    # Check if result is in cache
    if user_id in cache:
        return cache[user_id]
    
    # Expensive operation (e.g., database query or API call)
    result = fetch_user_from_database(user_id)
    
    # Store in cache for future use
    cache[user_id] = result
    
    return result
```

### 2. Precomputation

Computing values in advance to avoid runtime calculations.

**Example: Precomputed Lookup Table**

```java
public class TrigTable {
    private static final int TABLE_SIZE = 360;
    private static final double[] SIN_TABLE = new double[TABLE_SIZE];
    private static final double[] COS_TABLE = new double[TABLE_SIZE];
    
    static {
        // Precompute values
        for (int i = 0; i < TABLE_SIZE; i++) {
            double radians = Math.toRadians(i);
            SIN_TABLE[i] = Math.sin(radians);
            COS_TABLE[i] = Math.cos(radians);
        }
    }
    
    // Fast lookup instead of calculation
    public static double sin(int degrees) {
        return SIN_TABLE[degrees % TABLE_SIZE];
    }
    
    public static double cos(int degrees) {
        return COS_TABLE[degrees % TABLE_SIZE];
    }
}
```

## Practical Example: Optimizing a Search Function

Let's optimize a function that searches for products matching certain criteria.

### Initial Implementation

```java
// Initial implementation - inefficient
public List<Product> findProducts(List<Product> allProducts, 
                                 String nameFilter, 
                                 double minPrice, 
                                 double maxPrice, 
                                 List<String> categories) {
    List<Product> result = new ArrayList<>();
    
    for (Product product : allProducts) {
        // Check name filter
        boolean nameMatches = nameFilter == null || 
                             nameFilter.isEmpty() || 
                             product.getName().toLowerCase().contains(nameFilter.toLowerCase());
        
        // Check price range
        boolean priceMatches = product.getPrice() >= minPrice && 
                              product.getPrice() <= maxPrice;
        
        // Check categories
        boolean categoryMatches = categories == null || 
                                 categories.isEmpty() || 
                                 categories.contains(product.getCategory());
        
        // Add product if all criteria match
        if (nameMatches && priceMatches && categoryMatches) {
            result.add(product);
        }
    }
    
    return result;
}
```

### Optimized Implementation

```java
// Optimized implementation
public List<Product> findProductsOptimized(List<Product> allProducts, 
                                          String nameFilter, 
                                          double minPrice, 
                                          double maxPrice, 
                                          List<String> categories) {
    // Early return for empty input
    if (allProducts == null || allProducts.isEmpty()) {
        return Collections.emptyList();
    }
    
    // Prepare filters for efficiency
    final String nameLower = nameFilter == null ? null : nameFilter.toLowerCase();
    final Set<String> categorySet = categories == null ? null : 
                                   new HashSet<>(categories);
    
    // Use stream for efficient filtering
    return allProducts.stream()
        // Apply most restrictive filters first for short-circuiting
        .filter(p -> minPrice <= p.getPrice() && p.getPrice() <= maxPrice)
        .filter(p -> nameLower == null || nameLower.isEmpty() || 
                    p.getName().toLowerCase().contains(nameLower))
        .filter(p -> categorySet == null || categorySet.isEmpty() || 
                    categorySet.contains(p.getCategory()))
        .collect(Collectors.toList());
}
```

### Optimizations Applied

1. **Early termination** for empty input
2. **Preprocessing** filters before the loop
3. **Using a HashSet** for O(1) category lookups
4. **Ordering filters** by restrictiveness
5. **Stream processing** for cleaner code and potential parallelization

## Interview Questions

### Question 1: How would you optimize an algorithm that frequently searches for items in a large collection?

**Key Points to Address**:

1. **Data Structure Selection**:
   - Use hash tables (HashSet/HashMap) for O(1) lookups when exact matches are needed
   - Use balanced trees (TreeSet/TreeMap) for O(log n) lookups with ordering
   - Consider specialized structures like Bloom filters for membership testing

2. **Indexing Strategies**:
   - Create indexes on frequently searched fields
   - Use multi-level indexing for composite searches
   - Consider inverted indexes for text search

3. **Caching**:
   - Cache frequent search results
   - Implement LRU or other eviction policies
   - Consider distributed caching for scale

4. **Search Algorithm Selection**:
   - Binary search for sorted data
   - Trie structures for prefix searches
   - Approximate algorithms for fuzzy matching

5. **Preprocessing**:
   - Sort data if multiple binary searches will be performed
   - Precompute derived values used in search criteria
   - Denormalize data to avoid joins during search

### Question 2: Explain the tradeoffs between different sorting algorithms and when you would choose each one.

**Key Points**:

1. **Quick Sort**:
   - **Pros**: Fast average case O(n log n), in-place sorting
   - **Cons**: Worst case O(n²), not stable
   - **When to use**: General-purpose sorting, especially when space is a concern

2. **Merge Sort**:
   - **Pros**: Guaranteed O(n log n) performance, stable sort
   - **Cons**: Requires O(n) extra space
   - **When to use**: When stability is required, linked lists, external sorting

3. **Heap Sort**:
   - **Pros**: O(n log n) worst-case, in-place sorting
   - **Cons**: Slower than Quick Sort in practice, not stable
   - **When to use**: When guaranteed O(n log n) performance is needed with limited space

4. **Insertion Sort**:
   - **Pros**: Simple implementation, efficient for small datasets, O(n) for nearly sorted data
   - **Cons**: O(n²) average and worst case
   - **When to use**: Small arrays (n < 20), nearly sorted data, as part of hybrid algorithms

5. **Counting/Radix Sort**:
   - **Pros**: O(n) time complexity for specific data types
   - **Cons**: Limited to integers or strings, uses extra space
   - **When to use**: When data has a limited range of values

### Question 3: How would you optimize a function that needs to perform expensive calculations repeatedly with the same inputs?

**Key Points**:

1. **Memoization**:
   - Cache results of function calls based on input parameters
   - Implement with a hash map or similar structure
   - Consider cache size limits and eviction policies

2. **Implementation Approaches**:
   - Function wrappers that add caching behavior
   - Decorators in languages that support them
   - Built-in memoization libraries

3. **Considerations**:
   - Memory usage vs. computation time tradeoff
   - Cache invalidation strategy for changing data
   - Thread safety for concurrent access

4. **Example Implementation**:
   ```python
   def memoize(func):
       cache = {}
       def wrapper(*args):
           if args not in cache:
               cache[args] = func(*args)
           return cache[args]
       return wrapper
   
   @memoize
   def expensive_calculation(x, y):
       # Expensive operation here
       return x * y
   ```

## Next Steps

In the next section, we'll explore [Database Optimization](04_Database_Optimization.md), which focuses on improving the performance of database operations, a common bottleneck in many applications.

## Resources

1. [Introduction to Algorithms](https://mitpress.mit.edu/books/introduction-algorithms-third-edition) by Cormen, Leiserson, Rivest, and Stein
2. [Effective Java](https://www.oreilly.com/library/view/effective-java-3rd/9780134686097/) by Joshua Bloch
3. [High Performance Python](https://www.oreilly.com/library/view/high-performance-python/9781492055013/) by Micha Gorelick and Ian Ozsvald
4. [Algorithm Design Manual](http://www.algorist.com/) by Steven Skiena
</file>

<file path="system_design_guide/01_Foundation/04_Performance_Optimization/04_Database_Optimization.md">
# Database Optimization

## Introduction

Database operations are often the primary bottleneck in application performance. This section covers key strategies for optimizing database performance, from query optimization to schema design and beyond.

## Query Optimization

### 1. Understanding Query Execution Plans

Query execution plans show how the database will execute a query, including:
- Access methods (table scans, index scans)
- Join algorithms and order
- Filter application
- Sort operations

**Example (PostgreSQL):**
```sql
EXPLAIN ANALYZE
SELECT customers.name, SUM(orders.amount) 
FROM customers 
JOIN orders ON customers.id = orders.customer_id
WHERE customers.region = 'North'
GROUP BY customers.name
ORDER BY SUM(orders.amount) DESC;
```

**Key Components to Analyze:**
- Sequential vs. index scans
- Join types (nested loop, hash join, merge join)
- Filter efficiency
- Sort operations and memory usage

### 2. Index Optimization

Indexes accelerate data retrieval but come with maintenance costs.

**Index Types:**
- B-tree indexes: General-purpose, good for equality and range queries
- Hash indexes: Fast equality lookups
- GIN/GiST indexes: Full-text search, complex data types
- Spatial indexes: Geographical data
- Partial indexes: Index subset of rows
- Covering indexes: Include all needed columns

**Index Creation Example:**
```sql
-- Basic index
CREATE INDEX idx_customers_email ON customers(email);

-- Composite index
CREATE INDEX idx_orders_customer_date ON orders(customer_id, order_date);

-- Partial index
CREATE INDEX idx_orders_high_value ON orders(order_date)
WHERE amount > 1000;

-- Covering index
CREATE INDEX idx_products_search ON products(category, name)
INCLUDE (price, stock_status);
```

**Index Best Practices:**
- Index columns used in WHERE, JOIN, and ORDER BY clauses
- Put most selective columns first in composite indexes
- Don't over-index (each index adds overhead to writes)
- Consider covering indexes for frequent queries
- Regularly analyze index usage and remove unused indexes

### 3. Common Query Anti-patterns

**N+1 Query Problem:**
```javascript
// Inefficient: N+1 queries
const users = await db.query('SELECT * FROM users');
for (const user of users) {
  // This executes one query per user!
  const orders = await db.query('SELECT * FROM orders WHERE user_id = ?', [user.id]);
  user.orders = orders;
}

// Efficient: Single join query
const usersWithOrders = await db.query(`
  SELECT u.*, o.* 
  FROM users u
  LEFT JOIN orders o ON u.id = o.user_id
`);
// Process results to group orders by user
```

**Inefficient LIKE Queries:**
```sql
-- Inefficient: Can't use index effectively
SELECT * FROM products WHERE name LIKE '%keyboard%';

-- Better: Use full-text search
SELECT * FROM products 
WHERE to_tsvector('english', name) @@ to_tsquery('english', 'keyboard');
```

**Unnecessary Columns:**
```sql
-- Inefficient: Retrieving all columns
SELECT * FROM large_table WHERE condition;

-- Efficient: Retrieve only needed columns
SELECT id, name, status FROM large_table WHERE condition;
```

## Schema Optimization

### 1. Normalization vs. Denormalization

**Normalization Benefits:**
- Reduces data redundancy
- Ensures data integrity
- Simplifies updates

**Denormalization Benefits:**
- Reduces join operations
- Improves read performance
- Simplifies queries

**When to Normalize:**
- Write-heavy applications
- When data integrity is critical
- When storage is a concern

**When to Denormalize:**
- Read-heavy applications
- When query performance is critical
- When joins become a bottleneck

**Example of Denormalization:**
```sql
-- Normalized schema (two tables)
CREATE TABLE authors (
  id SERIAL PRIMARY KEY,
  name VARCHAR(100),
  bio TEXT
);

CREATE TABLE books (
  id SERIAL PRIMARY KEY,
  title VARCHAR(200),
  author_id INTEGER REFERENCES authors(id),
  published_date DATE
);

-- Denormalized schema (redundant author data)
CREATE TABLE books_denormalized (
  id SERIAL PRIMARY KEY,
  title VARCHAR(200),
  author_id INTEGER REFERENCES authors(id),
  author_name VARCHAR(100),  -- Denormalized from authors
  published_date DATE
);
```

### 2. Appropriate Data Types

Choosing the right data types impacts both storage and performance.

**Best Practices:**
- Use the smallest data type that fits your needs
- Consider storage alignment
- Use fixed-length types for frequently accessed columns
- Use variable-length types for large or variable content

**Examples:**
```sql
-- Inefficient
CREATE TABLE users (
  id VARCHAR(36),  -- UUID as string
  name VARCHAR(255),  -- Oversized for names
  active VARCHAR(5),  -- 'true'/'false' as string
  login_count VARCHAR(20),  -- Number as string
  description TEXT  -- For a small field
);

-- Optimized
CREATE TABLE users (
  id UUID,  -- Native UUID type
  name VARCHAR(100),  -- Reasonable size
  active BOOLEAN,  -- Boolean type
  login_count INTEGER,  -- Integer type
  description VARCHAR(500)  -- Limited size if appropriate
);
```

### 3. Partitioning

Partitioning divides large tables into smaller, more manageable pieces.

**Partitioning Strategies:**
- **Range Partitioning**: Based on value ranges (e.g., date ranges)
- **List Partitioning**: Based on discrete values (e.g., regions)
- **Hash Partitioning**: Based on hash of column value
- **Composite Partitioning**: Combining multiple strategies

**Example (PostgreSQL):**
```sql
-- Range partitioning by date
CREATE TABLE orders (
  id SERIAL,
  customer_id INTEGER,
  order_date DATE,
  amount DECIMAL(10,2)
) PARTITION BY RANGE (order_date);

-- Create partitions
CREATE TABLE orders_2021 PARTITION OF orders
  FOR VALUES FROM ('2021-01-01') TO ('2022-01-01');

CREATE TABLE orders_2022 PARTITION OF orders
  FOR VALUES FROM ('2022-01-01') TO ('2023-01-01');
```

**Benefits of Partitioning:**
- Improved query performance through partition pruning
- Easier management of large tables
- Efficient archiving of old data
- Parallel query execution across partitions

## Database Connection Management

### 1. Connection Pooling

Connection pooling reuses database connections to avoid the overhead of establishing new connections.

**Key Concepts:**
- Pool size configuration
- Connection lifecycle management
- Connection validation
- Statement caching

**Example (Java with HikariCP):**
```java
HikariConfig config = new HikariConfig();
config.setJdbcUrl("*************************************");
config.setUsername("user");
config.setPassword("password");
config.setMaximumPoolSize(10);
config.setMinimumIdle(5);
config.setIdleTimeout(300000); // 5 minutes
config.setConnectionTimeout(10000); // 10 seconds
config.addDataSourceProperty("cachePrepStmts", "true");
config.addDataSourceProperty("prepStmtCacheSize", "250");

HikariDataSource dataSource = new HikariDataSource(config);
```

**Best Practices:**
- Size the pool appropriately (too small: waiting; too large: resource waste)
- Set appropriate timeouts
- Validate connections before use
- Monitor pool metrics

### 2. Prepared Statements

Prepared statements improve performance and security by separating SQL from data.

**Benefits:**
- Reduced parsing overhead
- Protection against SQL injection
- Potential for server-side caching
- Type safety

**Example (Java):**
```java
// Inefficient: String concatenation
String query = "SELECT * FROM users WHERE email = '" + userEmail + "'";
Statement stmt = connection.createStatement();
ResultSet rs = stmt.executeQuery(query);

// Efficient: Prepared statement
String query = "SELECT * FROM users WHERE email = ?";
PreparedStatement pstmt = connection.prepareStatement(query);
pstmt.setString(1, userEmail);
ResultSet rs = pstmt.executeQuery();
```

## Caching Strategies

### 1. Database Query Cache

Many databases offer built-in query caching.

**MySQL Query Cache Example:**
```sql
-- Check if query cache is enabled
SHOW VARIABLES LIKE 'query_cache_type';

-- Set query cache size
SET GLOBAL query_cache_size = 67108864; -- 64MB

-- Query that will be cached
SELECT * FROM products WHERE category = 'electronics';
```

**PostgreSQL Statement Caching:**
```sql
-- Enable prepared statement caching
SET prepared_statement_cache_size = 100;
```

### 2. Application-Level Caching

Implementing caching in the application layer.

**Example (Redis with Node.js):**
```javascript
const redis = require('redis');
const client = redis.createClient();
const { promisify } = require('util');
const getAsync = promisify(client.get).bind(client);
const setAsync = promisify(client.set).bind(client);

async function getUserById(id) {
  // Try to get from cache first
  const cacheKey = `user:${id}`;
  const cachedUser = await getAsync(cacheKey);
  
  if (cachedUser) {
    return JSON.parse(cachedUser);
  }
  
  // If not in cache, get from database
  const user = await db.query('SELECT * FROM users WHERE id = ?', [id]);
  
  // Store in cache for future requests (expire after 1 hour)
  await setAsync(cacheKey, JSON.stringify(user), 'EX', 3600);
  
  return user;
}
```

**Caching Considerations:**
- Cache invalidation strategy
- Time-to-live (TTL) settings
- Memory usage
- Cache hit ratio monitoring
- Distributed caching for scaled applications

### 3. Materialized Views

Materialized views store the results of a query for faster access.

**Example (PostgreSQL):**
```sql
-- Create a materialized view
CREATE MATERIALIZED VIEW order_summary AS
SELECT 
  customer_id,
  COUNT(*) as order_count,
  SUM(amount) as total_spent,
  MAX(order_date) as last_order_date
FROM orders
GROUP BY customer_id;

-- Create an index on the materialized view
CREATE INDEX idx_order_summary_customer ON order_summary(customer_id);

-- Refresh the materialized view
REFRESH MATERIALIZED VIEW order_summary;
```

**Benefits:**
- Precomputed query results
- Significant performance improvement for complex queries
- Reduced load on the database

## Read/Write Splitting

Separating read and write operations to different database instances.

**Architecture:**
- Primary database handles writes
- Read replicas handle read queries
- Load balancer distributes read queries

**Example (Java with Spring):**
```java
@Configuration
public class DataSourceConfig {
    
    @Bean
    @Primary
    @ConfigurationProperties("spring.datasource.write")
    public DataSource writeDataSource() {
        return DataSourceBuilder.create().build();
    }
    
    @Bean
    @ConfigurationProperties("spring.datasource.read")
    public DataSource readDataSource() {
        return DataSourceBuilder.create().build();
    }
    
    @Bean
    public DataSource routingDataSource() {
        ReplicationRoutingDataSource routingDataSource = new ReplicationRoutingDataSource();
        
        Map<Object, Object> dataSources = new HashMap<>();
        dataSources.put("write", writeDataSource());
        dataSources.put("read", readDataSource());
        
        routingDataSource.setTargetDataSources(dataSources);
        routingDataSource.setDefaultTargetDataSource(writeDataSource());
        
        return routingDataSource;
    }
}

// Custom routing data source
public class ReplicationRoutingDataSource extends AbstractRoutingDataSource {
    @Override
    protected Object determineCurrentLookupKey() {
        return TransactionSynchronizationManager.isCurrentTransactionReadOnly() 
               ? "read" : "write";
    }
}
```

**Benefits:**
- Scales read capacity
- Reduces load on primary database
- Improves read performance
- Provides read availability during primary maintenance

## Practical Example: Optimizing a Product Search API

Let's optimize a product search API that's experiencing performance issues.

### Initial Implementation

```java
@RestController
@RequestMapping("/api/products")
public class ProductController {
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    @GetMapping("/search")
    public List<Product> searchProducts(
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String category,
            @RequestParam(required = false) Double minPrice,
            @RequestParam(required = false) Double maxPrice) {
        
        StringBuilder sql = new StringBuilder("SELECT * FROM products WHERE 1=1");
        List<Object> params = new ArrayList<>();
        
        if (name != null && !name.isEmpty()) {
            sql.append(" AND name LIKE ?");
            params.add("%" + name + "%");
        }
        
        if (category != null && !category.isEmpty()) {
            sql.append(" AND category = ?");
            params.add(category);
        }
        
        if (minPrice != null) {
            sql.append(" AND price >= ?");
            params.add(minPrice);
        }
        
        if (maxPrice != null) {
            sql.append(" AND price <= ?");
            params.add(maxPrice);
        }
        
        return jdbcTemplate.query(
            sql.toString(), 
            params.toArray(), 
            (rs, rowNum) -> new Product(
                rs.getLong("id"),
                rs.getString("name"),
                rs.getString("category"),
                rs.getDouble("price"),
                rs.getString("description")
            )
        );
    }
}
```

### Optimized Implementation

```java
@RestController
@RequestMapping("/api/products")
public class ProductController {
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    @Autowired
    private RedisTemplate<String, List<Product>> redisTemplate;
    
    @GetMapping("/search")
    public List<Product> searchProducts(
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String category,
            @RequestParam(required = false) Double minPrice,
            @RequestParam(required = false) Double maxPrice) {
        
        // Generate cache key based on search parameters
        String cacheKey = generateCacheKey(name, category, minPrice, maxPrice);
        
        // Try to get from cache
        List<Product> cachedResults = redisTemplate.opsForValue().get(cacheKey);
        if (cachedResults != null) {
            return cachedResults;
        }
        
        // Build optimized query
        StringBuilder sql = new StringBuilder();
        List<Object> params = new ArrayList<>();
        
        // Select only needed columns
        sql.append("SELECT id, name, category, price FROM products");
        
        // Use WHERE 1=1 only if we have conditions
        boolean hasConditions = false;
        
        if (name != null && !name.isEmpty()) {
            sql.append(hasConditions ? " AND" : " WHERE");
            // Use full-text search if available
            sql.append(" to_tsvector('english', name) @@ plainto_tsquery('english', ?)");
            params.add(name);
            hasConditions = true;
        }
        
        if (category != null && !category.isEmpty()) {
            sql.append(hasConditions ? " AND" : " WHERE");
            sql.append(" category = ?");
            params.add(category);
            hasConditions = true;
        }
        
        if (minPrice != null) {
            sql.append(hasConditions ? " AND" : " WHERE");
            sql.append(" price >= ?");
            params.add(minPrice);
            hasConditions = true;
        }
        
        if (maxPrice != null) {
            sql.append(hasConditions ? " AND" : " WHERE");
            sql.append(" price <= ?");
            params.add(maxPrice);
            hasConditions = true;
        }
        
        // Add limit to prevent excessive results
        sql.append(" LIMIT 100");
        
        // Execute query
        List<Product> results = jdbcTemplate.query(
            sql.toString(), 
            params.toArray(), 
            (rs, rowNum) -> new Product(
                rs.getLong("id"),
                rs.getString("name"),
                rs.getString("category"),
                rs.getDouble("price"),
                null  // Don't load description unless needed
            )
        );
        
        // Cache results for 10 minutes
        redisTemplate.opsForValue().set(cacheKey, results, 10, TimeUnit.MINUTES);
        
        return results;
    }
    
    private String generateCacheKey(String name, String category, Double minPrice, Double maxPrice) {
        return String.format("products:search:%s:%s:%s:%s",
            name == null ? "" : name,
            category == null ? "" : category,
            minPrice == null ? "" : minPrice,
            maxPrice == null ? "" : maxPrice
        );
    }
    
    // Separate endpoint for getting product details
    @GetMapping("/{id}")
    public Product getProductDetails(@PathVariable Long id) {
        return jdbcTemplate.queryForObject(
            "SELECT * FROM products WHERE id = ?",
            new Object[]{id},
            (rs, rowNum) -> new Product(
                rs.getLong("id"),
                rs.getString("name"),
                rs.getString("category"),
                rs.getDouble("price"),
                rs.getString("description")
            )
        );
    }
}
```

### Database Optimizations

```sql
-- Create indexes for search fields
CREATE INDEX idx_products_category ON products(category);
CREATE INDEX idx_products_price ON products(price);

-- Create full-text search index
CREATE INDEX idx_products_name_fts ON products USING GIN (to_tsvector('english', name));

-- Create a materialized view for common searches
CREATE MATERIALIZED VIEW popular_products AS
SELECT id, name, category, price
FROM products
WHERE price BETWEEN 10 AND 100
  AND category IN ('electronics', 'home', 'clothing')
ORDER BY popularity_score DESC
LIMIT 1000;

-- Create index on the materialized view
CREATE INDEX idx_popular_products_category ON popular_products(category);
CREATE INDEX idx_popular_products_price ON popular_products(price);

-- Refresh the materialized view periodically
REFRESH MATERIALIZED VIEW CONCURRENTLY popular_products;
```

### Optimizations Applied

1. **Caching**: Added Redis caching for search results
2. **Query Optimization**: 
   - Selected only needed columns
   - Used full-text search instead of LIKE
   - Added LIMIT to prevent excessive results
3. **Indexing**: Added appropriate indexes for search fields
4. **Materialized Views**: Created for common search patterns
5. **Separation of Concerns**: Split detailed product view from search results

## Interview Questions

### Question 1: How would you diagnose and fix a slow database query?

**Key Points to Address**:

1. **Diagnosis Steps**:
   - Use EXPLAIN (ANALYZE) to understand the query execution plan
   - Check for missing indexes or inefficient index usage
   - Look for full table scans, especially on large tables
   - Identify expensive operations (sorts, hash joins, etc.)
   - Check actual vs. estimated row counts

2. **Common Issues and Solutions**:
   - **Missing Indexes**: Add appropriate indexes for WHERE, JOIN, and ORDER BY clauses
   - **Inefficient Joins**: Ensure proper join conditions and join order
   - **Suboptimal Query Structure**: Rewrite to avoid correlated subqueries or excessive joins
   - **Data Skew**: Update statistics or consider partitioning
   - **Inefficient Functions**: Avoid functions on indexed columns in WHERE clauses

3. **Monitoring and Tools**:
   - Slow query logs
   - Performance schema or system catalogs
   - Query analyzers and visualization tools
   - Database monitoring solutions

### Question 2: What strategies would you use to scale a database for high read and write loads?

**Key Points**:

1. **Read Scaling**:
   - Read replicas for distributing read queries
   - Caching frequently accessed data
   - Materialized views for complex queries
   - Content delivery networks for static content
   - Database query result caching

2. **Write Scaling**:
   - Vertical scaling (more powerful database server)
   - Sharding data across multiple databases
   - Write-behind caching
   - Batch processing for bulk operations
   - Command-query responsibility segregation (CQRS)

3. **General Scaling Techniques**:
   - Connection pooling optimization
   - Database parameter tuning
   - Schema optimization
   - Asynchronous processing for non-critical operations
   - Microservices with dedicated databases

4. **Specific Database Technologies**:
   - NoSQL databases for specific workloads
   - Distributed databases (e.g., Cassandra, CockroachDB)
   - Time-series databases for metrics and logs
   - In-memory databases for ultra-low latency

### Question 3: Explain the tradeoffs between different database indexing strategies.

**Key Points**:

1. **B-tree Indexes**:
   - **Pros**: Good for range queries and equality, works with most data types
   - **Cons**: Slower for writes, larger storage overhead
   - **Best for**: General-purpose indexing, ordered data access

2. **Hash Indexes**:
   - **Pros**: Very fast for equality lookups, smaller than B-trees
   - **Cons**: Useless for range queries, no ordering
   - **Best for**: Exact match lookups, caching layers

3. **Bitmap Indexes**:
   - **Pros**: Efficient for low-cardinality columns, good for AND/OR operations
   - **Cons**: High maintenance cost for high-cardinality or frequently updated data
   - **Best for**: Data warehousing, columns with few distinct values

4. **Full-Text Indexes**:
   - **Pros**: Optimized for text search, supports relevance ranking
   - **Cons**: Higher storage and maintenance overhead
   - **Best for**: Search functionality, document databases

5. **Spatial Indexes**:
   - **Pros**: Efficient for geographic and multidimensional data
   - **Cons**: Specialized use case, complex implementation
   - **Best for**: Geographic information systems, location-based services

## Next Steps

In the next section, we'll explore [Network Optimization](05_Network_Optimization.md), which focuses on improving the performance of network communications, another critical aspect of system performance.

## Resources

1. [Use the Index, Luke!](https://use-the-index-luke.com/) - A guide to database performance for developers
2. [High Performance MySQL](https://www.oreilly.com/library/view/high-performance-mysql/9781492080503/) by Baron Schwartz, Peter Zaitsev, and Vadim Tkachenko
3. [PostgreSQL Documentation: Performance Tips](https://www.postgresql.org/docs/current/performance-tips.html)
4. [SQL Performance Explained](https://sql-performance-explained.com/) by Markus Winand
5. [Database Internals](https://www.databass.dev/) by Alex Petrov
</file>

<file path="system_design_guide/01_Foundation/code/fault_tolerant_api/api_client.py">
"""
Fault-Tolerant API Client

This module implements a fault-tolerant HTTP client for external APIs with:
1. Retry mechanism for transient failures
2. Circuit breaker for persistent failures
3. Fallbacks for when the API is unavailable
4. Timeout handling
5. Logging and monitoring
"""
import requests
import time
import random
import logging
import threading
from enum import Enum
from typing import Any, Callable, Dict, List, Optional, TypeVar, Union
from dataclasses import dataclass

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("fault_tolerant_api")

# Type variable for generic return types
T = TypeVar('T')

class CircuitState(Enum):
    """Possible states for the circuit breaker."""
    CLOSED = "CLOSED"  # Normal operation, requests pass through
    OPEN = "OPEN"      # Circuit is open, requests fail fast
    HALF_OPEN = "HALF_OPEN"  # Testing if the service has recovered


@dataclass
class CircuitBreakerConfig:
    """Configuration for the circuit breaker."""
    failure_threshold: int = 5  # Number of failures before opening circuit
    success_threshold: int = 3  # Number of successes in half-open state to close circuit
    timeout_seconds: int = 60   # Time to wait before transitioning from open to half-open
    

@dataclass
class RetryConfig:
    """Configuration for the retry mechanism."""
    max_retries: int = 3  # Maximum number of retry attempts
    initial_backoff_ms: int = 100  # Initial backoff time in milliseconds
    max_backoff_ms: int = 5000  # Maximum backoff time in milliseconds
    backoff_multiplier: float = 2.0  # Multiplier for exponential backoff
    jitter_factor: float = 0.1  # Random jitter factor to add to backoff


@dataclass
class TimeoutConfig:
    """Configuration for request timeouts."""
    connect_timeout_seconds: float = 3.0  # Timeout for establishing connection
    read_timeout_seconds: float = 10.0  # Timeout for reading response


@dataclass
class ApiClientConfig:
    """Configuration for the API client."""
    base_url: str
    circuit_breaker: CircuitBreakerConfig = CircuitBreakerConfig()
    retry: RetryConfig = RetryConfig()
    timeout: TimeoutConfig = TimeoutConfig()
    headers: Dict[str, str] = None


class CircuitBreaker:
    """
    Implementation of the Circuit Breaker pattern.
    
    Tracks failures and prevents calls to services that are likely to fail.
    """
    
    def __init__(self, config: CircuitBreakerConfig):
        """Initialize the circuit breaker with the given configuration."""
        self.config = config
        self.state = CircuitState.CLOSED
        self.failure_count = 0
        self.success_count = 0
        self.last_failure_time = 0
        self.lock = threading.RLock()
        
    def allow_request(self) -> bool:
        """
        Check if a request should be allowed based on the current circuit state.
        
        Returns:
            bool: True if the request should be allowed, False otherwise
        """
        with self.lock:
            if self.state == CircuitState.CLOSED:
                return True
                
            if self.state == CircuitState.OPEN:
                # Check if timeout has elapsed to transition to half-open
                if time.time() - self.last_failure_time > self.config.timeout_seconds:
                    logger.info("Circuit transitioning from OPEN to HALF_OPEN")
                    self.state = CircuitState.HALF_OPEN
                    self.success_count = 0
                    return True
                return False
                
            if self.state == CircuitState.HALF_OPEN:
                # In half-open state, only allow limited requests to test the service
                return self.success_count < self.config.success_threshold
                
            return True
    
    def record_success(self) -> None:
        """Record a successful request."""
        with self.lock:
            if self.state == CircuitState.HALF_OPEN:
                self.success_count += 1
                if self.success_count >= self.config.success_threshold:
                    logger.info("Circuit transitioning from HALF_OPEN to CLOSED")
                    self.state = CircuitState.CLOSED
                    self.failure_count = 0
                    self.success_count = 0
            elif self.state == CircuitState.CLOSED:
                self.failure_count = 0
    
    def record_failure(self) -> None:
        """Record a failed request."""
        with self.lock:
            self.last_failure_time = time.time()
            
            if self.state == CircuitState.CLOSED:
                self.failure_count += 1
                if self.failure_count >= self.config.failure_threshold:
                    logger.warning("Circuit transitioning from CLOSED to OPEN")
                    self.state = CircuitState.OPEN
                    
            elif self.state == CircuitState.HALF_OPEN:
                logger.warning("Failure in HALF_OPEN state, circuit transitioning back to OPEN")
                self.state = CircuitState.OPEN
                self.success_count = 0
    
    def get_state(self) -> CircuitState:
        """Get the current state of the circuit breaker."""
        with self.lock:
            return self.state


class FaultTolerantApiClient:
    """
    A fault-tolerant HTTP client for external APIs.
    
    Features:
    - Retry mechanism for transient failures
    - Circuit breaker for persistent failures
    - Fallbacks for when the API is unavailable
    - Timeout handling
    - Logging and monitoring
    """
    
    def __init__(self, config: ApiClientConfig):
        """Initialize the API client with the given configuration."""
        self.config = config
        self.circuit_breaker = CircuitBreaker(config.circuit_breaker)
        self.session = requests.Session()
        if config.headers:
            self.session.headers.update(config.headers)
        
        # Metrics for monitoring
        self.metrics = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "circuit_open_count": 0,
            "retry_count": 0,
            "fallback_count": 0,
            "timeout_count": 0
        }
        self.metrics_lock = threading.Lock()
    
    def get(self, path: str, params: Dict[str, Any] = None, 
            fallback: Callable[[], T] = None) -> Union[Dict[str, Any], T]:
        """
        Perform a GET request with fault tolerance.
        
        Args:
            path: The API endpoint path
            params: Query parameters
            fallback: Function to call when the request fails
            
        Returns:
            The API response as a dictionary or the fallback result
        """
        return self._request_with_fallback(
            method="GET",
            path=path,
            params=params,
            fallback=fallback
        )
    
    def post(self, path: str, data: Dict[str, Any] = None, json: Dict[str, Any] = None,
             fallback: Callable[[], T] = None) -> Union[Dict[str, Any], T]:
        """
        Perform a POST request with fault tolerance.
        
        Args:
            path: The API endpoint path
            data: Form data
            json: JSON data
            fallback: Function to call when the request fails
            
        Returns:
            The API response as a dictionary or the fallback result
        """
        return self._request_with_fallback(
            method="POST",
            path=path,
            data=data,
            json=json,
            fallback=fallback
        )
    
    def put(self, path: str, data: Dict[str, Any] = None, json: Dict[str, Any] = None,
            fallback: Callable[[], T] = None) -> Union[Dict[str, Any], T]:
        """
        Perform a PUT request with fault tolerance.
        
        Args:
            path: The API endpoint path
            data: Form data
            json: JSON data
            fallback: Function to call when the request fails
            
        Returns:
            The API response as a dictionary or the fallback result
        """
        return self._request_with_fallback(
            method="PUT",
            path=path,
            data=data,
            json=json,
            fallback=fallback
        )
    
    def delete(self, path: str, fallback: Callable[[], T] = None) -> Union[Dict[str, Any], T]:
        """
        Perform a DELETE request with fault tolerance.
        
        Args:
            path: The API endpoint path
            fallback: Function to call when the request fails
            
        Returns:
            The API response as a dictionary or the fallback result
        """
        return self._request_with_fallback(
            method="DELETE",
            path=path,
            fallback=fallback
        )
    
    def _request_with_fallback(self, method: str, path: str, fallback: Callable[[], T] = None, 
                              **kwargs) -> Union[Dict[str, Any], T]:
        """
        Perform an HTTP request with circuit breaking, retries, and fallback.
        
        Args:
            method: HTTP method (GET, POST, etc.)
            path: API endpoint path
            fallback: Function to call when the request fails
            **kwargs: Additional arguments to pass to the request
            
        Returns:
            The API response as a dictionary or the fallback result
        """
        # Update metrics
        with self.metrics_lock:
            self.metrics["total_requests"] += 1
        
        # Check if circuit breaker allows the request
        if not self.circuit_breaker.allow_request():
            logger.warning(f"Circuit breaker is open, failing fast for {method} {path}")
            with self.metrics_lock:
                self.metrics["circuit_open_count"] += 1
            return self._handle_fallback(fallback, f"Circuit breaker open for {path}")
        
        # Add timeout configuration
        kwargs.setdefault('timeout', (
            self.config.timeout.connect_timeout_seconds,
            self.config.timeout.read_timeout_seconds
        ))
        
        # Construct the full URL
        url = f"{self.config.base_url.rstrip('/')}/{path.lstrip('/')}"
        
        # Try the request with retries
        retry_config = self.config.retry
        retries = 0
        
        while True:
            try:
                logger.debug(f"Attempting {method} request to {url}")
                response = self.session.request(method, url, **kwargs)
                
                # Check if the response indicates success
                response.raise_for_status()
                
                # Record success in circuit breaker
                self.circuit_breaker.record_success()
                
                # Update metrics
                with self.metrics_lock:
                    self.metrics["successful_requests"] += 1
                
                # Return the response data
                return response.json()
                
            except (requests.exceptions.RequestException, ValueError) as e:
                # Determine if we should retry
                should_retry = (
                    retries < retry_config.max_retries and 
                    self._is_retriable_error(e)
                )
                
                if should_retry:
                    retries += 1
                    with self.metrics_lock:
                        self.metrics["retry_count"] += 1
                    
                    # Calculate backoff time with exponential backoff and jitter
                    backoff_ms = min(
                        retry_config.initial_backoff_ms * (retry_config.backoff_multiplier ** (retries - 1)),
                        retry_config.max_backoff_ms
                    )
                    jitter_ms = random.uniform(
                        -retry_config.jitter_factor * backoff_ms,
                        retry_config.jitter_factor * backoff_ms
                    )
                    sleep_time = (backoff_ms + jitter_ms) / 1000.0  # Convert to seconds
                    
                    logger.info(f"Request failed, retrying in {sleep_time:.2f}s ({retries}/{retry_config.max_retries}): {str(e)}")
                    time.sleep(sleep_time)
                    continue
                
                # We've exhausted retries or encountered a non-retriable error
                logger.error(f"Request failed after {retries} retries: {str(e)}")
                
                # Record failure in circuit breaker
                self.circuit_breaker.record_failure()
                
                # Update metrics
                with self.metrics_lock:
                    self.metrics["failed_requests"] += 1
                    if isinstance(e, requests.exceptions.Timeout):
                        self.metrics["timeout_count"] += 1
                
                # Use fallback if provided
                return self._handle_fallback(fallback, str(e))
    
    def _is_retriable_error(self, error: Exception) -> bool:
        """
        Determine if an error should trigger a retry.
        
        Args:
            error: The exception that occurred
            
        Returns:
            True if the error is retriable, False otherwise
        """
        # Network-related errors are generally retriable
        if isinstance(error, (
            requests.exceptions.ConnectionError,
            requests.exceptions.Timeout,
            requests.exceptions.TooManyRedirects
        )):
            return True
        
        # Some HTTP status codes are retriable
        if isinstance(error, requests.exceptions.HTTPError):
            status_code = error.response.status_code
            # 429 (Too Many Requests) and 5xx errors are generally retriable
            return status_code == 429 or 500 <= status_code < 600
        
        return False
    
    def _handle_fallback(self, fallback: Callable[[], T], error_message: str) -> Union[Dict[str, Any], T]:
        """
        Handle fallback logic when a request fails.
        
        Args:
            fallback: Function to call for fallback
            error_message: The error message
            
        Returns:
            The fallback result or an error dictionary
        """
        if fallback:
            logger.info(f"Using fallback for failed request: {error_message}")
            with self.metrics_lock:
                self.metrics["fallback_count"] += 1
            try:
                return fallback()
            except Exception as e:
                logger.error(f"Fallback also failed: {str(e)}")
                return {"error": "Service unavailable", "message": error_message}
        else:
            return {"error": "Service unavailable", "message": error_message}
    
    def get_metrics(self) -> Dict[str, int]:
        """Get the current metrics for monitoring."""
        with self.metrics_lock:
            return self.metrics.copy()
    
    def get_circuit_state(self) -> str:
        """Get the current state of the circuit breaker."""
        return self.circuit_breaker.get_state().value
    
    def reset_metrics(self) -> None:
        """Reset all metrics counters to zero."""
        with self.metrics_lock:
            for key in self.metrics:
                self.metrics[key] = 0


# Example usage
if __name__ == "__main__":
    # Configure the client
    config = ApiClientConfig(
        base_url="https://api.example.com",
        circuit_breaker=CircuitBreakerConfig(
            failure_threshold=3,
            success_threshold=2,
            timeout_seconds=30
        ),
        retry=RetryConfig(
            max_retries=3,
            initial_backoff_ms=200,
            max_backoff_ms=2000,
            backoff_multiplier=2.0,
            jitter_factor=0.2
        ),
        timeout=TimeoutConfig(
            connect_timeout_seconds=2.0,
            read_timeout_seconds=5.0
        ),
        headers={
            "User-Agent": "FaultTolerantApiClient/1.0",
            "Accept": "application/json"
        }
    )
    
    # Create the client
    client = FaultTolerantApiClient(config)
    
    # Example GET request with fallback
    def fallback_data():
        return {"data": "fallback", "source": "cache"}
    
    try:
        # This would normally call the API
        result = client.get("users/123", fallback=fallback_data)
        print(f"Result: {result}")
        
        # Check metrics
        print(f"Metrics: {client.get_metrics()}")
        print(f"Circuit state: {client.get_circuit_state()}")
    except Exception as e:
        print(f"Error: {str(e)}")
</file>

<file path="system_design_guide/01_Foundation/code/fault_tolerant_api/example_app.py">
"""
Example Application Using the Fault-Tolerant API Client

This example demonstrates how to use the fault-tolerant API client
in a real-world application scenario.
"""
import time
import logging
from flask import Flask, jsonify, request
from api_client import (
    FaultTolerantApiClient, 
    ApiClientConfig, 
    CircuitBreakerConfig, 
    RetryConfig, 
    TimeoutConfig
)

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("example_app")

# Create Flask app
app = Flask(__name__)

# Configure the API client
weather_api_config = ApiClientConfig(
    base_url="https://api.weatherapi.com/v1",
    circuit_breaker=CircuitBreakerConfig(
        failure_threshold=5,
        success_threshold=3,
        timeout_seconds=60
    ),
    retry=RetryConfig(
        max_retries=3,
        initial_backoff_ms=200,
        max_backoff_ms=2000
    ),
    timeout=TimeoutConfig(
        connect_timeout_seconds=3.0,
        read_timeout_seconds=10.0
    ),
    headers={
        "User-Agent": "WeatherApp/1.0",
        "Accept": "application/json"
    }
)

# Create the API client
weather_api = FaultTolerantApiClient(weather_api_config)

# In-memory cache for weather data
weather_cache = {}

def get_cached_weather(location):
    """Get weather data from cache if available and not expired."""
    if location in weather_cache:
        cache_entry = weather_cache[location]
        # Check if cache entry is less than 30 minutes old
        if time.time() - cache_entry["timestamp"] < 1800:
            logger.info(f"Using cached weather data for {location}")
            return cache_entry["data"]
    return None

def cache_weather_data(location, data):
    """Cache weather data with timestamp."""
    weather_cache[location] = {
        "data": data,
        "timestamp": time.time()
    }

def get_default_weather(location):
    """Provide default weather data when API is unavailable."""
    logger.warning(f"Using default weather data for {location}")
    return {
        "location": location,
        "temperature": 20,
        "condition": "Unknown",
        "humidity": 50,
        "source": "default"
    }

@app.route('/weather/<location>')
def get_weather(location):
    """
    Get weather information for a location.
    
    Uses the fault-tolerant API client to fetch data from the weather API,
    with caching and fallback mechanisms.
    """
    # Check cache first
    cached_data = get_cached_weather(location)
    if cached_data:
        cached_data["source"] = "cache"
        return jsonify(cached_data)
    
    # Define fallback function that uses cache or defaults
    def weather_fallback():
        # Try to use slightly expired cache data as fallback
        for loc, entry in weather_cache.items():
            if loc == location and time.time() - entry["timestamp"] < 86400:  # 24 hours
                logger.info(f"Using expired cache as fallback for {location}")
                data = entry["data"].copy()
                data["source"] = "expired_cache"
                return data
        
        # If no suitable cache entry, return default data
        return get_default_weather(location)
    
    # Fetch from API with fallback
    try:
        # Add API key from request args if provided
        params = {"q": location}
        if "api_key" in request.args:
            params["key"] = request.args["api_key"]
        
        # Make the API request
        weather_data = weather_api.get(
            "current.json", 
            params=params,
            fallback=weather_fallback
        )
        
        # Process and simplify the response
        if "error" in weather_data:
            # API returned an error
            return jsonify(weather_fallback())
        
        # Extract relevant data
        processed_data = {
            "location": weather_data.get("location", {}).get("name", location),
            "temperature": weather_data.get("current", {}).get("temp_c"),
            "condition": weather_data.get("current", {}).get("condition", {}).get("text"),
            "humidity": weather_data.get("current", {}).get("humidity"),
            "source": "api"
        }
        
        # Cache the processed data
        cache_weather_data(location, processed_data)
        
        return jsonify(processed_data)
        
    except Exception as e:
        logger.error(f"Unexpected error getting weather for {location}: {str(e)}")
        return jsonify(weather_fallback())

@app.route('/metrics')
def get_metrics():
    """Get metrics about the API client."""
    return jsonify({
        "api_metrics": weather_api.get_metrics(),
        "circuit_state": weather_api.get_circuit_state(),
        "cache_size": len(weather_cache)
    })

@app.route('/health')
def health_check():
    """Health check endpoint."""
    circuit_state = weather_api.get_circuit_state()
    is_healthy = circuit_state != "OPEN"
    
    return jsonify({
        "status": "healthy" if is_healthy else "degraded",
        "circuit_state": circuit_state,
        "timestamp": time.time()
    }), 200 if is_healthy else 503

if __name__ == "__main__":
    app.run(debug=True, port=5000)
</file>

<file path="system_design_guide/01_Foundation/code/fault_tolerant_api/README.md">
# Fault-Tolerant API Client

This is a practical implementation of a fault-tolerant HTTP client that demonstrates the reliability and fault tolerance concepts covered in Module 1.

## Features

- **Retry Mechanism**: Automatically retries failed requests with exponential backoff and jitter
- **Circuit Breaker**: Prevents cascading failures by failing fast when a service is unhealthy
- **Fallbacks**: Provides alternative responses when the primary service is unavailable
- **Timeout Handling**: Sets appropriate timeouts for connections and reads
- **Metrics Collection**: Tracks success/failure rates and other important metrics
- **Configurable Behavior**: Allows customization of all fault tolerance parameters

## Components

### 1. FaultTolerantApiClient

The main client class that provides fault-tolerant HTTP requests:
- Wraps the Python `requests` library with additional reliability features
- Supports standard HTTP methods (GET, POST, PUT, DELETE)
- Handles errors gracefully with configurable fallbacks
- Collects metrics for monitoring

### 2. CircuitBreaker

Implementation of the Circuit Breaker pattern:
- Tracks failure rates and prevents calls to failing services
- Supports three states: CLOSED, OPEN, and HALF-OPEN
- Automatically transitions between states based on success/failure patterns
- Provides fast failure when a service is known to be down

### 3. Configuration Classes

Structured configuration for different aspects of fault tolerance:
- `ApiClientConfig`: Overall client configuration
- `CircuitBreakerConfig`: Circuit breaker parameters
- `RetryConfig`: Retry behavior settings
- `TimeoutConfig`: Connection and read timeout values

## Example Application

The repository includes an example weather application that demonstrates how to use the fault-tolerant API client in a real-world scenario:
- Fetches weather data from an external API
- Implements caching as an additional reliability mechanism
- Provides graceful degradation when the API is unavailable
- Exposes metrics for monitoring

## Usage

### Basic Usage

```python
from api_client import FaultTolerantApiClient, ApiClientConfig

# Configure the client
config = ApiClientConfig(
    base_url="https://api.example.com",
    # Additional configuration options...
)

# Create the client
client = FaultTolerantApiClient(config)

# Make a request with a fallback
def fallback_data():
    return {"data": "fallback", "source": "cache"}

result = client.get("users/123", fallback=fallback_data)
```

### Configuration Options

```python
from api_client import (
    ApiClientConfig, 
    CircuitBreakerConfig, 
    RetryConfig, 
    TimeoutConfig
)

config = ApiClientConfig(
    base_url="https://api.example.com",
    circuit_breaker=CircuitBreakerConfig(
        failure_threshold=5,    # Number of failures before opening circuit
        success_threshold=3,    # Successes in half-open state to close circuit
        timeout_seconds=60      # Time before transitioning from open to half-open
    ),
    retry=RetryConfig(
        max_retries=3,          # Maximum retry attempts
        initial_backoff_ms=200, # Initial backoff time
        max_backoff_ms=2000,    # Maximum backoff time
        backoff_multiplier=2.0, # Multiplier for exponential backoff
        jitter_factor=0.1       # Random jitter factor
    ),
    timeout=TimeoutConfig(
        connect_timeout_seconds=3.0,  # Connection timeout
        read_timeout_seconds=10.0     # Read timeout
    ),
    headers={                   # Default headers for all requests
        "User-Agent": "MyApp/1.0",
        "Accept": "application/json"
    }
)
```

### Monitoring

```python
# Get current metrics
metrics = client.get_metrics()
print(f"Total requests: {metrics['total_requests']}")
print(f"Success rate: {metrics['successful_requests'] / metrics['total_requests'] * 100:.2f}%")

# Get circuit breaker state
circuit_state = client.get_circuit_state()
print(f"Circuit state: {circuit_state}")
```

## Requirements

- Python 3.7+
- requests
- Flask (for example application)
- responses (for tests)

## Running the Tests

```bash
pip install responses
python -m unittest test_api_client.py
```

## Running the Example Application

```bash
pip install flask
python example_app.py
```

Then visit:
- `http://localhost:5000/weather/London` to get weather data
- `http://localhost:5000/metrics` to see API client metrics
- `http://localhost:5000/health` for a health check endpoint

## Fault Tolerance Patterns Demonstrated

### 1. Circuit Breaker Pattern

The circuit breaker prevents cascading failures by:
- Tracking failure rates
- Opening the circuit when failures exceed a threshold
- Allowing limited testing in half-open state
- Automatically recovering when the service returns to health

### 2. Retry Pattern with Exponential Backoff

The retry mechanism handles transient failures by:
- Automatically retrying failed requests
- Using exponential backoff to avoid overwhelming the service
- Adding jitter to prevent synchronized retries
- Distinguishing between retriable and non-retriable errors

### 3. Fallback Pattern

The fallback mechanism provides graceful degradation by:
- Allowing custom fallback functions for each request
- Executing fallbacks when all retries fail
- Supporting different fallback strategies (caching, defaults, etc.)

### 4. Timeout Pattern

The timeout handling prevents hanging requests by:
- Setting appropriate connection and read timeouts
- Treating timeouts as retriable errors
- Providing metrics on timeout frequency

## Exercise

Try extending this implementation with the following features:
1. Bulkhead pattern to isolate different API endpoints
2. Request caching with time-based expiration
3. Distributed circuit breaker state using Redis
4. Adaptive timeout based on recent response times
5. Comprehensive logging with correlation IDs

## Next Steps

After understanding this implementation, consider how you would modify it to handle:
1. Asynchronous requests using `asyncio` and `aiohttp`
2. OAuth authentication with token refresh
3. GraphQL queries in addition to REST
4. Streaming responses for large data sets
5. WebSocket connections with reconnection logic
</file>

<file path="system_design_guide/01_Foundation/code/fault_tolerant_api/requirements.txt">
requests>=2.25.0
flask>=2.0.0
responses>=0.13.0  # For testing
</file>

<file path="system_design_guide/01_Foundation/code/fault_tolerant_api/test_api_client.py">
"""
Tests for the Fault-Tolerant API Client.

This module contains tests that demonstrate the fault tolerance features:
1. Retry mechanism
2. Circuit breaker
3. Fallbacks
4. Timeout handling
"""
import unittest
import time
import responses
import requests
from unittest.mock import patch, MagicMock
from api_client import (
    FaultTolerantApiClient, 
    ApiClientConfig, 
    CircuitBreakerConfig, 
    RetryConfig, 
    TimeoutConfig,
    CircuitState
)

class TestFaultTolerantApiClient(unittest.TestCase):
    """Test cases for the FaultTolerantApiClient."""
    
    def setUp(self):
        """Set up a client for testing."""
        self.config = ApiClientConfig(
            base_url="https://api.example.com",
            circuit_breaker=CircuitBreakerConfig(
                failure_threshold=3,
                success_threshold=2,
                timeout_seconds=1  # Short timeout for testing
            ),
            retry=RetryConfig(
                max_retries=2,
                initial_backoff_ms=10,  # Short backoff for testing
                max_backoff_ms=100,
                backoff_multiplier=2.0,
                jitter_factor=0.1
            ),
            timeout=TimeoutConfig(
                connect_timeout_seconds=0.5,
                read_timeout_seconds=1.0
            )
        )
        self.client = FaultTolerantApiClient(self.config)
    
    @responses.activate
    def test_successful_request(self):
        """Test a successful API request."""
        # Mock a successful response
        responses.add(
            responses.GET,
            "https://api.example.com/users/123",
            json={"id": 123, "name": "Test User"},
            status=200
        )
        
        # Make the request
        result = self.client.get("users/123")
        
        # Verify the result
        self.assertEqual(result, {"id": 123, "name": "Test User"})
        
        # Check metrics
        metrics = self.client.get_metrics()
        self.assertEqual(metrics["total_requests"], 1)
        self.assertEqual(metrics["successful_requests"], 1)
        self.assertEqual(metrics["failed_requests"], 0)
        self.assertEqual(metrics["retry_count"], 0)
        
        # Check circuit state
        self.assertEqual(self.client.get_circuit_state(), CircuitState.CLOSED.value)
    
    @responses.activate
    def test_retry_on_server_error(self):
        """Test retry behavior on server errors."""
        # Mock a server error followed by a success
        responses.add(
            responses.GET,
            "https://api.example.com/users/123",
            json={"error": "Internal Server Error"},
            status=500
        )
        responses.add(
            responses.GET,
            "https://api.example.com/users/123",
            json={"id": 123, "name": "Test User"},
            status=200
        )
        
        # Make the request
        result = self.client.get("users/123")
        
        # Verify the result
        self.assertEqual(result, {"id": 123, "name": "Test User"})
        
        # Check metrics
        metrics = self.client.get_metrics()
        self.assertEqual(metrics["total_requests"], 1)
        self.assertEqual(metrics["successful_requests"], 1)
        self.assertEqual(metrics["failed_requests"], 0)
        self.assertEqual(metrics["retry_count"], 1)
    
    @responses.activate
    def test_fallback_after_max_retries(self):
        """Test fallback after exhausting retries."""
        # Mock multiple server errors
        for _ in range(3):  # Initial request + 2 retries
            responses.add(
                responses.GET,
                "https://api.example.com/users/123",
                json={"error": "Internal Server Error"},
                status=500
            )
        
        # Define a fallback function
        fallback_called = False
        def fallback():
            nonlocal fallback_called
            fallback_called = True
            return {"id": 123, "name": "Fallback User"}
        
        # Make the request with fallback
        result = self.client.get("users/123", fallback=fallback)
        
        # Verify the result
        self.assertEqual(result, {"id": 123, "name": "Fallback User"})
        self.assertTrue(fallback_called)
        
        # Check metrics
        metrics = self.client.get_metrics()
        self.assertEqual(metrics["total_requests"], 1)
        self.assertEqual(metrics["successful_requests"], 0)
        self.assertEqual(metrics["failed_requests"], 1)
        self.assertEqual(metrics["retry_count"], 2)
        self.assertEqual(metrics["fallback_count"], 1)
    
    @responses.activate
    def test_circuit_breaker_opens_after_failures(self):
        """Test that the circuit breaker opens after multiple failures."""
        # Mock multiple server errors
        for _ in range(5):  # More than failure_threshold
            responses.add(
                responses.GET,
                "https://api.example.com/users/123",
                json={"error": "Internal Server Error"},
                status=500
            )
        
        # Make requests until the circuit opens
        for _ in range(3):  # failure_threshold
            result = self.client.get("users/123")
            self.assertEqual(result, {"error": "Service unavailable", "message": "HTTP Error 500: Internal Server Error"})
        
        # Verify circuit is open
        self.assertEqual(self.client.get_circuit_state(), CircuitState.OPEN.value)
        
        # Next request should fail fast without calling the API
        before_count = len(responses.calls)
        result = self.client.get("users/123")
        after_count = len(responses.calls)
        
        # Verify no additional API call was made
        self.assertEqual(before_count, after_count)
        
        # Verify the result indicates circuit open
        self.assertEqual(result["error"], "Service unavailable")
        self.assertTrue("Circuit breaker open" in result["message"])
        
        # Check metrics
        metrics = self.client.get_metrics()
        self.assertEqual(metrics["circuit_open_count"], 1)
    
    @responses.activate
    def test_circuit_half_open_after_timeout(self):
        """Test that the circuit transitions to half-open after timeout."""
        # Mock multiple server errors
        for _ in range(5):
            responses.add(
                responses.GET,
                "https://api.example.com/users/123",
                json={"error": "Internal Server Error"},
                status=500
            )
        
        # Make requests until the circuit opens
        for _ in range(3):  # failure_threshold
            self.client.get("users/123")
        
        # Verify circuit is open
        self.assertEqual(self.client.get_circuit_state(), CircuitState.OPEN.value)
        
        # Wait for timeout
        time.sleep(1.1)  # Just over the timeout_seconds
        
        # Circuit should now be half-open
        self.assertEqual(self.client.get_circuit_state(), CircuitState.HALF_OPEN.value)
        
        # Add a successful response
        responses.add(
            responses.GET,
            "https://api.example.com/users/123",
            json={"id": 123, "name": "Test User"},
            status=200
        )
        
        # Make a request - should be allowed in half-open state
        result = self.client.get("users/123")
        self.assertEqual(result, {"id": 123, "name": "Test User"})
        
        # Add another successful response
        responses.add(
            responses.GET,
            "https://api.example.com/users/123",
            json={"id": 123, "name": "Test User"},
            status=200
        )
        
        # Make another request
        self.client.get("users/123")
        
        # Circuit should now be closed after success_threshold successes
        self.assertEqual(self.client.get_circuit_state(), CircuitState.CLOSED.value)
    
    @patch('requests.Session.request')
    def test_timeout_handling(self, mock_request):
        """Test handling of request timeouts."""
        # Mock a timeout
        mock_request.side_effect = requests.exceptions.Timeout("Request timed out")
        
        # Make the request
        result = self.client.get("users/123")
        
        # Verify the result
        self.assertEqual(result["error"], "Service unavailable")
        self.assertTrue("Request timed out" in result["message"])
        
        # Check metrics
        metrics = self.client.get_metrics()
        self.assertEqual(metrics["timeout_count"], 1)
    
    @responses.activate
    def test_different_http_methods(self):
        """Test that different HTTP methods work correctly."""
        # Mock responses for different methods
        responses.add(
            responses.POST,
            "https://api.example.com/users",
            json={"id": 123, "name": "New User"},
            status=201
        )
        responses.add(
            responses.PUT,
            "https://api.example.com/users/123",
            json={"id": 123, "name": "Updated User"},
            status=200
        )
        responses.add(
            responses.DELETE,
            "https://api.example.com/users/123",
            json={"status": "deleted"},
            status=200
        )
        
        # Test POST
        result = self.client.post("users", json={"name": "New User"})
        self.assertEqual(result, {"id": 123, "name": "New User"})
        
        # Test PUT
        result = self.client.put("users/123", json={"name": "Updated User"})
        self.assertEqual(result, {"id": 123, "name": "Updated User"})
        
        # Test DELETE
        result = self.client.delete("users/123")
        self.assertEqual(result, {"status": "deleted"})
    
    def test_reset_metrics(self):
        """Test resetting metrics."""
        # Update some metrics
        with self.client.metrics_lock:
            self.client.metrics["total_requests"] = 10
            self.client.metrics["successful_requests"] = 8
            self.client.metrics["failed_requests"] = 2
        
        # Reset metrics
        self.client.reset_metrics()
        
        # Verify all metrics are zero
        metrics = self.client.get_metrics()
        for key, value in metrics.items():
            self.assertEqual(value, 0, f"Metric {key} should be 0 after reset")


if __name__ == "__main__":
    unittest.main()
</file>

<file path="system_design_guide/01_Foundation/code/scalable_counter/counter_api.py">
"""
Scalable Counter Service REST API

This module implements a REST API for the scalable counter service using Flask.
"""
from flask import Flask, request, jsonify
from counter_service import CounterService
import time
import os

app = Flask(__name__)
# Use environment variables for Redis connection
counter_service = CounterService()

@app.route('/counters/<counter_name>', methods=['POST'])
def increment_counter(counter_name):
    """
    Increment a counter.

    Request body (optional):
    {
        "amount": 5,  # Amount to increment (default: 1)
        "ttl": 3600   # Time-to-live in seconds (optional)
    }
    """
    data = request.json or {}
    amount = data.get('amount', 1)
    ttl = data.get('ttl')

    try:
        if ttl:
            new_value = counter_service.increment_with_expiry(counter_name, amount, ttl)
        else:
            new_value = counter_service.increment(counter_name, amount)

        return jsonify({
            'counter': counter_name,
            'value': new_value,
            'operation': 'increment',
            'amount': amount
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/counters/<counter_name>', methods=['GET'])
def get_counter(counter_name):
    """Get the current value of a counter."""
    try:
        value = counter_service.get(counter_name)
        return jsonify({
            'counter': counter_name,
            'value': value
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/counters/<counter_name>', methods=['DELETE'])
def reset_counter(counter_name):
    """Reset a counter to zero."""
    try:
        counter_service.reset(counter_name)
        return jsonify({
            'counter': counter_name,
            'value': 0,
            'operation': 'reset'
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/counters/<counter_name>/reset-and-get', methods=['POST'])
def get_and_reset(counter_name):
    """Get the current value of a counter and reset it to zero."""
    try:
        value = counter_service.get_and_reset(counter_name)
        return jsonify({
            'counter': counter_name,
            'value': value,
            'operation': 'get_and_reset'
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/counters', methods=['GET'])
def get_all_counters():
    """Get all counters and their values."""
    try:
        counters = counter_service.get_all_counters()
        return jsonify({
            'counters': counters,
            'count': len(counters)
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/counters/batch', methods=['POST'])
def batch_operations():
    """
    Perform batch operations on counters.

    Request body:
    {
        "increment": ["counter1", "counter2"],  # Counters to increment
        "get": ["counter3", "counter4"]         # Counters to get
    }
    """
    data = request.json or {}

    if not data:
        return jsonify({'error': 'Request body is required'}), 400

    result = {}

    # Process increment batch
    if 'increment' in data:
        increment_counters = data['increment']
        if increment_counters:
            try:
                incremented = counter_service.increment_batch(increment_counters)
                result['incremented'] = incremented
            except Exception as e:
                result['increment_error'] = str(e)

    # Process get batch
    if 'get' in data:
        get_counters = data['get']
        if get_counters:
            try:
                values = counter_service.get_batch(get_counters)
                result['values'] = values
            except Exception as e:
                result['get_error'] = str(e)

    return jsonify(result)

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint."""
    try:
        # Try a simple Redis operation to check connectivity
        counter_service.redis_client.ping()
        return jsonify({
            'status': 'healthy',
            'timestamp': time.time()
        })
    except Exception as e:
        return jsonify({
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': time.time()
        }), 500

@app.route('/load-test', methods=['POST'])
def run_load_test():
    """
    Run a load test on the counter service.

    Request body:
    {
        "threads": 10,              # Number of threads
        "operations_per_thread": 1000  # Operations per thread
    }
    """
    from counter_service import load_test
    import threading

    data = request.json or {}
    threads = data.get('threads', 5)
    operations = data.get('operations_per_thread', 100)

    # Run load test in a separate thread to not block the response
    def run_test():
        load_test(counter_service, threads, operations)

    thread = threading.Thread(target=run_test)
    thread.start()

    return jsonify({
        'message': 'Load test started',
        'threads': threads,
        'operations_per_thread': operations,
        'total_operations': threads * operations
    })

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)
</file>

<file path="system_design_guide/01_Foundation/code/scalable_counter/counter_service.py">
"""
Scalable Counter Service

This module implements a scalable counter service that can:
1. Increment counters
2. Retrieve counter values
3. Handle high throughput (thousands of increments per second)
4. Maintain accuracy under concurrent load

The implementation uses Redis for atomic operations and persistence.
"""
import redis
import time
import threading
import random
import os
from typing import Dict, List, Optional, Union, Tuple

class CounterService:
    """A scalable counter service using Redis."""

    def __init__(self, host: str = None, port: int = None, db: int = 0):
        """
        Initialize the counter service with Redis connection.

        Args:
            host: Redis host (defaults to REDIS_HOST env var or 'localhost')
            port: Redis port (defaults to REDIS_PORT env var or 6379)
            db: Redis database number
        """
        # Get connection details from environment variables or use defaults
        redis_host = host or os.environ.get('REDIS_HOST', 'localhost')
        redis_port = port or int(os.environ.get('REDIS_PORT', 6379))

        self.redis_client = redis.Redis(host=redis_host, port=redis_port, db=db)
        self.counter_prefix = "counter:"
        self.lock_prefix = "lock:"

    def increment(self, counter_name: str, amount: int = 1) -> int:
        """
        Increment a counter by the specified amount.

        Args:
            counter_name: Name of the counter
            amount: Amount to increment (default: 1)

        Returns:
            The new counter value
        """
        key = f"{self.counter_prefix}{counter_name}"
        return self.redis_client.incrby(key, amount)

    def get(self, counter_name: str) -> int:
        """
        Get the current value of a counter.

        Args:
            counter_name: Name of the counter

        Returns:
            The current counter value, or 0 if the counter doesn't exist
        """
        key = f"{self.counter_prefix}{counter_name}"
        value = self.redis_client.get(key)
        return int(value) if value else 0

    def reset(self, counter_name: str) -> bool:
        """
        Reset a counter to zero.

        Args:
            counter_name: Name of the counter

        Returns:
            True if the counter was reset, False otherwise
        """
        key = f"{self.counter_prefix}{counter_name}"
        return bool(self.redis_client.set(key, 0))

    def increment_with_lock(self, counter_name: str, amount: int = 1,
                           timeout: int = 10) -> Optional[int]:
        """
        Increment a counter with distributed locking for additional safety.

        Args:
            counter_name: Name of the counter
            amount: Amount to increment (default: 1)
            timeout: Lock timeout in seconds (default: 10)

        Returns:
            The new counter value, or None if the lock couldn't be acquired
        """
        counter_key = f"{self.counter_prefix}{counter_name}"
        lock_key = f"{self.lock_prefix}{counter_name}"

        # Try to acquire the lock
        lock_value = str(time.time())
        acquired = self.redis_client.setnx(lock_key, lock_value)

        if acquired:
            try:
                # Set lock expiration to prevent deadlocks
                self.redis_client.expire(lock_key, timeout)

                # Get current value
                current = self.redis_client.get(counter_key)
                current = int(current) if current else 0

                # Increment
                new_value = current + amount
                self.redis_client.set(counter_key, new_value)

                return new_value
            finally:
                # Release the lock if we still own it
                if self.redis_client.get(lock_key) == lock_value.encode():
                    self.redis_client.delete(lock_key)

        return None

    def increment_batch(self, counter_names: List[str], amount: int = 1) -> Dict[str, int]:
        """
        Increment multiple counters in a single batch operation.

        Args:
            counter_names: List of counter names
            amount: Amount to increment each counter (default: 1)

        Returns:
            Dictionary mapping counter names to their new values
        """
        pipeline = self.redis_client.pipeline()

        # Queue all increments
        for name in counter_names:
            key = f"{self.counter_prefix}{name}"
            pipeline.incrby(key, amount)

        # Execute all commands in a single round-trip
        results = pipeline.execute()

        # Map results back to counter names
        return dict(zip(counter_names, results))

    def get_batch(self, counter_names: List[str]) -> Dict[str, int]:
        """
        Get multiple counter values in a single batch operation.

        Args:
            counter_names: List of counter names

        Returns:
            Dictionary mapping counter names to their values
        """
        pipeline = self.redis_client.pipeline()

        # Queue all get operations
        for name in counter_names:
            key = f"{self.counter_prefix}{name}"
            pipeline.get(key)

        # Execute all commands in a single round-trip
        results = pipeline.execute()

        # Convert results to integers and handle None values
        processed_results = [int(r) if r else 0 for r in results]

        # Map results back to counter names
        return dict(zip(counter_names, processed_results))

    def get_all_counters(self) -> Dict[str, int]:
        """
        Get all counters and their values.

        Returns:
            Dictionary mapping counter names to their values
        """
        # Get all counter keys
        pattern = f"{self.counter_prefix}*"
        keys = self.redis_client.keys(pattern)

        if not keys:
            return {}

        # Get all values in a single batch
        values = self.redis_client.mget(keys)

        # Process results
        result = {}
        for key, value in zip(keys, values):
            # Extract counter name from key
            counter_name = key.decode('utf-8').replace(self.counter_prefix, '')
            result[counter_name] = int(value) if value else 0

        return result

    def increment_with_expiry(self, counter_name: str, amount: int = 1,
                             ttl_seconds: int = 3600) -> int:
        """
        Increment a counter and set an expiration time.

        Args:
            counter_name: Name of the counter
            amount: Amount to increment (default: 1)
            ttl_seconds: Time-to-live in seconds (default: 1 hour)

        Returns:
            The new counter value
        """
        key = f"{self.counter_prefix}{counter_name}"

        # Use a pipeline for atomicity
        pipeline = self.redis_client.pipeline()
        pipeline.incrby(key, amount)
        pipeline.expire(key, ttl_seconds)
        results = pipeline.execute()

        return results[0]  # Return the new counter value

    def get_and_reset(self, counter_name: str) -> int:
        """
        Get the current value of a counter and reset it to zero atomically.

        Args:
            counter_name: Name of the counter

        Returns:
            The counter value before reset
        """
        key = f"{self.counter_prefix}{counter_name}"

        # Use Lua script for atomicity
        script = """
        local current = redis.call('get', KEYS[1])
        redis.call('set', KEYS[1], 0)
        return current
        """

        result = self.redis_client.eval(script, 1, key)
        return int(result) if result else 0


# Example usage and load testing
def load_test(service, num_threads=10, operations_per_thread=1000):
    """
    Run a load test on the counter service.

    Args:
        service: CounterService instance
        num_threads: Number of concurrent threads
        operations_per_thread: Number of operations per thread
    """
    counters = ['users', 'visits', 'clicks', 'purchases', 'errors']

    def worker():
        """Worker function for each thread."""
        for _ in range(operations_per_thread):
            # Randomly choose an operation
            op = random.choice(['increment', 'get', 'batch'])

            if op == 'increment':
                # Increment a random counter
                counter = random.choice(counters)
                service.increment(counter)
            elif op == 'get':
                # Get a random counter
                counter = random.choice(counters)
                service.get(counter)
            elif op == 'batch':
                # Batch increment multiple counters
                batch_size = random.randint(1, len(counters))
                batch_counters = random.sample(counters, batch_size)
                service.increment_batch(batch_counters)

    # Create and start threads
    threads = []
    start_time = time.time()

    for _ in range(num_threads):
        thread = threading.Thread(target=worker)
        threads.append(thread)
        thread.start()

    # Wait for all threads to complete
    for thread in threads:
        thread.join()

    end_time = time.time()

    # Calculate and print results
    total_operations = num_threads * operations_per_thread
    elapsed_time = end_time - start_time
    ops_per_second = total_operations / elapsed_time

    print(f"Load test completed:")
    print(f"- Threads: {num_threads}")
    print(f"- Operations per thread: {operations_per_thread}")
    print(f"- Total operations: {total_operations}")
    print(f"- Elapsed time: {elapsed_time:.2f} seconds")
    print(f"- Operations per second: {ops_per_second:.2f}")

    # Print final counter values
    print("\nFinal counter values:")
    for counter, value in service.get_all_counters().items():
        print(f"- {counter}: {value}")


if __name__ == "__main__":
    # Create counter service
    counter_service = CounterService()

    # Reset counters for clean test
    for counter in ['users', 'visits', 'clicks', 'purchases', 'errors']:
        counter_service.reset(counter)

    # Run load test
    load_test(counter_service, num_threads=10, operations_per_thread=1000)
</file>

<file path="system_design_guide/01_Foundation/code/scalable_counter/docker-compose.yml">
version: '3'

services:
  redis:
    image: redis:6.2
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: always
    networks:
      - counter_network

  counter_api:
    build: .
    ports:
      - "5000:5000"
    depends_on:
      - redis
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - FLASK_ENV=production
    restart: always
    networks:
      - counter_network
    deploy:
      replicas: 2
      restart_policy:
        condition: on-failure
        max_attempts: 3
      update_config:
        parallelism: 1
        delay: 10s

networks:
  counter_network:
    driver: bridge

volumes:
  redis_data:
</file>

<file path="system_design_guide/01_Foundation/code/scalable_counter/Dockerfile">
FROM python:3.9-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY counter_service.py counter_api.py ./

EXPOSE 5000

CMD ["python", "counter_api.py"]
</file>

<file path="system_design_guide/01_Foundation/code/scalable_counter/load_test.py">
"""
Load Testing Script for Scalable Counter Service

This script performs load testing on the counter service to demonstrate its scalability.
"""
import requests
import threading
import time
import random
import argparse
from concurrent.futures import ThreadPoolExecutor
import matplotlib.pyplot as plt
import numpy as np

def increment_counter(base_url, counter_name):
    """Increment a counter."""
    try:
        response = requests.post(f"{base_url}/counters/{counter_name}")
        return response.status_code == 200
    except Exception as e:
        print(f"Error incrementing counter: {e}")
        return False

def get_counter(base_url, counter_name):
    """Get a counter value."""
    try:
        response = requests.get(f"{base_url}/counters/{counter_name}")
        return response.status_code == 200
    except Exception as e:
        print(f"Error getting counter: {e}")
        return False

def batch_operation(base_url, counter_names):
    """Perform a batch operation."""
    try:
        data = {
            "increment": random.sample(counter_names, random.randint(1, len(counter_names))),
            "get": random.sample(counter_names, random.randint(1, len(counter_names)))
        }
        response = requests.post(f"{base_url}/counters/batch", json=data)
        return response.status_code == 200
    except Exception as e:
        print(f"Error in batch operation: {e}")
        return False

def worker(base_url, counter_names, num_operations):
    """Worker function that performs random operations."""
    operations = ["increment", "get", "batch"]
    results = {"success": 0, "failure": 0}
    
    for _ in range(num_operations):
        op = random.choice(operations)
        success = False
        
        if op == "increment":
            counter = random.choice(counter_names)
            success = increment_counter(base_url, counter)
        elif op == "get":
            counter = random.choice(counter_names)
            success = get_counter(base_url, counter)
        elif op == "batch":
            success = batch_operation(base_url, counter_names)
        
        if success:
            results["success"] += 1
        else:
            results["failure"] += 1
    
    return results

def run_load_test(base_url, num_threads, operations_per_thread, counter_names=None):
    """
    Run a load test with multiple threads.
    
    Args:
        base_url: Base URL of the counter service API
        num_threads: Number of concurrent threads
        operations_per_thread: Number of operations per thread
        counter_names: List of counter names to use (default: generates random names)
    
    Returns:
        Dictionary with test results
    """
    if counter_names is None:
        counter_names = [f"test_counter_{i}" for i in range(10)]
    
    # Reset counters
    for counter in counter_names:
        requests.delete(f"{base_url}/counters/{counter}")
    
    start_time = time.time()
    results = {"success": 0, "failure": 0}
    
    with ThreadPoolExecutor(max_workers=num_threads) as executor:
        futures = []
        for _ in range(num_threads):
            future = executor.submit(worker, base_url, counter_names, operations_per_thread)
            futures.append(future)
        
        for future in futures:
            worker_results = future.result()
            results["success"] += worker_results["success"]
            results["failure"] += worker_results["failure"]
    
    end_time = time.time()
    elapsed_time = end_time - start_time
    
    # Get final counter values
    counter_values = {}
    for counter in counter_names:
        response = requests.get(f"{base_url}/counters/{counter}")
        if response.status_code == 200:
            counter_values[counter] = response.json().get("value", 0)
    
    return {
        "elapsed_time": elapsed_time,
        "operations": results["success"] + results["failure"],
        "success_rate": results["success"] / (results["success"] + results["failure"]) * 100,
        "operations_per_second": (results["success"] + results["failure"]) / elapsed_time,
        "counter_values": counter_values
    }

def run_scalability_test(base_url, max_threads, operations_per_thread=100):
    """
    Run a scalability test with increasing thread counts.
    
    Args:
        base_url: Base URL of the counter service API
        max_threads: Maximum number of threads to test
        operations_per_thread: Number of operations per thread
    
    Returns:
        Dictionary with test results for each thread count
    """
    thread_counts = [1, 2, 5, 10, 20, 50, 100, 200, 500]
    thread_counts = [t for t in thread_counts if t <= max_threads]
    
    counter_names = [f"scalability_test_{i}" for i in range(10)]
    results = {}
    
    for thread_count in thread_counts:
        print(f"Testing with {thread_count} threads...")
        test_result = run_load_test(base_url, thread_count, operations_per_thread, counter_names)
        results[thread_count] = test_result
        print(f"  Operations per second: {test_result['operations_per_second']:.2f}")
        print(f"  Success rate: {test_result['success_rate']:.2f}%")
        print()
    
    return results

def plot_results(results):
    """
    Plot the results of a scalability test.
    
    Args:
        results: Dictionary with test results for each thread count
    """
    thread_counts = sorted(results.keys())
    ops_per_second = [results[t]["operations_per_second"] for t in thread_counts]
    
    plt.figure(figsize=(10, 6))
    
    # Plot operations per second
    plt.subplot(2, 1, 1)
    plt.plot(thread_counts, ops_per_second, 'o-', linewidth=2)
    plt.xlabel('Number of Threads')
    plt.ylabel('Operations per Second')
    plt.title('Scalability Test Results')
    plt.grid(True)
    
    # Plot efficiency (ops per second per thread)
    plt.subplot(2, 1, 2)
    efficiency = [ops / threads for ops, threads in zip(ops_per_second, thread_counts)]
    plt.plot(thread_counts, efficiency, 'o-', linewidth=2)
    plt.xlabel('Number of Threads')
    plt.ylabel('Efficiency (Ops/s per Thread)')
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig('scalability_results.png')
    plt.close()
    
    print(f"Results plot saved to scalability_results.png")

def main():
    parser = argparse.ArgumentParser(description='Load test for Scalable Counter Service')
    parser.add_argument('--url', default='http://localhost:5000', help='Base URL of the counter service API')
    parser.add_argument('--threads', type=int, default=10, help='Number of concurrent threads')
    parser.add_argument('--operations', type=int, default=100, help='Operations per thread')
    parser.add_argument('--scalability-test', action='store_true', help='Run scalability test with increasing thread counts')
    parser.add_argument('--max-threads', type=int, default=100, help='Maximum number of threads for scalability test')
    
    args = parser.parse_args()
    
    if args.scalability_test:
        print(f"Running scalability test with up to {args.max_threads} threads...")
        results = run_scalability_test(args.url, args.max_threads, args.operations)
        plot_results(results)
    else:
        print(f"Running load test with {args.threads} threads, {args.operations} operations per thread...")
        results = run_load_test(args.url, args.threads, args.operations)
        
        print("\nLoad Test Results:")
        print(f"Elapsed time: {results['elapsed_time']:.2f} seconds")
        print(f"Total operations: {results['operations']}")
        print(f"Operations per second: {results['operations_per_second']:.2f}")
        print(f"Success rate: {results['success_rate']:.2f}%")
        
        print("\nFinal counter values:")
        for counter, value in results['counter_values'].items():
            print(f"  {counter}: {value}")

if __name__ == "__main__":
    main()
</file>

<file path="system_design_guide/01_Foundation/code/scalable_counter/README.md">
# Scalable Counter Service

This is a practical implementation of a scalable counter service that demonstrates the scalability concepts covered in Module 1.

## Features

- Increment counters with atomic operations
- Retrieve counter values
- Batch operations for efficiency
- Distributed locking for additional safety
- Time-based expiration for counters
- Atomic get-and-reset operations
- REST API for easy integration
- Load testing capabilities

## Components

### 1. CounterService

Core implementation of the counter service using Redis for:
- Atomic operations
- Persistence
- High throughput
- Distributed access

### 2. REST API

Flask-based REST API that exposes the counter service functionality:
- Increment counters
- Get counter values
- Reset counters
- Batch operations
- Health checks
- Load testing

## Scalability Features

### 1. Horizontal Scaling

- Stateless API design allows multiple instances
- Redis handles the shared state
- No instance-specific data

### 2. Efficient Operations

- Batch processing to reduce network overhead
- Pipelining for atomic multi-step operations
- Lua scripts for complex atomic operations

### 3. Performance Optimization

- Minimized round-trips to Redis
- Efficient data structures
- Optimized key naming strategy

### 4. Resilience

- Timeout mechanisms to prevent deadlocks
- Error handling and recovery
- Health check endpoint

## Requirements

- Python 3.6+
- Redis server
- Flask
- redis-py

## Installation

```bash
pip install redis flask
```

## Running the Service

1. Start Redis server:
   ```bash
   redis-server
   ```

2. Run the counter service API:
   ```bash
   python counter_api.py
   ```

## API Endpoints

### Increment a Counter
```
POST /counters/<counter_name>

Request body (optional):
{
    "amount": 5,  # Amount to increment (default: 1)
    "ttl": 3600   # Time-to-live in seconds (optional)
}
```

### Get a Counter Value
```
GET /counters/<counter_name>
```

### Reset a Counter
```
DELETE /counters/<counter_name>
```

### Get and Reset a Counter
```
POST /counters/<counter_name>/reset-and-get
```

### Get All Counters
```
GET /counters
```

### Batch Operations
```
POST /counters/batch

Request body:
{
    "increment": ["counter1", "counter2"],  # Counters to increment
    "get": ["counter3", "counter4"]         # Counters to get
}
```

### Health Check
```
GET /health
```

### Run Load Test
```
POST /load-test

Request body:
{
    "threads": 10,              # Number of threads
    "operations_per_thread": 1000  # Operations per thread
}
```

## Load Testing

The service includes a built-in load testing function that:
1. Creates multiple threads to simulate concurrent users
2. Performs random operations (increment, get, batch)
3. Measures throughput (operations per second)
4. Reports final counter values

## Scaling Considerations

### Redis Scaling

For production use, consider:
- Redis Cluster for horizontal scaling
- Redis Sentinel for high availability
- Redis Enterprise for managed scaling

### API Scaling

For production use, consider:
- Multiple API instances behind a load balancer
- Containerization with Docker
- Orchestration with Kubernetes
- Auto-scaling based on load

## Exercise

Try extending this implementation with the following features:
1. Authentication and authorization
2. Rate limiting to prevent abuse
3. Monitoring and alerting
4. Sharded counters for extreme scale
5. Multi-datacenter replication

## Next Steps

After understanding this implementation, consider how you would modify it to handle:
1. Billions of counters
2. Millions of operations per second
3. Global distribution with low latency
4. Five-nines availability requirements
</file>

<file path="system_design_guide/01_Foundation/code/scalable_counter/requirements.txt">
flask==2.0.1
redis==4.3.4
gunicorn==20.1.0
</file>

<file path="system_design_guide/01_Foundation/code/url_shortener/app.py">
"""
Flask web application for the URL Shortener.

This demonstrates how to use the URL Shortener components in a web application.
"""
from flask import Flask, request, jsonify, redirect, render_template_string
from url_shortener import URLRepository, URLService, URLShortenerAPI

app = Flask(__name__)

# Set up the components
repository = URLRepository()
url_service = URLService(repository, base_url="http://localhost:5000/")
api = URLShortenerAPI(url_service)

# HTML template for the home page
HOME_TEMPLATE = """
<!DOCTYPE html>
<html>
<head>
    <title>URL Shortener</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            background-color: #f9f9f9;
            border-radius: 5px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #333;
        }
        input, button {
            padding: 10px;
            margin: 5px 0;
        }
        input[type="text"] {
            width: 100%;
            box-sizing: border-box;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
            border-radius: 3px;
        }
        button:hover {
            background-color: #45a049;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            background-color: #e9f7ef;
            border-radius: 3px;
        }
        .error {
            color: #e74c3c;
        }
        .stats {
            margin-top: 30px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>URL Shortener</h1>
        
        <form id="shortenForm">
            <div>
                <label for="url">URL to shorten:</label>
                <input type="text" id="url" name="url" placeholder="https://example.com" required>
            </div>
            
            <button type="submit">Shorten URL</button>
        </form>
        
        <div id="result" class="result" style="display: none;"></div>
        
        {% if short_code %}
        <div class="stats">
            <h2>URL Statistics</h2>
            <table>
                <tr>
                    <th>Original URL</th>
                    <td>{{ stats.original_url }}</td>
                </tr>
                <tr>
                    <th>Created At</th>
                    <td>{{ stats.created_at }}</td>
                </tr>
                <tr>
                    <th>Clicks</th>
                    <td>{{ stats.clicks }}</td>
                </tr>
            </table>
        </div>
        {% endif %}
    </div>
    
    <script>
        document.getElementById('shortenForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const url = document.getElementById('url').value;
            
            fetch('/api/shorten', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    url: url
                }),
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('result');
                resultDiv.style.display = 'block';
                
                if (data.error) {
                    resultDiv.innerHTML = `<p class="error">Error: ${data.error}</p>`;
                } else {
                    resultDiv.innerHTML = `
                        <p>Shortened URL: <a href="${data.short_url}" target="_blank">${data.short_url}</a></p>
                    `;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                const resultDiv = document.getElementById('result');
                resultDiv.style.display = 'block';
                resultDiv.innerHTML = `<p class="error">Error: ${error.message}</p>`;
            });
        });
    </script>
</body>
</html>
"""

@app.route('/')
def home():
    """Render the home page."""
    short_code = request.args.get('code')
    stats = None
    
    if short_code:
        stats = api.get_url_stats(short_code)
        if 'error' in stats:
            stats = None
    
    return render_template_string(HOME_TEMPLATE, short_code=short_code, stats=stats)

@app.route('/<short_code>')
def redirect_to_url(short_code):
    """Redirect to the original URL."""
    result = api.redirect_to_original(short_code)
    
    if 'error' in result:
        return jsonify(result), 404
    
    return redirect(result['redirect_to'])

@app.route('/api/shorten', methods=['POST'])
def shorten_url():
    """API endpoint to shorten a URL."""
    data = request.json
    url = data.get('url')
    
    if not url:
        return jsonify({'error': 'URL is required'}), 400
    
    result = api.shorten_url(url)
    
    if 'error' in result:
        return jsonify(result), 400
    
    return jsonify(result)

@app.route('/api/stats/<short_code>')
def get_stats(short_code):
    """API endpoint to get URL statistics."""
    result = api.get_url_stats(short_code)
    
    if 'error' in result:
        return jsonify(result), 404
    
    return jsonify(result)

if __name__ == '__main__':
    app.run(debug=True)
</file>

<file path="system_design_guide/01_Foundation/code/url_shortener/README.md">
# URL Shortener Implementation

This is a practical implementation of a URL shortener service that demonstrates the basic system design principles covered in Module 1.

## Design Principles Demonstrated

1. **Separation of Concerns**
   - Data Access Layer (URLRepository)
   - Service Layer (URLService)
   - API Layer (URLShortenerAPI)
   - Web Application Layer (Flask app)

2. **Single Responsibility Principle**
   - Each class has a specific role and responsibility
   - URLRepository handles database operations
   - URLService contains business logic
   - URLShortenerAPI handles API requests

3. **KISS (Keep It Simple, Stupid)**
   - Simple, straightforward implementation
   - No unnecessary complexity
   - Clear, readable code

4. **DRY (Don't Repeat Yourself)**
   - Reusable components
   - Centralized logic for URL validation, short code generation, etc.

5. **Loose Coupling**
   - Components interact through well-defined interfaces
   - Dependencies are injected rather than hardcoded

## Components

### 1. URLRepository

Handles all database operations:
- Creating tables
- Saving URL mappings
- Retrieving original URLs
- Recording click statistics
- Retrieving URL statistics

### 2. URLService

Contains business logic:
- Generating short codes
- Creating short URLs
- Retrieving original URLs
- Managing click tracking

### 3. URLShortenerAPI

Handles API requests:
- URL validation
- Error handling
- Response formatting

### 4. Flask Web Application

Provides a web interface:
- HTML form for URL shortening
- API endpoints for programmatic access
- Redirection from short URLs to original URLs
- Statistics display

## How to Run

1. Install the required dependencies:
   ```
   pip install flask
   ```

2. Run the Flask application:
   ```
   python app.py
   ```

3. Open your browser and navigate to `http://localhost:5000/`

## API Endpoints

- `POST /api/shorten` - Shorten a URL
  - Request body: `{"url": "https://example.com"}`
  - Response: `{"short_url": "http://localhost:5000/abc123"}`

- `GET /<short_code>` - Redirect to the original URL

- `GET /api/stats/<short_code>` - Get statistics for a short URL
  - Response: `{"original_url": "https://example.com", "created_at": "...", "clicks": 5}`

## Database Schema

### url_mappings Table
- `short_code` (TEXT, PRIMARY KEY) - The unique short code
- `original_url` (TEXT) - The original URL
- `created_at` (TIMESTAMP) - When the mapping was created

### click_stats Table
- `id` (INTEGER, PRIMARY KEY) - Auto-incrementing ID
- `short_code` (TEXT, FOREIGN KEY) - The short code that was clicked
- `clicked_at` (TIMESTAMP) - When the click occurred

## Scaling Considerations

This implementation is designed for educational purposes and has several limitations for production use:

1. **Database**: Uses SQLite, which is not suitable for high-concurrency environments. In production, consider:
   - PostgreSQL or MySQL for relational needs
   - Redis for caching frequently accessed URLs
   - NoSQL solutions like MongoDB for horizontal scaling

2. **Short Code Generation**: Uses random generation, which could lead to collisions. In production, consider:
   - Base62 encoding of incremental IDs
   - Distributed ID generation (e.g., Snowflake algorithm)

3. **Caching**: No caching implementation. In production, consider:
   - In-memory cache for hot URLs
   - Distributed cache like Redis or Memcached

4. **Load Balancing**: Single instance only. In production, consider:
   - Multiple application instances
   - Load balancer (e.g., Nginx, HAProxy)
   - Stateless design for horizontal scaling

## Exercise

Try extending this implementation with the following features:
1. Custom short codes (allow users to specify their own short code)
2. Expiration dates for URLs
3. User authentication and URL management
4. Analytics dashboard with more detailed statistics
5. Rate limiting to prevent abuse

## Next Steps

After understanding this implementation, consider how you would modify it to handle:
1. Millions of URLs
2. Thousands of requests per second
3. Global distribution with low latency
4. High availability requirements
</file>

<file path="system_design_guide/01_Foundation/code/url_shortener/url_shortener.py">
"""
URL Shortener Implementation

This module implements a simple URL shortener service following the principles of:
- Separation of Concerns
- Single Responsibility
- KISS (Keep It Simple, Stupid)
- DRY (Don't Repeat Yourself)
"""
import random
import string
import sqlite3
from datetime import datetime
from typing import Dict, Optional, Tuple, Any

# 1. Data Access Layer
class URLRepository:
    """Handles all database operations for URL mappings."""
    
    def __init__(self, db_path: str = "url_shortener.db"):
        """Initialize the repository with a database connection."""
        self.conn = sqlite3.connect(db_path)
        self._create_tables()
    
    def _create_tables(self) -> None:
        """Create necessary tables if they don't exist."""
        cursor = self.conn.cursor()
        
        # URL mappings table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS url_mappings (
            short_code TEXT PRIMARY KEY,
            original_url TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        # Click statistics table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS click_stats (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            short_code TEXT,
            clicked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (short_code) REFERENCES url_mappings (short_code)
        )
        ''')
        
        self.conn.commit()
    
    def save_url_mapping(self, short_code: str, original_url: str) -> None:
        """Save a new URL mapping."""
        cursor = self.conn.cursor()
        cursor.execute(
            "INSERT INTO url_mappings (short_code, original_url) VALUES (?, ?)",
            (short_code, original_url)
        )
        self.conn.commit()
    
    def get_original_url(self, short_code: str) -> Optional[str]:
        """Retrieve the original URL for a given short code."""
        cursor = self.conn.cursor()
        result = cursor.execute(
            "SELECT original_url FROM url_mappings WHERE short_code = ?",
            (short_code,)
        ).fetchone()
        
        return result[0] if result else None
    
    def record_click(self, short_code: str) -> None:
        """Record a click event for a short URL."""
        cursor = self.conn.cursor()
        cursor.execute(
            "INSERT INTO click_stats (short_code) VALUES (?)",
            (short_code,)
        )
        self.conn.commit()
    
    def get_click_count(self, short_code: str) -> int:
        """Get the number of clicks for a short URL."""
        cursor = self.conn.cursor()
        result = cursor.execute(
            "SELECT COUNT(*) FROM click_stats WHERE short_code = ?",
            (short_code,)
        ).fetchone()
        
        return result[0] if result else 0
    
    def get_url_stats(self, short_code: str) -> Optional[Dict[str, Any]]:
        """Get comprehensive statistics for a short URL."""
        cursor = self.conn.cursor()
        result = cursor.execute(
            """
            SELECT m.original_url, m.created_at, COUNT(s.id) as clicks
            FROM url_mappings m
            LEFT JOIN click_stats s ON m.short_code = s.short_code
            WHERE m.short_code = ?
            GROUP BY m.short_code
            """,
            (short_code,)
        ).fetchone()
        
        if not result:
            return None
        
        return {
            "original_url": result[0],
            "created_at": result[1],
            "clicks": result[2]
        }
    
    def close(self) -> None:
        """Close the database connection."""
        self.conn.close()

# 2. Service Layer
class URLService:
    """Contains business logic for URL shortening."""
    
    def __init__(self, repository: URLRepository, base_url: str = "http://short.url/"):
        """Initialize the service with a repository and base URL."""
        self.repository = repository
        self.base_url = base_url
    
    def create_short_url(self, original_url: str) -> str:
        """Create a short URL for the given original URL."""
        short_code = self._generate_short_code()
        
        # Ensure the short code is unique
        while self.repository.get_original_url(short_code) is not None:
            short_code = self._generate_short_code()
        
        self.repository.save_url_mapping(short_code, original_url)
        
        return f"{self.base_url}{short_code}"
    
    def get_original_url(self, short_code: str) -> Optional[str]:
        """Get the original URL for a given short code."""
        return self.repository.get_original_url(short_code)
    
    def record_click(self, short_code: str) -> None:
        """Record a click for a short URL."""
        self.repository.record_click(short_code)
    
    def get_url_stats(self, short_code: str) -> Optional[Dict[str, Any]]:
        """Get statistics for a short URL."""
        return self.repository.get_url_stats(short_code)
    
    def _generate_short_code(self, length: int = 6) -> str:
        """Generate a random short code."""
        characters = string.ascii_letters + string.digits
        return ''.join(random.choice(characters) for _ in range(length))

# 3. API Layer (simplified for demonstration)
class URLShortenerAPI:
    """Handles API requests for URL shortening."""
    
    def __init__(self, url_service: URLService):
        """Initialize the API with a URL service."""
        self.url_service = url_service
    
    def shorten_url(self, original_url: str) -> Dict[str, str]:
        """Shorten a URL and return the result."""
        if not original_url:
            return {"error": "URL is required"}
        
        if not self._is_valid_url(original_url):
            return {"error": "Invalid URL format"}
        
        short_url = self.url_service.create_short_url(original_url)
        return {"short_url": short_url}
    
    def redirect_to_original(self, short_code: str) -> Dict[str, str]:
        """Get redirection information for a short code."""
        original_url = self.url_service.get_original_url(short_code)
        
        if not original_url:
            return {"error": "URL not found"}
        
        # Record the click
        self.url_service.record_click(short_code)
        
        return {"redirect_to": original_url}
    
    def get_url_stats(self, short_code: str) -> Dict[str, Any]:
        """Get statistics for a short URL."""
        stats = self.url_service.get_url_stats(short_code)
        
        if not stats:
            return {"error": "URL not found"}
        
        return stats
    
    def _is_valid_url(self, url: str) -> bool:
        """Validate URL format (simplified)."""
        return url.startswith(('http://', 'https://'))

# Example usage
def main():
    # Set up the components
    repository = URLRepository()
    url_service = URLService(repository)
    api = URLShortenerAPI(url_service)
    
    # Example: Shorten a URL
    result = api.shorten_url("https://example.com/very/long/url/that/needs/shortening")
    print(f"Shortened URL: {result.get('short_url')}")
    
    # Extract the short code from the result
    short_code = result.get('short_url').split('/')[-1]
    
    # Example: Redirect to original URL
    redirect_result = api.redirect_to_original(short_code)
    print(f"Redirecting to: {redirect_result.get('redirect_to')}")
    
    # Example: Get URL statistics
    stats_result = api.get_url_stats(short_code)
    print(f"URL Statistics: {stats_result}")
    
    # Clean up
    repository.close()

if __name__ == "__main__":
    main()
</file>

<file path="system_design_guide/01_Foundation/01_Basic_System_Design_Principles.md">
# Basic System Design Principles

## Introduction

System design is the process of defining the architecture, components, interfaces, and data for a system to satisfy specified requirements. Good system design balances various concerns including scalability, reliability, efficiency, and maintainability.

## Core Principles

### 1. Separation of Concerns

**Concept**: Divide your system into distinct sections, each addressing a separate concern.

**Benefits**:
- Easier to understand, develop, and maintain
- Components can be developed and updated independently
- Facilitates testing and debugging

**Example**:
- Separating frontend (UI), backend (business logic), and data storage
- Using a layered architecture: presentation, business, data access layers

**Code Example**:

```python
# Bad: Mixing concerns
def process_user_registration(user_data):
    # Validate data
    if not user_data.get('email') or not user_data.get('password'):
        return {'error': 'Missing required fields'}
    
    # Business logic
    hashed_password = hash_password(user_data['password'])
    
    # Database operation
    db.execute(
        "INSERT INTO users (email, password) VALUES (?, ?)",
        [user_data['email'], hashed_password]
    )
    
    # Email notification
    send_welcome_email(user_data['email'])
    
    return {'success': True}

# Better: Separation of concerns
def validate_user_data(user_data):
    if not user_data.get('email') or not user_data.get('password'):
        return False
    return True

def process_user_registration(user_data):
    if not validate_user_data(user_data):
        return {'error': 'Missing required fields'}
    
    user_service.register_user(user_data)
    notification_service.send_welcome_email(user_data['email'])
    
    return {'success': True}
```

### 2. Single Responsibility Principle

**Concept**: Each component should have one and only one reason to change.

**Benefits**:
- Reduces complexity
- Improves maintainability
- Makes code more testable

**Example**:
- A class that handles user authentication should not also handle user profile management
- A service that processes payments should not also handle inventory management

**Code Example**:

```python
# Bad: Multiple responsibilities
class UserManager:
    def authenticate_user(self, username, password):
        # Authentication logic
        pass
    
    def update_profile(self, user_id, profile_data):
        # Profile update logic
        pass
    
    def process_payment(self, user_id, amount):
        # Payment processing logic
        pass

# Better: Single responsibility
class UserAuthenticator:
    def authenticate_user(self, username, password):
        # Authentication logic
        pass

class UserProfileManager:
    def update_profile(self, user_id, profile_data):
        # Profile update logic
        pass

class PaymentProcessor:
    def process_payment(self, user_id, amount):
        # Payment processing logic
        pass
```

### 3. KISS (Keep It Simple, Stupid)

**Concept**: Systems work best when they are kept simple rather than made complex.

**Benefits**:
- Easier to understand and maintain
- Fewer bugs and issues
- Faster development and iteration

**Example**:
- Using established patterns and technologies instead of inventing new ones
- Avoiding premature optimization
- Starting with a monolith before breaking into microservices

**Code Example**:

```python
# Overly complex
def get_active_users(users):
    result = []
    for i in range(len(users)):
        current_user = users[i]
        if current_user is not None:
            if 'status' in current_user:
                if current_user['status'] == 'active':
                    result.append(current_user)
    return result

# Simple and clear
def get_active_users(users):
    return [user for user in users if user and user.get('status') == 'active']
```

### 4. DRY (Don't Repeat Yourself)

**Concept**: Every piece of knowledge or logic should have a single, unambiguous representation within a system.

**Benefits**:
- Reduces code duplication
- Makes maintenance easier
- Reduces the chance of bugs

**Example**:
- Creating utility functions for common operations
- Using inheritance or composition for shared functionality
- Implementing reusable components

**Code Example**:

```python
# Violating DRY
def validate_email(email):
    import re
    pattern = r'^[\w\.-]+@[\w\.-]+\.\w+$'
    return bool(re.match(pattern, email))

def register_user(user_data):
    # Validating email again
    import re
    pattern = r'^[\w\.-]+@[\w\.-]+\.\w+$'
    if not bool(re.match(pattern, user_data['email'])):
        return {'error': 'Invalid email'}
    # Rest of registration logic

# Following DRY
def validate_email(email):
    import re
    pattern = r'^[\w\.-]+@[\w\.-]+\.\w+$'
    return bool(re.match(pattern, email))

def register_user(user_data):
    if not validate_email(user_data['email']):
        return {'error': 'Invalid email'}
    # Rest of registration logic
```

### 5. YAGNI (You Aren't Gonna Need It)

**Concept**: Don't add functionality until it is necessary.

**Benefits**:
- Reduces complexity
- Saves development time
- Prevents feature bloat

**Example**:
- Not building complex caching mechanisms until performance issues arise
- Not implementing a microservices architecture for a simple application
- Not adding fields to a database that might be needed in the future

### 6. Loose Coupling

**Concept**: Components should have minimal knowledge of and dependencies on other components.

**Benefits**:
- Easier to modify components without affecting others
- Facilitates testing and maintenance
- Enables parallel development

**Example**:
- Using interfaces or abstract classes
- Implementing dependency injection
- Using message queues for communication between services

**Code Example**:

```python
# Tightly coupled
class OrderProcessor:
    def process_order(self, order):
        # Direct instantiation creates tight coupling
        payment_processor = PaymentProcessor()
        inventory_manager = InventoryManager()
        
        payment_processor.process_payment(order.payment_details)
        inventory_manager.update_inventory(order.items)
        # More processing...

# Loosely coupled with dependency injection
class OrderProcessor:
    def __init__(self, payment_processor, inventory_manager):
        self.payment_processor = payment_processor
        self.inventory_manager = inventory_manager
    
    def process_order(self, order):
        self.payment_processor.process_payment(order.payment_details)
        self.inventory_manager.update_inventory(order.items)
        # More processing...
```

## System Design Process

1. **Understand Requirements**
   - Functional requirements (what the system should do)
   - Non-functional requirements (performance, scalability, reliability)
   - Constraints (time, budget, technology)

2. **High-Level Design**
   - System components and their interactions
   - Data flow diagrams
   - Technology stack selection

3. **Detailed Design**
   - API definitions
   - Database schema
   - Class/component diagrams

4. **Implementation Considerations**
   - Coding standards
   - Testing strategy
   - Deployment approach

5. **Evaluation**
   - Review against requirements
   - Performance testing
   - Security assessment

## Case Study: URL Shortener

Let's apply these principles to design a simple URL shortener service.

### Requirements
- Convert long URLs to short ones
- Redirect users from short URLs to original ones
- Track click statistics
- Handle high traffic

### High-Level Design
- Web server to handle HTTP requests
- Application logic for URL shortening
- Database to store URL mappings
- Caching layer for frequently accessed URLs

### Component Breakdown (Separation of Concerns)
1. **API Layer**: Handles HTTP requests and responses
2. **Service Layer**: Contains business logic for shortening URLs
3. **Data Access Layer**: Manages database operations
4. **Analytics Service**: Tracks and stores click statistics

### Implementation Example

```python
# URL Shortener with Separation of Concerns

# 1. API Layer
class URLShortenerAPI:
    def __init__(self, url_service, analytics_service):
        self.url_service = url_service
        self.analytics_service = analytics_service
    
    def shorten_url(self, original_url):
        short_url = self.url_service.create_short_url(original_url)
        return {"short_url": short_url}
    
    def redirect_to_original(self, short_code):
        original_url = self.url_service.get_original_url(short_code)
        if original_url:
            self.analytics_service.record_click(short_code)
            return {"redirect_to": original_url}
        return {"error": "URL not found"}

# 2. Service Layer
class URLService:
    def __init__(self, url_repository):
        self.url_repository = url_repository
    
    def create_short_url(self, original_url):
        # Generate a unique short code
        short_code = self._generate_short_code()
        
        # Store the mapping
        self.url_repository.save_url_mapping(short_code, original_url)
        
        return f"https://short.url/{short_code}"
    
    def get_original_url(self, short_code):
        return self.url_repository.get_original_url(short_code)
    
    def _generate_short_code(self):
        # Implementation of short code generation
        import random
        import string
        return ''.join(random.choices(string.ascii_letters + string.digits, k=6))

# 3. Data Access Layer
class URLRepository:
    def __init__(self, db_connection, cache):
        self.db_connection = db_connection
        self.cache = cache
    
    def save_url_mapping(self, short_code, original_url):
        # Save to database
        self.db_connection.execute(
            "INSERT INTO url_mappings (short_code, original_url) VALUES (?, ?)",
            [short_code, original_url]
        )
        
        # Update cache
        self.cache.set(short_code, original_url)
    
    def get_original_url(self, short_code):
        # Try to get from cache first
        cached_url = self.cache.get(short_code)
        if cached_url:
            return cached_url
        
        # If not in cache, get from database
        result = self.db_connection.execute(
            "SELECT original_url FROM url_mappings WHERE short_code = ?",
            [short_code]
        ).fetchone()
        
        if result:
            original_url = result[0]
            # Update cache
            self.cache.set(short_code, original_url)
            return original_url
        
        return None

# 4. Analytics Service
class AnalyticsService:
    def __init__(self, db_connection):
        self.db_connection = db_connection
    
    def record_click(self, short_code):
        self.db_connection.execute(
            "INSERT INTO click_stats (short_code, clicked_at) VALUES (?, ?)",
            [short_code, datetime.now()]
        )
    
    def get_click_count(self, short_code):
        result = self.db_connection.execute(
            "SELECT COUNT(*) FROM click_stats WHERE short_code = ?",
            [short_code]
        ).fetchone()
        
        return result[0] if result else 0
```

## Interview Questions

### Question 1: Design a URL Shortener

**Interviewer**: Design a URL shortening service like TinyURL.

**Key Points to Address**:
1. **Functional Requirements**:
   - Shorten a URL to a unique short URL
   - Redirect from short URL to original URL
   - Optional: Custom short URLs, analytics, expiration

2. **Non-Functional Requirements**:
   - High availability
   - Low latency for redirections
   - Scalability to handle high traffic

3. **API Design**:
   - `POST /shorten` - Create a short URL
   - `GET /{shortCode}` - Redirect to original URL

4. **Database Design**:
   - Table with short_code (PK), original_url, creation_date, etc.
   - Consider NoSQL for scalability

5. **Algorithm for Generating Short Codes**:
   - Random string generation
   - Base62 encoding of incremental IDs
   - Collision handling

6. **System Architecture**:
   - Load balancers
   - Web servers
   - Database servers
   - Caching layer

7. **Scaling Considerations**:
   - Database sharding
   - Caching frequently accessed URLs
   - Read replicas for database

### Question 2: How would you ensure high availability in a system?

**Key Points**:
1. **Redundancy**: Multiple instances of each component
2. **Elimination of Single Points of Failure**
3. **Load Balancing**: Distribute traffic across multiple servers
4. **Failover Mechanisms**: Automatic switching to backup systems
5. **Geographical Distribution**: Multiple data centers
6. **Monitoring and Alerting**: Detect issues before they cause outages
7. **Graceful Degradation**: System continues to function with reduced capabilities

### Question 3: Explain the CAP theorem and its implications for system design.

**Key Points**:
1. **CAP Theorem Definition**:
   - Consistency: All nodes see the same data at the same time
   - Availability: Every request receives a response
   - Partition Tolerance: System continues to operate despite network partitions

2. **You can only guarantee two out of three properties**

3. **Common Choices**:
   - CA: Traditional RDBMS (not partition tolerant)
   - CP: Systems that prioritize consistency (e.g., HBase, MongoDB)
   - AP: Systems that prioritize availability (e.g., Cassandra, DynamoDB)

4. **Implications for Design**:
   - Choose based on business requirements
   - Consider eventual consistency for high availability
   - Use different databases for different components based on needs

## Practical Exercise

### Exercise: Implement a Simple URL Shortener

Implement a basic URL shortener with the following features:
1. Convert long URLs to short ones
2. Redirect from short URLs to original ones
3. Track click statistics

**Requirements**:
- Follow the separation of concerns principle
- Implement proper error handling
- Use appropriate data structures
- Consider potential scaling issues

## AI/ML Integration

### How do these principles apply to ML systems?

1. **Separation of Concerns in ML Pipelines**:
   - Data collection and preprocessing
   - Feature engineering
   - Model training
   - Model serving
   - Monitoring and feedback

2. **Single Responsibility in ML Components**:
   - Feature stores for managing features
   - Model registries for versioning
   - Separate services for inference
   - Dedicated components for monitoring

3. **KISS in ML Systems**:
   - Start with simple models before complex ones
   - Use established frameworks rather than custom implementations
   - Implement baseline models before advanced techniques

4. **DRY in ML Code**:
   - Reusable preprocessing pipelines
   - Shared evaluation metrics
   - Common utilities for data manipulation

5. **Loose Coupling in ML Architecture**:
   - Separation between training and inference
   - Modular pipeline components
   - Clear interfaces between stages

## Next Steps

In the next section, we'll explore scalability fundamentals, including:
- Vertical vs. horizontal scaling
- Load balancing techniques
- Database scaling strategies
- Caching mechanisms
- Stateless design

## Resources

1. [System Design Primer](https://github.com/donnemartin/system-design-primer)
2. [Designing Data-Intensive Applications](https://dataintensive.net/) by Martin Kleppmann
3. [Clean Architecture](https://blog.cleancoder.com/uncle-bob/2012/08/13/the-clean-architecture.html) by Robert C. Martin
4. [SOLID Principles](https://en.wikipedia.org/wiki/SOLID)
</file>

<file path="system_design_guide/01_Foundation/02_Scalability_Fundamentals.md">
# Scalability Fundamentals

## Introduction

Scalability is a system's ability to handle growing amounts of work by adding resources. It's a critical aspect of system design, especially for applications expected to grow in terms of users, data, or traffic.

## Types of Scalability

### 1. Vertical Scaling (Scaling Up)

**Concept**: Adding more power to an existing machine.

**Examples**:
- Upgrading CPU
- Adding more RAM
- Increasing disk space

**Advantages**:
- Simple to implement
- No distribution complexity
- Lower software licensing costs

**Disadvantages**:
- Hardware limitations
- Single point of failure
- Downtime during upgrades
- Expensive beyond a certain point

**When to Use**:
- Small to medium applications
- When simplicity is preferred
- For stateful applications that are difficult to distribute
- As a short-term solution

### 2. Horizontal Scaling (Scaling Out)

**Concept**: Adding more machines to a system.

**Examples**:
- Adding more web servers
- Distributing database across multiple servers
- Creating server clusters

**Advantages**:
- Theoretically unlimited scaling
- Better fault tolerance
- Cost-effective (can use commodity hardware)
- No downtime for adding capacity

**Disadvantages**:
- Increased complexity
- Data consistency challenges
- Network overhead
- More complex deployment and maintenance

**When to Use**:
- Large-scale applications
- When high availability is required
- For stateless components
- When cost-efficiency at scale is important

## Key Scalability Concepts

### 1. Load Balancing

**Concept**: Distributing incoming network traffic across multiple servers.

**Methods**:
- Round Robin: Requests are distributed sequentially
- Least Connections: Requests go to the server with fewest active connections
- Least Response Time: Requests go to the server with fastest response time
- IP Hash: Client IP determines which server receives the request

**Implementation Options**:
- Hardware load balancers (e.g., F5, Citrix)
- Software load balancers (e.g., Nginx, HAProxy)
- DNS load balancing
- Cloud load balancers (e.g., AWS ELB, Google Cloud Load Balancing)

**Code Example (Nginx Configuration)**:
```nginx
http {
    upstream backend_servers {
        least_conn;  # Least connections algorithm
        server backend1.example.com;
        server backend2.example.com;
        server backend3.example.com;
    }

    server {
        listen 80;
        location / {
            proxy_pass http://backend_servers;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
    }
}
```

### 2. Database Scaling

#### a. Replication

**Concept**: Creating copies of a database to distribute read operations.

**Types**:
- Master-Slave: One primary (write) database, multiple read-only replicas
- Master-Master: Multiple databases that can accept writes

**Benefits**:
- Improved read performance
- Enhanced availability
- Geographic distribution

**Challenges**:
- Replication lag
- Consistency issues
- Failover complexity

**Example (MySQL Master-Slave Configuration)**:
```sql
-- On Master
CREATE USER 'replication_user'@'%' IDENTIFIED BY 'password';
GRANT REPLICATION SLAVE ON *.* TO 'replication_user'@'%';

-- On Slave
CHANGE MASTER TO
  MASTER_HOST='master_host',
  MASTER_USER='replication_user',
  MASTER_PASSWORD='password',
  MASTER_LOG_FILE='mysql-bin.000001',
  MASTER_LOG_POS=123;
START SLAVE;
```

#### b. Sharding

**Concept**: Partitioning data across multiple databases.

**Sharding Strategies**:
- Horizontal Sharding: Different rows in different databases
- Vertical Sharding: Different columns/features in different databases
- Directory-Based Sharding: Central lookup service to determine which shard contains the data

**Sharding Keys**:
- User ID
- Geographic location
- Date/time
- Custom hash functions

**Benefits**:
- Improved write performance
- Reduced index size
- Parallel query execution

**Challenges**:
- Complex queries across shards
- Rebalancing data
- Joins between shards
- Transaction management

**Code Example (Application-Level Sharding)**:
```python
def get_database_shard(user_id):
    """Determine which database shard to use based on user ID."""
    shard_count = 4
    shard_id = user_id % shard_count
    return f"database_shard_{shard_id}"

def save_user_data(user_id, data):
    """Save user data to the appropriate shard."""
    shard = get_database_shard(user_id)
    connection = get_database_connection(shard)
    cursor = connection.cursor()
    cursor.execute(
        "INSERT INTO user_data (user_id, data) VALUES (?, ?)",
        (user_id, data)
    )
    connection.commit()
```

### 3. Caching

**Concept**: Storing frequently accessed data in memory for faster retrieval.

**Caching Levels**:
- Client-side caching (browsers)
- CDN caching
- Application caching
- Database caching

**Caching Strategies**:
- Cache-Aside (Lazy Loading): Application checks cache first, then database
- Write-Through: Data is written to both cache and database
- Write-Behind (Write-Back): Data is written to cache and asynchronously to database
- Refresh-Ahead: Cache proactively refreshes before expiration

**Popular Caching Systems**:
- Redis
- Memcached
- Varnish
- CDNs (Cloudflare, Akamai)

**Code Example (Redis Caching)**:
```python
import redis
import json

# Initialize Redis client
redis_client = redis.Redis(host='localhost', port=6379, db=0)

def get_user(user_id):
    """Get user data with caching."""
    # Try to get from cache first
    cache_key = f"user:{user_id}"
    cached_user = redis_client.get(cache_key)
    
    if cached_user:
        # Cache hit
        return json.loads(cached_user)
    
    # Cache miss - get from database
    user = database.get_user(user_id)
    
    # Store in cache for future requests (expire after 1 hour)
    redis_client.setex(
        cache_key,
        3600,  # 1 hour in seconds
        json.dumps(user)
    )
    
    return user
```

### 4. Stateless Design

**Concept**: Servers don't store client state between requests.

**Benefits**:
- Easier horizontal scaling
- Improved reliability
- Simpler deployment
- Better load balancing

**Implementation Approaches**:
- Store state in the client (cookies, local storage)
- Store state in a distributed cache (Redis, Memcached)
- Store state in a database
- Use JWT or similar tokens

**Code Example (Stateless Authentication with JWT)**:
```python
import jwt
from datetime import datetime, timedelta

SECRET_KEY = "your-secret-key"

def create_access_token(user_id):
    """Create a JWT token for authentication."""
    expiration = datetime.utcnow() + timedelta(hours=1)
    
    payload = {
        "sub": user_id,
        "exp": expiration
    }
    
    return jwt.encode(payload, SECRET_KEY, algorithm="HS256")

def authenticate_request(token):
    """Authenticate a request using JWT token."""
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=["HS256"])
        user_id = payload["sub"]
        return user_id
    except jwt.ExpiredSignatureError:
        return None  # Token expired
    except jwt.InvalidTokenError:
        return None  # Invalid token
```

### 5. Asynchronous Processing

**Concept**: Handling time-consuming tasks outside the main request-response cycle.

**Benefits**:
- Improved response times
- Better resource utilization
- Increased throughput
- Enhanced user experience

**Implementation Options**:
- Message queues (RabbitMQ, Kafka, SQS)
- Task queues (Celery, Sidekiq)
- Event-driven architecture
- Webhooks

**Code Example (Asynchronous Processing with Celery)**:
```python
# tasks.py
from celery import Celery

app = Celery('tasks', broker='redis://localhost:6379/0')

@app.task
def process_image(image_path):
    """Process an image asynchronously."""
    # Time-consuming image processing
    # ...
    return "Processing complete"

# web_app.py
from flask import Flask, request
from tasks import process_image

app = Flask(__name__)

@app.route('/upload', methods=['POST'])
def upload_image():
    # Save the uploaded image
    image_path = save_uploaded_image(request.files['image'])
    
    # Trigger asynchronous processing
    task = process_image.delay(image_path)
    
    # Return immediately with task ID
    return {"status": "processing", "task_id": task.id}

@app.route('/status/<task_id>')
def check_status(task_id):
    # Check the status of the task
    task = process_image.AsyncResult(task_id)
    if task.ready():
        return {"status": "complete", "result": task.result}
    return {"status": "processing"}
```

## Scalability Patterns

### 1. Microservices Architecture

**Concept**: Building an application as a collection of small, loosely coupled services.

**Benefits**:
- Independent scaling of components
- Technology diversity
- Parallel development
- Fault isolation

**Challenges**:
- Increased complexity
- Network overhead
- Data consistency
- Operational overhead

**Example Architecture**:
```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│  User       │     │  Product    │     │  Order      │
│  Service    │     │  Service    │     │  Service    │
└─────────────┘     └─────────────┘     └─────────────┘
       │                  │                   │
       └──────────────────┼───────────────────┘
                          │
                 ┌─────────────────┐
                 │  API Gateway    │
                 └─────────────────┘
                          │
                     ┌─────────┐
                     │ Clients │
                     └─────────┘
```

### 2. CQRS (Command Query Responsibility Segregation)

**Concept**: Separating read and write operations into different models.

**Benefits**:
- Independent scaling of read and write workloads
- Optimized data schemas for different operations
- Improved performance for read-heavy applications

**Implementation**:
- Write commands go to the write model
- Read queries go to the read model
- Asynchronous synchronization between models

**Example Architecture**:
```
┌───────────┐     ┌───────────┐
│ Commands  │     │ Queries   │
└─────┬─────┘     └─────┬─────┘
      │                 │
┌─────▼─────┐     ┌─────▼─────┐
│ Write     │     │ Read      │
│ Model     │     │ Model     │
└─────┬─────┘     └───────────┘
      │                ▲
      │                │
      └────────────────┘
      Synchronization
```

### 3. Event Sourcing

**Concept**: Storing all changes to application state as a sequence of events.

**Benefits**:
- Complete audit trail
- Temporal queries (state at any point in time)
- Event replay for recovery
- Natural fit for event-driven architectures

**Challenges**:
- Learning curve
- Eventual consistency
- Versioning of events
- Query complexity

**Example Implementation**:
```javascript
// Event store
const eventStore = [];

// Command handler
function createOrder(orderId, customerId, items) {
  const event = {
    type: 'OrderCreated',
    timestamp: new Date(),
    data: {
      orderId,
      customerId,
      items
    }
  };
  
  eventStore.push(event);
  
  // Update read model
  updateReadModel(event);
  
  return orderId;
}

// Event handler to update read model
function updateReadModel(event) {
  if (event.type === 'OrderCreated') {
    orders[event.data.orderId] = {
      customerId: event.data.customerId,
      items: event.data.items,
      status: 'created'
    };
  }
  // Handle other event types...
}

// Query from read model
function getOrder(orderId) {
  return orders[orderId];
}
```

## Measuring and Monitoring Scalability

### 1. Key Metrics

- **Throughput**: Requests per second
- **Latency**: Response time
- **Error Rate**: Percentage of failed requests
- **Resource Utilization**: CPU, memory, disk, network
- **Saturation**: How full your service is
- **Cost**: Dollars per request

### 2. Tools and Approaches

- **Load Testing**: Apache JMeter, Locust, Gatling
- **Monitoring**: Prometheus, Grafana, Datadog
- **Distributed Tracing**: Jaeger, Zipkin
- **Log Aggregation**: ELK Stack, Graylog
- **Alerting**: PagerDuty, OpsGenie

### 3. Scalability Testing

**Steps**:
1. Establish baseline performance
2. Define scalability goals
3. Create realistic test scenarios
4. Gradually increase load
5. Identify bottlenecks
6. Implement improvements
7. Retest to verify

**Example Load Test Script (Locust)**:
```python
from locust import HttpUser, task, between

class WebsiteUser(HttpUser):
    wait_time = between(1, 5)  # Wait 1-5 seconds between tasks
    
    @task(2)
    def view_items(self):
        self.client.get("/items")
        
    @task(1)
    def view_item(self):
        item_id = random.randint(1, 1000)
        self.client.get(f"/items/{item_id}")
    
    @task(1)
    def add_to_cart(self):
        item_id = random.randint(1, 1000)
        self.client.post("/cart", json={"item_id": item_id})
```

## Case Study: Scaling a Web Application

### Initial Architecture

A simple web application with:
- Single web server
- Single database server
- No caching

### Problems Encountered

1. **Slow response times** during peak hours
2. **Database overload** with increasing users
3. **Single point of failure** causing downtime
4. **Limited geographic performance** for global users

### Scalability Solutions Applied

1. **Horizontal Scaling**:
   - Added multiple web servers
   - Implemented Nginx load balancer

2. **Database Optimization**:
   - Added read replicas for read queries
   - Implemented connection pooling
   - Optimized slow queries

3. **Caching Strategy**:
   - Added Redis for session storage and caching
   - Implemented CDN for static assets
   - Added browser caching headers

4. **Asynchronous Processing**:
   - Moved email sending to a message queue
   - Implemented background processing for reports

5. **Monitoring and Alerting**:
   - Added Prometheus for metrics collection
   - Set up Grafana dashboards
   - Implemented automated scaling based on metrics

### Results

- **10x increase** in supported concurrent users
- **95% reduction** in database load
- **70% improvement** in average response time
- **99.99% uptime** achieved
- **Global performance** improvements

### Architecture Evolution

```
Initial:
┌─────────┐     ┌─────────┐
│ Web     │     │ Database│
│ Server  │────▶│ Server  │
└─────────┘     └─────────┘

Final:
                ┌─────────┐
                │   CDN   │
                └────┬────┘
                     │
┌─────────┐     ┌────▼────┐
│ Load    │     │ Web     │
│ Balancer│────▶│ Servers │
└─────────┘     └────┬────┘
                     │
                ┌────▼────┐     ┌─────────┐
                │ Cache   │     │ Message │
                │ Layer   │     │ Queue   │
                └────┬────┘     └────┬────┘
                     │               │
                ┌────▼────┐     ┌────▼────┐
                │ Database│     │ Worker  │
                │ Cluster │     │ Servers │
                └─────────┘     └─────────┘
```

## Interview Questions

### Question 1: How would you scale a database that's becoming a bottleneck?

**Key Points to Address**:

1. **Identify the bottleneck type**:
   - Read-heavy vs. write-heavy workload
   - Query complexity
   - Data volume

2. **For read-heavy workloads**:
   - Add read replicas
   - Implement caching
   - Use a CDN for static content

3. **For write-heavy workloads**:
   - Database sharding
   - Use write-optimized databases
   - Implement write-behind caching

4. **General optimizations**:
   - Index optimization
   - Query optimization
   - Connection pooling
   - Vertical scaling (short-term)

5. **Advanced solutions**:
   - CQRS pattern
   - NoSQL databases for specific workloads
   - Database-specific optimizations

### Question 2: Design a system that can handle 10 million concurrent users.

**Key Points to Address**:

1. **Load distribution**:
   - Global load balancing with DNS
   - Regional load balancers
   - Auto-scaling server groups

2. **Stateless architecture**:
   - No server-side sessions
   - Token-based authentication
   - Client-side state management

3. **Caching strategy**:
   - Multi-level caching
   - CDN for static assets
   - Distributed cache for application data

4. **Database strategy**:
   - Sharded databases
   - Read replicas
   - NoSQL for appropriate data

5. **Asynchronous processing**:
   - Message queues for non-critical operations
   - Event-driven architecture
   - Background processing

6. **Optimization techniques**:
   - Connection pooling
   - HTTP/2 or HTTP/3
   - Efficient protocols (gRPC, WebSockets)

### Question 3: What are the tradeoffs between vertical and horizontal scaling?

**Key Points to Address**:

1. **Vertical Scaling**:
   - **Pros**: Simpler, no distribution complexity, lower licensing costs
   - **Cons**: Hardware limits, single point of failure, downtime during upgrades, expensive

2. **Horizontal Scaling**:
   - **Pros**: Theoretically unlimited, better fault tolerance, cost-effective, no downtime for adding capacity
   - **Cons**: Increased complexity, data consistency challenges, network overhead

3. **Decision factors**:
   - Application architecture (stateful vs. stateless)
   - Budget constraints
   - Growth projections
   - Availability requirements
   - Team expertise

4. **Hybrid approach**:
   - Vertical scaling for databases (initially)
   - Horizontal scaling for web/application servers
   - Gradual transition as needs evolve

## Practical Exercise

### Exercise: Implement a Scalable Counter Service

Design and implement a service that can:
1. Increment counters
2. Retrieve counter values
3. Handle high throughput (thousands of increments per second)
4. Maintain accuracy under concurrent load

**Requirements**:
- The service should be horizontally scalable
- Counters should be persistent
- The system should be resilient to failures
- Implement appropriate caching

**Hint**: Consider using Redis for atomic operations and persistence.

## AI/ML Integration

### Scaling Challenges Specific to ML Systems

1. **Model Serving**:
   - Model size (especially large language models)
   - Inference latency requirements
   - Batch vs. real-time prediction
   - Hardware acceleration needs (GPU, TPU)

2. **Training Infrastructure**:
   - Distributed training
   - Parameter servers
   - Data parallelism vs. model parallelism
   - Checkpointing and recovery

3. **Feature Engineering Pipeline**:
   - Real-time feature computation
   - Feature stores
   - Feature consistency between training and serving

4. **Data Management**:
   - Large dataset handling
   - Data versioning
   - Training/validation/test splits
   - Data augmentation at scale

### Scalable ML Architecture Patterns

1. **Model Serving Patterns**:
   - Model-as-a-Service: Dedicated API for model inference
   - Batch Prediction: Scheduled batch processing
   - Edge Deployment: Models deployed to edge devices
   - Hybrid Approaches: Combination of real-time and batch

2. **Feature Store Architecture**:
   - Offline Store: Batch-computed features
   - Online Store: Low-latency feature access
   - Feature Registry: Metadata and documentation
   - Transformation Service: Consistent transformations

3. **Training Infrastructure**:
   - Parameter Server: Centralized parameter storage
   - AllReduce: Decentralized gradient aggregation
   - Horovod: Efficient distributed training
   - Kubernetes-based orchestration

### Code Example: Scalable Model Serving

```python
# Using TensorFlow Serving for scalable model deployment

# 1. Export the model
import tensorflow as tf

model = tf.keras.models.load_model('my_model')
tf.saved_model.save(model, 'export/my_model/1/')

# 2. Deploy with Docker
# docker run -p 8501:8501 --mount type=bind,source=/path/to/export,target=/models/my_model -e MODEL_NAME=my_model tensorflow/serving

# 3. Client code for inference
import json
import requests

def predict(instances):
    data = json.dumps({
        "signature_name": "serving_default",
        "instances": instances
    })
    
    headers = {"content-type": "application/json"}
    url = "http://localhost:8501/v1/models/my_model:predict"
    
    response = requests.post(url, data=data, headers=headers)
    return response.json()

# Example prediction
result = predict([[5.1, 3.5, 1.4, 0.2]])
print(result)
```

## Next Steps

In the next section, we'll explore reliability and fault tolerance, including:
- Failure modes and recovery strategies
- Redundancy and replication
- Circuit breakers and bulkheads
- Chaos engineering
- Distributed system reliability patterns

## Resources

1. [Designing Data-Intensive Applications](https://dataintensive.net/) by Martin Kleppmann
2. [The Art of Scalability](https://theartofscalability.com/) by Martin L. Abbott and Michael T. Fisher
3. [Web Scalability for Startup Engineers](https://www.amazon.com/Scalability-Startup-Engineers-Artur-Ejsmont/dp/0071843655) by Artur Ejsmont
4. [System Design Primer - Scalability](https://github.com/donnemartin/system-design-primer#scalability)
5. [AWS Well-Architected Framework - Performance Efficiency Pillar](https://docs.aws.amazon.com/wellarchitected/latest/performance-efficiency-pillar/welcome.html)
</file>

<file path="system_design_guide/01_Foundation/03_Reliability_and_Fault_Tolerance.md">
# Reliability and Fault Tolerance

## Introduction

Reliability and fault tolerance are critical aspects of system design that ensure applications continue to function correctly even when components fail. In this module, we'll explore strategies for building resilient systems that can withstand various types of failures.

## Understanding Reliability

Reliability refers to a system's ability to perform its intended functions correctly and consistently over time. A reliable system:

- Continues to work correctly even when things go wrong
- Performs at the expected level under normal conditions
- Can handle invalid inputs or unexpected user behavior
- Prevents unauthorized access and abuse

### Measuring Reliability

Reliability is often measured using these metrics:

**Mean Time Between Failures (MTBF)**: The average time between system failures.

**Mean Time To Recovery (MTTR)**: The average time needed to restore the system after a failure.

**Availability**: The percentage of time a system is operational, often expressed in "nines":
- Two nines (99%): ~87.6 hours of downtime per year
- Three nines (99.9%): ~8.8 hours of downtime per year
- Four nines (99.99%): ~52.6 minutes of downtime per year
- Five nines (99.999%): ~5.3 minutes of downtime per year

**Service Level Objectives (SLOs)**: Target levels of service reliability, usually defined in terms of availability or response time.

## Types of Failures

Understanding different failure types helps in designing appropriate fault tolerance mechanisms:

### 1. Hardware Failures

**Description**: Physical component failures such as server crashes, disk failures, network outages, or power loss.

**Impact**: Can affect single machines or entire data centers.

**Examples**:
- Hard drive crashes causing data loss
- Network switch failures isolating parts of the system
- Power outages affecting entire data centers

### 2. Software Failures

**Description**: Bugs, memory leaks, resource exhaustion, or unhandled exceptions in application code.

**Impact**: Can cause individual services to crash or behave incorrectly.

**Examples**:
- Memory leaks gradually consuming all available RAM
- Unhandled edge cases causing crashes
- Deadlocks preventing progress

### 3. Dependency Failures

**Description**: Failures in external services or systems that your application depends on.

**Impact**: Can propagate through the system, causing cascading failures.

**Examples**:
- Database unavailability affecting all dependent services
- Third-party API outages
- DNS resolution failures

### 4. Network Failures

**Description**: Communication issues between system components.

**Impact**: Can cause partial system failures or inconsistent behavior.

**Examples**:
- Network partitions isolating parts of a distributed system
- Packet loss causing timeouts
- High latency affecting performance

### 5. Human Errors

**Description**: Mistakes made by operators, developers, or users.

**Impact**: Can cause unexpected system behavior or outages.

**Examples**:
- Accidental deletion of data
- Misconfiguration during deployment
- Incorrect manual interventions during incidents

## Fault Tolerance Strategies

### 1. Redundancy

**Concept**: Duplicating critical components to eliminate single points of failure.

**Types**:
- **Hardware redundancy**: Multiple servers, network paths, power supplies
- **Geographic redundancy**: Distributing systems across multiple data centers
- **Data redundancy**: Maintaining multiple copies of data

**Implementation**:
- Deploy applications across multiple availability zones
- Use RAID configurations for storage
- Implement database replication

**Example**:
```
# AWS Multi-AZ deployment in Terraform
resource "aws_db_instance" "example" {
  engine               = "mysql"
  instance_class       = "db.t3.micro"
  allocated_storage    = 20
  name                 = "mydb"
  username             = "admin"
  password             = "password"
  multi_az             = true  # Enable Multi-AZ deployment for redundancy
  backup_retention_period = 7
}
```

### 2. Isolation and Bulkheads

**Concept**: Containing failures to prevent them from cascading through the system.

**Implementation**:
- Separate critical services from non-critical ones
- Use containers or VMs to isolate components
- Implement resource limits per component
- Design independent failure domains

**Example**:
```java
// Resource isolation using thread pools in Java
ThreadPoolExecutor criticalPool = new ThreadPoolExecutor(
    10, 20, 60, TimeUnit.SECONDS, new ArrayBlockingQueue<>(100));

ThreadPoolExecutor nonCriticalPool = new ThreadPoolExecutor(
    5, 10, 60, TimeUnit.SECONDS, new ArrayBlockingQueue<>(100));

// Critical operations use one pool
criticalPool.submit(() -> processCriticalTask());

// Non-critical operations use another pool
nonCriticalPool.submit(() -> processNonCriticalTask());
```

### 3. Timeouts

**Concept**: Setting maximum time limits for operations to prevent indefinite waiting.

**Implementation**:
- Add timeouts to all external calls
- Make timeout values configurable
- Consider the implications of timeouts (e.g., retries)

**Example**:
```python
import requests
from requests.exceptions import Timeout

try:
    # Set a 3-second timeout for this request
    response = requests.get('https://api.example.com/data', timeout=3)
    data = response.json()
except Timeout:
    # Handle the timeout case
    data = get_cached_data()  # Fall back to cached data
```

### 4. Retries with Backoff

**Concept**: Automatically retrying failed operations with increasing delays between attempts.

**Implementation**:
- Use exponential backoff to avoid overwhelming the target
- Add jitter to prevent synchronized retries
- Set a maximum retry limit

**Example**:
```javascript
async function fetchWithRetry(url, maxRetries = 3) {
  let retries = 0;
  
  while (true) {
    try {
      return await fetch(url);
    } catch (error) {
      if (retries >= maxRetries) throw error;
      
      // Exponential backoff with jitter
      const delay = Math.min(100 * Math.pow(2, retries), 5000);
      const jitter = Math.random() * 200;
      await new Promise(resolve => setTimeout(resolve, delay + jitter));
      
      retries++;
    }
  }
}
```

### 5. Circuit Breakers

**Concept**: Automatically stopping operations that are likely to fail, preventing system overload.

**States**:
- **Closed**: Operations proceed normally
- **Open**: Operations fail fast without attempting execution
- **Half-Open**: Limited operations are allowed to test if the problem is resolved

**Implementation**:
- Monitor failure rates
- Trip the circuit when failures exceed a threshold
- Automatically reset after a cooling period

**Example**:
```java
// Using Resilience4j in Java
CircuitBreaker circuitBreaker = CircuitBreakerRegistry.ofDefaults()
    .circuitBreaker("backendService");

Supplier<String> decoratedSupplier = CircuitBreaker
    .decorateSupplier(circuitBreaker, () -> backendService.doSomething());

// The circuit breaker will track failures and open if necessary
String result = Try.ofSupplier(decoratedSupplier)
    .recover(throwable -> "Fallback value").get();
```

### 6. Graceful Degradation

**Concept**: Reducing functionality instead of failing completely when resources are constrained or dependencies are unavailable.

**Implementation**:
- Identify core vs. non-core features
- Implement fallbacks for critical operations
- Design for partial availability

**Example**:
```python
def get_product_details(product_id):
    # Try to get full details with recommendations
    try:
        product = get_basic_product_info(product_id)
        
        # Try to get recommendations, but continue without them if unavailable
        try:
            recommendations = recommendation_service.get_recommendations(product_id, timeout=1)
            product['recommendations'] = recommendations
        except ServiceUnavailableError:
            # Degrade gracefully by omitting recommendations
            product['recommendations'] = []
            log.warning("Recommendation service unavailable")
            
        return product
    except Exception as e:
        # Last resort fallback to cached data
        log.error(f"Failed to get product details: {e}")
        return get_cached_product(product_id)
```

### 7. Load Shedding

**Concept**: Selectively dropping requests when the system is overloaded to prevent complete failure.

**Implementation**:
- Prioritize different types of requests
- Reject low-priority requests during high load
- Implement rate limiting at entry points

**Example**:
```java
// Simple load shedding based on queue size
public Response processRequest(Request request) {
    if (isHighPriorityRequest(request)) {
        // Always process high-priority requests
        return actuallyProcessRequest(request);
    } else {
        // Check current load for low-priority requests
        int queueSize = getRequestQueueSize();
        if (queueSize > LOAD_THRESHOLD) {
            // Shed load by rejecting the request
            return Response.status(503)
                .entity("Service temporarily overloaded, please try again later")
                .build();
        } else {
            return actuallyProcessRequest(request);
        }
    }
}
```

### 8. Fail Fast

**Concept**: Detecting and reporting failures as quickly as possible rather than trying to proceed with likely-to-fail operations.

**Implementation**:
- Validate inputs early
- Check preconditions before starting operations
- Fail immediately when dependencies are unavailable

**Example**:
```csharp
public void ProcessOrder(Order order)
{
    // Fail fast by validating inputs immediately
    if (order == null)
        throw new ArgumentNullException(nameof(order));
        
    if (string.IsNullOrEmpty(order.CustomerId))
        throw new ArgumentException("Customer ID is required");
        
    if (order.Items.Count == 0)
        throw new ArgumentException("Order must contain at least one item");
    
    // Check if inventory service is available before proceeding
    if (!inventoryService.IsAvailable())
        throw new ServiceUnavailableException("Inventory service is down");
    
    // Now proceed with the actual processing
    // ...
}
```

## Designing for Fault Tolerance

### 1. Stateless Design

**Concept**: Designing services that don't store client-specific state between requests, making them easier to replicate and recover.

**Benefits**:
- Any server can handle any request
- Easier horizontal scaling
- Simpler recovery after failures

**Implementation**:
- Store state in external systems (databases, caches)
- Use tokens or cookies for client identification
- Pass necessary context in each request

### 2. Idempotent Operations

**Concept**: Designing operations that can be repeated multiple times without causing additional side effects beyond the first execution.

**Benefits**:
- Safe to retry after failures
- Simplifies error handling
- Improves system reliability

**Example**:
```python
# Idempotent payment processing using a transaction ID
def process_payment(payment_request):
    transaction_id = payment_request.transaction_id
    
    # Check if this transaction was already processed
    if database.transaction_exists(transaction_id):
        return get_existing_transaction(transaction_id)
    
    # Process the payment only if it hasn't been processed before
    result = payment_gateway.charge(
        amount=payment_request.amount,
        card=payment_request.card,
        idempotency_key=transaction_id
    )
    
    # Store the result
    database.save_transaction(transaction_id, result)
    
    return result
```

### 3. Asynchronous Processing

**Concept**: Processing operations in the background rather than during the user request, reducing the impact of failures.

**Benefits**:
- Improved user experience
- Better fault isolation
- Ability to retry failed operations

**Implementation**:
- Use message queues for reliable delivery
- Implement dead letter queues for failed messages
- Design for eventual consistency

**Example**:
```javascript
// Using a message queue for asynchronous order processing
app.post('/orders', async (req, res) => {
  try {
    // Validate the order
    const order = validateOrder(req.body);
    
    // Generate an order ID
    const orderId = generateOrderId();
    
    // Store the order with 'pending' status
    await db.orders.insert({
      id: orderId,
      ...order,
      status: 'pending',
      createdAt: new Date()
    });
    
    // Send to message queue for asynchronous processing
    await messageQueue.sendMessage({
      type: 'ORDER_CREATED',
      payload: {
        orderId,
        order
      }
    });
    
    // Respond to the user immediately
    res.status(202).json({
      orderId,
      status: 'pending',
      message: 'Order received and is being processed'
    });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});
```

### 4. Health Checks and Self-Healing

**Concept**: Continuously monitoring system health and automatically recovering from failures.

**Implementation**:
- Implement health check endpoints
- Use readiness and liveness probes
- Automatically restart failed components
- Implement automated rollbacks

**Example**:
```yaml
# Kubernetes liveness and readiness probes
apiVersion: v1
kind: Pod
metadata:
  name: my-service
spec:
  containers:
  - name: my-service
    image: my-service:1.0
    ports:
    - containerPort: 8080
    livenessProbe:
      httpGet:
        path: /health
        port: 8080
      initialDelaySeconds: 30
      periodSeconds: 10
      failureThreshold: 3
    readinessProbe:
      httpGet:
        path: /ready
        port: 8080
      initialDelaySeconds: 5
      periodSeconds: 5
```

## Testing for Reliability

### 1. Chaos Engineering

**Concept**: Deliberately introducing failures to test system resilience.

**Implementation**:
- Simulate hardware failures
- Inject latency and errors
- Kill random processes
- Block network access

**Tools**:
- Netflix Chaos Monkey
- Gremlin
- Chaos Toolkit

### 2. Load Testing

**Concept**: Testing system behavior under high load to identify breaking points.

**Implementation**:
- Simulate realistic user behavior
- Gradually increase load until failure
- Measure response times and error rates
- Identify bottlenecks

**Tools**:
- Apache JMeter
- Locust
- Gatling

### 3. Fault Injection

**Concept**: Deliberately introducing faults to test recovery mechanisms.

**Implementation**:
- Simulate dependency failures
- Test timeout handling
- Verify retry mechanisms
- Validate circuit breakers

**Example**:
```java
// Using fault injection to test resilience
@Test
public void testDatabaseFailureHandling() {
    // Setup fault injection
    when(databaseService.getData())
        .thenThrow(new DatabaseConnectionException("Simulated failure"));
    
    // Call the service that should handle the failure
    ServiceResponse response = userService.getUserData();
    
    // Verify graceful degradation
    assertNotNull(response);
    assertEquals("fallback-data", response.getData());
    assertEquals(ServiceStatus.DEGRADED, response.getStatus());
}
```

## Case Study: E-commerce Checkout System

Let's apply reliability and fault tolerance principles to an e-commerce checkout system:

### Requirements
- Process customer orders reliably
- Handle payment processing
- Manage inventory updates
- Send order confirmations

### Potential Failures
1. Payment gateway timeouts or failures
2. Inventory service unavailability
3. Email service failures
4. Database connection issues
5. High traffic causing system overload

### Fault-Tolerant Design

**1. Redundancy**
- Deploy the checkout service across multiple availability zones
- Use a database with multi-region replication
- Implement multiple payment providers as fallbacks

**2. Circuit Breakers**
- Add circuit breakers for payment gateway calls
- Implement circuit breakers for inventory service calls
- Monitor failure rates and open circuits when thresholds are exceeded

**3. Asynchronous Processing**
- Use a message queue for order processing
- Process inventory updates asynchronously
- Send confirmation emails via a separate queue

**4. Idempotent Operations**
- Generate unique order IDs client-side
- Make payment processing idempotent using transaction IDs
- Design inventory updates to be safely retryable

**5. Graceful Degradation**
- Allow checkout with delayed inventory verification if the inventory service is down
- Provide estimated delivery dates if precise calculation is unavailable
- Queue email notifications if the email service is unavailable

**6. Timeouts and Retries**
- Set appropriate timeouts for all external calls
- Implement retry with backoff for transient failures
- Limit the number of retries to prevent overwhelming dependencies

### Implementation Example

```java
public class CheckoutService {
    private final PaymentService paymentService;
    private final InventoryService inventoryService;
    private final NotificationService notificationService;
    private final OrderRepository orderRepository;
    private final MessageQueue orderQueue;
    
    public OrderResult processCheckout(Cart cart, PaymentDetails paymentDetails) {
        // Generate a unique order ID
        String orderId = UUID.randomUUID().toString();
        
        try {
            // Validate order (fail fast)
            validateOrder(cart);
            
            // Create order with PENDING status
            Order order = createOrder(orderId, cart);
            orderRepository.save(order);
            
            // Process payment with circuit breaker and retry
            PaymentResult paymentResult = paymentCircuitBreaker.executeWithFallback(
                () -> processPaymentWithRetry(orderId, paymentDetails, cart.getTotalAmount()),
                this::handlePaymentFailure
            );
            
            if (!paymentResult.isSuccessful()) {
                return new OrderResult(orderId, OrderStatus.PAYMENT_FAILED, paymentResult.getMessage());
            }
            
            // Update order status
            order.setStatus(OrderStatus.PAYMENT_RECEIVED);
            orderRepository.update(order);
            
            // Queue for asynchronous processing
            orderQueue.send(new OrderMessage(orderId, OrderAction.PROCESS));
            
            return new OrderResult(orderId, OrderStatus.PROCESSING, "Order is being processed");
        } catch (Exception e) {
            // Log the error
            logger.error("Checkout failed for order " + orderId, e);
            
            // Return appropriate error to the user
            return new OrderResult(orderId, OrderStatus.ERROR, "An unexpected error occurred");
        }
    }
    
    private PaymentResult processPaymentWithRetry(String orderId, PaymentDetails details, BigDecimal amount) {
        RetryPolicy<PaymentResult> retryPolicy = RetryPolicy.<PaymentResult>builder()
            .handle(TransientPaymentException.class)
            .withBackoff(500, 5000, ChronoUnit.MILLIS)
            .withJitter(0.3)
            .withMaxRetries(3)
            .build();
            
        return Failsafe.with(retryPolicy).get(() -> 
            paymentService.processPayment(orderId, details, amount)
        );
    }
    
    private PaymentResult handlePaymentFailure(Exception e) {
        // Graceful degradation - return a failure result
        return new PaymentResult(false, "Payment service unavailable, please try again later");
    }
    
    // Asynchronous order processor
    @MessageListener(queue = "order-queue")
    public void processOrder(OrderMessage message) {
        String orderId = message.getOrderId();
        Order order = orderRepository.findById(orderId);
        
        try {
            // Check inventory with circuit breaker
            boolean inventoryReserved = inventoryCircuitBreaker.executeWithFallback(
                () -> inventoryService.reserveInventory(order.getItems()),
                (e) -> handleInventoryFailure(order)
            );
            
            if (inventoryReserved) {
                order.setStatus(OrderStatus.CONFIRMED);
            } else {
                order.setStatus(OrderStatus.INVENTORY_FAILED);
            }
            
            orderRepository.update(order);
            
            // Send notification (non-critical, can fail)
            try {
                notificationService.sendOrderConfirmation(order);
            } catch (Exception e) {
                // Log but don't fail the order processing
                logger.warn("Failed to send order confirmation for " + orderId, e);
            }
        } catch (Exception e) {
            // Handle unexpected errors
            logger.error("Failed to process order " + orderId, e);
            
            // Retry later by sending back to the queue with a delay
            orderQueue.sendWithDelay(message, Duration.ofMinutes(5));
        }
    }
    
    private boolean handleInventoryFailure(Order order) {
        // Graceful degradation - mark for manual inventory check
        order.setStatus(OrderStatus.PENDING_INVENTORY_CHECK);
        orderRepository.update(order);
        
        // Notify inventory team
        alertService.sendAlert("Inventory service down, manual check required for order " + order.getId());
        
        return false;
    }
}
```

## Interview Questions

### Question 1: How would you design a system to be resilient to database failures?

**Key Points to Address**:

1. **Redundancy**:
   - Use database replication (master-slave or multi-master)
   - Deploy across multiple availability zones or regions
   - Implement read replicas for read operations

2. **Caching**:
   - Add a caching layer (Redis, Memcached) to reduce database load
   - Implement cache-aside pattern for frequently accessed data
   - Consider write-through or write-behind caching for writes

3. **Circuit Breakers**:
   - Implement circuit breakers for database calls
   - Provide fallbacks when the database is unavailable
   - Monitor error rates and response times

4. **Queuing**:
   - Use message queues for write operations
   - Process database updates asynchronously
   - Implement dead letter queues for failed operations

5. **Data Partitioning**:
   - Shard data across multiple database instances
   - Distribute load and risk across multiple servers
   - Isolate failures to specific shards

6. **Retry Mechanisms**:
   - Implement retries with exponential backoff
   - Make operations idempotent to allow safe retries
   - Set appropriate timeout values

7. **Graceful Degradation**:
   - Return cached data when the database is unavailable
   - Disable non-critical features during database outages
   - Provide clear user feedback during degraded operation

### Question 2: Explain the difference between fail-fast and fail-silent approaches. When would you use each?

**Fail-Fast**:
- Immediately reports failures when detected
- Raises exceptions or errors to callers
- Makes problems visible quickly
- Prevents cascading failures by stopping early

**When to use Fail-Fast**:
- During development and testing to catch issues early
- For critical operations where correctness is essential
- When the caller can take meaningful action on failure
- In synchronous operations where immediate feedback is needed

**Fail-Silent**:
- Attempts to continue operation despite failures
- Hides errors from users when possible
- Focuses on maintaining service availability
- May degrade functionality rather than failing completely

**When to use Fail-Silent**:
- For non-critical features in production systems
- When partial functionality is better than none
- In asynchronous operations that can be retried
- For user-facing components where graceful degradation improves experience

### Question 3: How would you implement a circuit breaker pattern in a microservices architecture?

**Key Components**:

1. **Circuit State Management**:
   - Track success/failure counts
   - Maintain circuit state (closed, open, half-open)
   - Implement timeout for resetting to half-open

2. **Failure Detection**:
   - Define what constitutes a failure (exceptions, timeouts, HTTP status codes)
   - Set thresholds for tripping the circuit (e.g., 50% failure rate)
   - Consider sliding window for failure rate calculation

3. **Circuit Behavior**:
   - Closed: Allow requests to pass through normally
   - Open: Fail fast without calling the service
   - Half-Open: Allow limited requests to test recovery

4. **Fallback Mechanisms**:
   - Provide alternative responses when the circuit is open
   - Return cached data, default values, or gracefully degrade
   - Queue requests for later processing if appropriate

5. **Monitoring and Metrics**:
   - Track circuit state changes
   - Monitor success/failure rates
   - Alert on circuit trips

**Implementation Example**:
```java
// Using Resilience4j in a Spring Boot application
@Service
public class ProductService {
    private final RestTemplate restTemplate;
    private final CircuitBreaker circuitBreaker;
    
    public ProductService(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
        
        // Configure circuit breaker
        CircuitBreakerConfig config = CircuitBreakerConfig.custom()
            .failureRateThreshold(50)
            .slidingWindowSize(10)
            .minimumNumberOfCalls(5)
            .waitDurationInOpenState(Duration.ofSeconds(30))
            .permittedNumberOfCallsInHalfOpenState(3)
            .build();
            
        CircuitBreakerRegistry registry = CircuitBreakerRegistry.of(config);
        this.circuitBreaker = registry.circuitBreaker("productService");
    }
    
    public Product getProduct(String productId) {
        // Use the circuit breaker to call the product API
        return Try.ofSupplier(
            CircuitBreaker.decorateSupplier(
                circuitBreaker,
                () -> restTemplate.getForObject(
                    "/products/" + productId,
                    Product.class
                )
            )
        ).recover(throwable -> {
            // Fallback when circuit is open or call fails
            return getCachedProduct(productId);
        }).get();
    }
    
    private Product getCachedProduct(String productId) {
        // Return cached product or a default one
        // ...
    }
}
```

## Practical Exercise

### Exercise: Implement a Fault-Tolerant API Client

Design and implement a fault-tolerant HTTP client for an external API that:

1. Handles transient failures with retries
2. Implements circuit breaking for persistent failures
3. Provides fallbacks when the API is unavailable
4. Includes appropriate timeout handling
5. Logs and monitors failure rates

**Requirements**:
- The client should be configurable (retry attempts, timeouts, etc.)
- It should handle different types of failures appropriately
- The implementation should be testable

**Hint**: Consider using a library like Resilience4j, Hystrix, or Polly depending on your language.

## AI/ML Integration

### Reliability Challenges in ML Systems

1. **Model Serving Reliability**:
   - Models may crash due to unexpected inputs
   - Inference latency can vary significantly
   - Resource consumption (CPU, memory) can be unpredictable

2. **Data Quality Issues**:
   - Input data drift can cause model performance degradation
   - Missing features or corrupted data can break models
   - Outliers may cause unexpected behavior

3. **Model Versioning Challenges**:
   - Rolling back to previous model versions during issues
   - Ensuring consistent model behavior across deployments
   - Managing model dependencies

### Fault Tolerance Strategies for ML Systems

1. **Model Redundancy**:
   - Deploy multiple model versions in parallel
   - Use ensemble methods to combine predictions
   - Implement model fallbacks for different failure scenarios

2. **Input Validation and Preprocessing**:
   - Validate inputs before sending to the model
   - Handle missing features with imputation
   - Normalize or transform outliers

3. **Monitoring and Circuit Breaking**:
   - Monitor prediction quality metrics
   - Implement circuit breakers based on model performance
   - Set up alerts for data drift or model degradation

4. **Graceful Degradation**:
   - Fall back to simpler models when complex ones fail
   - Use rule-based systems as backups
   - Return reasonable defaults with confidence indicators

**Example: Fault-Tolerant Model Serving**:
```python
class FaultTolerantModelServer:
    def __init__(self):
        # Load primary and fallback models
        self.primary_model = load_complex_model()
        self.fallback_model = load_simple_model()
        self.rule_based_fallback = RuleBasedPredictor()
        
        # Initialize circuit breaker
        self.circuit_breaker = CircuitBreaker(
            failure_threshold=5,
            recovery_timeout=300,  # 5 minutes
            expected_exception=ModelPredictionError
        )
        
    def predict(self, features):
        try:
            # Validate input features
            validated_features = self.validate_and_preprocess(features)
            
            # Try primary model with circuit breaker
            if self.circuit_breaker.is_closed():
                try:
                    prediction = self.primary_model.predict(validated_features)
                    self.circuit_breaker.record_success()
                    return {
                        'prediction': prediction,
                        'model': 'primary',
                        'confidence': self.calculate_confidence(prediction)
                    }
                except Exception as e:
                    self.circuit_breaker.record_failure()
                    raise ModelPredictionError(f"Primary model failed: {str(e)}")
            
            # Circuit is open, try fallback model
            try:
                prediction = self.fallback_model.predict(validated_features)
                return {
                    'prediction': prediction,
                    'model': 'fallback',
                    'confidence': self.calculate_confidence(prediction) * 0.8  # Lower confidence
                }
            except Exception:
                # Last resort: rule-based fallback
                prediction = self.rule_based_fallback.predict(validated_features)
                return {
                    'prediction': prediction,
                    'model': 'rule_based',
                    'confidence': 0.5  # Low confidence
                }
                
        except Exception as e:
            # Log the error
            logger.error(f"All prediction methods failed: {str(e)}")
            
            # Return safe default with very low confidence
            return {
                'prediction': self.get_safe_default(),
                'model': 'default',
                'confidence': 0.1
            }
    
    def validate_and_preprocess(self, features):
        # Validate feature names and types
        # Handle missing values
        # Transform outliers
        # ...
        return processed_features
```

## Next Steps

In the next section, we'll explore Performance Optimization, including:
- Performance metrics and measurement
- Profiling and bottleneck identification
- Algorithmic optimization
- Database query optimization
- Network optimization

## Resources

1. [Designing Data-Intensive Applications](https://dataintensive.net/) by Martin Kleppmann
2. [Release It!](https://pragprog.com/titles/mnee2/release-it-second-edition/) by Michael Nygard
3. [Chaos Engineering](https://www.oreilly.com/library/view/chaos-engineering/9781492043867/) by Casey Rosenthal and Nora Jones
4. [Resilience4j Documentation](https://resilience4j.readme.io/docs)
5. [AWS Well-Architected Framework - Reliability Pillar](https://docs.aws.amazon.com/wellarchitected/latest/reliability-pillar/welcome.html)
</file>

<file path="system_design_guide/getting_started.md">
# Getting Started with the System Design Guide

Welcome to your comprehensive System Design Guide! This document will help you get started with the guide and make the most of the resources provided.

## What We've Created

We've set up a structured learning path to help you master system design concepts and prepare for staff/principal/architect roles. Here's what's included so far:

### 1. Overall Structure

- **README.md**: Overview of the entire guide and learning approach
- **module_outline.md**: Detailed outline of all modules and topics
- **next_steps.md**: Guidance on how to proceed with your learning journey

### 2. Foundation Module (Started)

- **Basic System Design Principles**: Core concepts and patterns with examples
- **Scalability Fundamentals**: Techniques for building scalable systems

### 3. Practical Implementations

- **URL Shortener**: Demonstrates separation of concerns and basic design principles
- **Scalable Counter Service**: Shows scalability techniques with Redis

## How to Use This Guide

### Step 1: Understand the Structure

Start by reading the main README.md file to understand the overall approach and structure of the guide. Then review the module_outline.md to see the complete learning path.

### Step 2: Begin with Foundation Concepts

1. Read through `01_Foundation/01_Basic_System_Design_Principles.md`
2. Study the URL Shortener implementation in `01_Foundation/code/url_shortener/`
3. Continue with `01_Foundation/02_Scalability_Fundamentals.md`
4. Explore the Scalable Counter Service in `01_Foundation/code/scalable_counter/`

### Step 3: Hands-on Practice

For each topic:
1. **Read the theory** in the markdown files
2. **Study the code examples** to understand practical implementation
3. **Modify the examples** to reinforce your understanding
4. **Complete the exercises** at the end of each module

### Step 4: Follow the Learning Path

Use the module_outline.md as your roadmap. Each week, focus on completing one or two topics from the outline, including both theory and practical implementation.

## Running the Code Examples

### URL Shortener

1. Navigate to the URL Shortener directory:
   ```
   cd system_design_guide/01_Foundation/code/url_shortener
   ```

2. Run the Flask web application:
   ```
   python app.py
   ```

3. Open your browser and go to `http://localhost:5000`

### Scalable Counter Service

1. Start Redis (required for the counter service):
   ```
   docker run -p 6379:6379 redis
   ```
   
   Or use Docker Compose for a complete setup:
   ```
   cd system_design_guide/01_Foundation/code/scalable_counter
   docker-compose up
   ```

2. Run the counter service API:
   ```
   python counter_api.py
   ```

3. Run the load test to see scalability in action:
   ```
   python load_test.py --scalability-test
   ```

## Customizing Your Learning

Feel free to adapt this guide to your specific needs:

1. **Focus on relevant topics**: If you're more interested in ML systems, you can prioritize those modules
2. **Adjust the pace**: The weekly schedule is a suggestion - move faster or slower as needed
3. **Add your own examples**: Create additional implementations to reinforce concepts
4. **Connect with your work**: Apply the concepts to problems you're facing in your current role

## Getting Help

If you encounter difficulties or have questions:

1. **Revisit prerequisites**: Make sure you understand the foundational concepts
2. **Search online**: Many system design concepts have excellent resources available
3. **Join communities**: Participate in forums and communities focused on system design
4. **Pair with colleagues**: Discussing concepts with others can deepen understanding

## Next Steps

Review the next_steps.md file for detailed guidance on how to proceed with your learning journey, including short-term and long-term goals.

Happy learning, and enjoy your system design journey!
</file>

<file path="system_design_guide/module_outline.md">
# System Design Guide - Module Outline

This document provides a detailed outline of all modules in the System Design Guide, helping you navigate through the learning path. When you provide implementations and examples, code logic should be in python.

## Foundation (Weeks 1-2)

### 1. Basic System Design Principles
- Core principles (Separation of Concerns, KISS, DRY, YAGNI, etc.)
- System design process
- Case study: URL Shortener
- Implementation: URL Shortener service
- Interview questions and exercises

### 2. Scalability Fundamentals
- Vertical vs. horizontal scaling
- Load balancing
- Database scaling (replication, sharding)
- Caching strategies
- Stateless design
- Asynchronous processing
- Implementation: Scalable Counter Service
- Interview questions and exercises

### 3. Reliability and Fault Tolerance
- Failure modes and recovery
- Redundancy and replication
- Circuit breakers and bulkheads
- Retry strategies with backoff
- Graceful degradation
- Implementation: Fault-tolerant Service
- Interview questions and exercises

### 4. Performance Optimization
- Performance metrics and measurement
- Profiling and bottleneck identification
- Algorithmic optimization
- Database query optimization
- Network optimization
- Implementation: Performance-optimized API
- Interview questions and exercises

### 5. API Design
- REST API design principles
- GraphQL vs. REST
- API versioning strategies
- Authentication and authorization
- Rate limiting and throttling
- Implementation: Well-designed API
- Interview questions and exercises

## Data Systems (Weeks 3-4)

### 6. Database Selection and Design
- Relational vs. NoSQL databases
- Database selection criteria
- Schema design principles
- Normalization vs. denormalization
- Indexing strategies
- Implementation: Multi-database System
- Interview questions and exercises

### 7. SQL vs. NoSQL Tradeoffs
- ACID vs. BASE properties
- Consistency models
- Performance characteristics
- Use case analysis
- Polyglot persistence
- Implementation: Hybrid Database System
- Interview questions and exercises

### 8. Data Modeling
- Entity-relationship modeling
- Object-relational mapping
- Document database modeling
- Graph database modeling
- Time-series data modeling
- Implementation: Complex Data Model
- Interview questions and exercises

### 9. Caching Strategies
- Cache types and use cases
- Cache invalidation strategies
- Cache coherence
- Distributed caching
- Cache eviction policies
- Implementation: Multi-level Caching System
- Interview questions and exercises

### 10. Data Partitioning and Sharding
- Horizontal vs. vertical partitioning
- Sharding strategies and algorithms
- Consistent hashing
- Rebalancing and migration
- Cross-shard operations
- Implementation: Sharded Database
- Interview questions and exercises

## Distributed Systems (Weeks 5-6)

### 11. Distributed System Fundamentals
- Distributed system challenges
- Network models and assumptions
- Time and ordering
- State and coordination
- Failure detection
- Implementation: Distributed Counter
- Interview questions and exercises

### 12. Consistency Models
- Strong consistency
- Eventual consistency
- Causal consistency
- Session consistency
- Tunable consistency
- Implementation: Consistent Data Store
- Interview questions and exercises

### 13. Consensus Algorithms
- Paxos
- Raft
- ZAB (ZooKeeper Atomic Broadcast)
- Byzantine fault tolerance
- Leader election
- Implementation: Consensus Protocol
- Interview questions and exercises

### 14. Distributed Caching
- Cache topologies
- Data partitioning in caches
- Cache coherence protocols
- Write-through vs. write-behind
- Cache invalidation
- Implementation: Distributed Cache
- Interview questions and exercises

### 15. Message Queues and Event-Driven Architecture
- Message queue patterns
- Publish-subscribe systems
- Event sourcing
- CQRS (Command Query Responsibility Segregation)
- Stream processing
- Implementation: Event-driven System
- Interview questions and exercises

## Scaling Applications (Weeks 7-8)

### 16. Horizontal vs. Vertical Scaling
- Scaling decision framework
- Resource utilization analysis
- Cost-benefit analysis
- Hybrid scaling approaches
- Auto-scaling strategies
- Implementation: Auto-scaling System
- Interview questions and exercises

### 17. Load Balancing Strategies
- Layer 4 vs. Layer 7 load balancing
- Load balancing algorithms
- Health checking and failover
- Session persistence
- Global load balancing
- Implementation: Advanced Load Balancer
- Interview questions and exercises

### 18. Microservices Architecture
- Monolith vs. microservices
- Service boundaries and design
- Inter-service communication
- Deployment strategies
- Monitoring and observability
- Implementation: Microservices Application
- Interview questions and exercises

### 19. Service Discovery
- Client-side vs. server-side discovery
- Service registry patterns
- DNS-based discovery
- Health checking
- Load balancing integration
- Implementation: Service Discovery System
- Interview questions and exercises

### 20. API Gateways
- API gateway responsibilities
- Request routing
- Authentication and authorization
- Rate limiting and throttling
- Request/response transformation
- Implementation: API Gateway
- Interview questions and exercises

## System Resilience (Weeks 9-10)

### 21. Failure Modes and Recovery
- Failure classification
- Failure detection
- Recovery strategies
- Disaster recovery planning
- Business continuity
- Implementation: Self-healing System
- Interview questions and exercises

### 22. Circuit Breakers
- Circuit breaker pattern
- State management
- Threshold configuration
- Monitoring and alerting
- Integration with retry logic
- Implementation: Circuit Breaker Library
- Interview questions and exercises

### 23. Rate Limiting
- Rate limiting algorithms
- Distributed rate limiting
- Client identification strategies
- Response handling
- Quota management
- Implementation: Rate Limiting Service
- Interview questions and exercises

### 24. Retry Strategies
- Retry patterns
- Backoff algorithms
- Idempotency considerations
- Circuit breaker integration
- Timeout management
- Implementation: Retry Library
- Interview questions and exercises

### 25. Chaos Engineering
- Principles of chaos engineering
- Experiment design
- Safety mechanisms
- Metrics and monitoring
- Tooling and infrastructure
- Implementation: Chaos Testing Framework
- Interview questions and exercises

## ML Systems Design (Weeks 11-12)

### 26. ML System Architecture
- ML system components
- Training vs. inference architecture
- Batch vs. real-time prediction
- Model lifecycle management
- Monitoring and observability
- Implementation: ML System Architecture
- Interview questions and exercises

### 27. Feature Stores
- Feature store architecture
- Feature computation and storage
- Feature serving
- Feature versioning
- Online/offline consistency
- Implementation: Feature Store
- Interview questions and exercises

### 28. Model Serving Infrastructure
- Model server architecture
- Scaling model inference
- Model versioning and rollback
- A/B testing infrastructure
- Hardware acceleration
- Implementation: Model Serving System
- Interview questions and exercises

### 29. Online vs. Batch Prediction
- Tradeoffs and use cases
- Hybrid approaches
- Latency optimization
- Resource utilization
- Consistency considerations
- Implementation: Hybrid Prediction System
- Interview questions and exercises

### 30. ML Monitoring and Observability
- Model performance metrics
- Data drift detection
- Concept drift detection
- Alerting and remediation
- Debugging tools
- Implementation: ML Monitoring System
- Interview questions and exercises

## Advanced Topics (Weeks 13-14)

### 31. Global Distribution Strategies
- Content delivery networks
- Edge computing
- Data locality
- Geo-partitioning
- Global consistency
- Implementation: Globally Distributed System
- Interview questions and exercises

### 32. Multi-Region Architecture
- Region selection strategies
- Active-active vs. active-passive
- Data replication
- Disaster recovery
- Traffic routing
- Implementation: Multi-region Application
- Interview questions and exercises

### 33. Data Consistency in Distributed ML
- Feature consistency
- Model consistency
- Training-serving skew
- Versioning strategies
- Consistency-latency tradeoffs
- Implementation: Consistent ML Pipeline
- Interview questions and exercises

### 34. Real-time Processing Systems
- Stream processing architectures
- Event time vs. processing time
- Windowing strategies
- State management
- Exactly-once processing
- Implementation: Real-time Analytics System
- Interview questions and exercises

### 35. Large-Scale Data Processing
- Batch processing frameworks
- Distributed computation models
- Data partitioning strategies
- Resource management
- Workflow orchestration
- Implementation: Data Processing Pipeline
- Interview questions and exercises

## Specialized Systems (Weeks 15-16)

### 36. Search Systems
- Inverted indexes
- Relevance scoring
- Query processing
- Distributed search
- Search optimization
- Implementation: Search Engine
- Interview questions and exercises

### 37. Recommendation Systems
- Recommendation algorithms
- Feature engineering for recommendations
- Real-time vs. batch recommendations
- Evaluation metrics
- A/B testing
- Implementation: Recommendation Engine
- Interview questions and exercises

### 38. Real-time Analytics
- Event ingestion
- Stream processing
- Real-time aggregation
- Dashboard architecture
- Alerting systems
- Implementation: Real-time Dashboard
- Interview questions and exercises

### 39. Stream Processing
- Stream processing models
- Stateful processing
- Windowing and triggers
- Fault tolerance
- Exactly-once semantics
- Implementation: Stream Processing Application
- Interview questions and exercises

### 40. Large Language Model Infrastructure
- LLM serving architecture
- Inference optimization
- Prompt engineering infrastructure
- Fine-tuning pipelines
- Evaluation frameworks
- Implementation: LLM Service
- Interview questions and exercises
</file>

<file path="system_design_guide/next_steps.md">
# Next Steps in Your System Design Journey

Congratulations on starting your system design learning journey! Here's how to make the most of this guide and continue building your expertise.

## Immediate Next Steps

### 1. Complete the Foundation Module

Start by working through the Foundation module (Weeks 1-2):

1. **Basic System Design Principles**
   - Study the core principles in `01_Foundation/01_Basic_System_Design_Principles.md`
   - Implement the URL Shortener example in `01_Foundation/code/url_shortener/`
   - Practice the interview questions at the end of the document

2. **Scalability Fundamentals**
   - Study the scalability concepts in `01_Foundation/02_Scalability_Fundamentals.md`
   - Implement and test the Scalable Counter Service in `01_Foundation/code/scalable_counter/`
   - Run load tests to understand performance characteristics

3. **Continue with Remaining Foundation Topics**
   - Reliability and Fault Tolerance
   - Performance Optimization
   - API Design

### 2. Set a Regular Study Schedule

Consistency is key to mastering system design:

- **Daily Practice**: Spend at least 30-60 minutes daily
- **Weekly Implementation**: Implement one practical example each week
- **Bi-weekly Review**: Review and consolidate what you've learned

### 3. Enhance Your Learning

- **Draw Diagrams**: Create visual representations of the systems you study
- **Explain Concepts**: Practice explaining concepts in simple terms
- **Peer Discussion**: Discuss concepts with colleagues or online communities

## Medium-Term Goals (1-3 Months)

### 1. Complete the Data Systems Module

After the Foundation module, move on to Data Systems (Weeks 3-4):

- Database Selection and Design
- SQL vs. NoSQL Tradeoffs
- Data Modeling
- Caching Strategies
- Data Partitioning and Sharding

### 2. Build a Portfolio Project

Apply what you've learned to build a more complex system:

- **Idea**: Choose a real-world problem that interests you
- **Design**: Create a detailed system design document
- **Implementation**: Build a working prototype
- **Documentation**: Document your design decisions and tradeoffs

### 3. Study Real-World Systems

Analyze how successful companies have designed their systems:

- Read engineering blogs from companies like Netflix, Uber, Airbnb, etc.
- Study open-source projects with similar requirements
- Compare different approaches to solving the same problem

## Long-Term Goals (3-6 Months)

### 1. Complete the Entire Guide

Work through all modules in the guide:

- Distributed Systems
- Scaling Applications
- System Resilience
- ML Systems Design
- Advanced Topics
- Specialized Systems

### 2. Specialize in Areas Relevant to Your Career

Based on your interests and career goals, dive deeper into specific areas:

- **Backend Engineer**: Focus on API design, microservices, and database scaling
- **ML Engineer**: Focus on ML systems design, feature stores, and model serving
- **Data Engineer**: Focus on data processing, stream processing, and analytics

### 3. Prepare for System Design Interviews

If you're targeting staff/principal/architect roles:

- Practice explaining your design decisions
- Work on communicating tradeoffs clearly
- Build a repertoire of design patterns and when to apply them
- Practice with mock interviews

## Resources to Complement This Guide

### Books

1. **Designing Data-Intensive Applications** by Martin Kleppmann
2. **System Design Interview** by Alex Xu
3. **Building Microservices** by Sam Newman
4. **Fundamentals of Software Architecture** by Mark Richards & Neal Ford
5. **Designing Machine Learning Systems** by Chip Huyen

### Online Resources

1. [System Design Primer](https://github.com/donnemartin/system-design-primer)
2. [High Scalability Blog](http://highscalability.com/)
3. [AWS Architecture Center](https://aws.amazon.com/architecture/)
4. [Google Cloud Architecture Center](https://cloud.google.com/architecture)
5. [Microsoft Azure Architecture Center](https://docs.microsoft.com/en-us/azure/architecture/)

### Communities

1. [r/systemdesign](https://www.reddit.com/r/systemdesign/)
2. [Hacker News](https://news.ycombinator.com/)
3. [Stack Overflow](https://stackoverflow.com/)
4. [Engineering blogs of tech companies](https://github.com/kilimchoi/engineering-blogs)

## Tracking Your Progress

To stay organized and motivated:

1. **Create a learning journal** to document concepts, questions, and insights
2. **Set specific goals** for each week and month
3. **Track your implementations** and what you learned from each
4. **Regularly review** your progress and adjust your plan as needed

## Final Advice

Remember that system design is both an art and a science. There are rarely perfect solutions, only tradeoffs. The more you practice, the better you'll become at:

1. **Identifying requirements** and constraints
2. **Evaluating tradeoffs** between different approaches
3. **Communicating designs** clearly and effectively
4. **Implementing systems** that are scalable, reliable, and maintainable

Enjoy the journey, and don't hesitate to revisit earlier modules as you gain more experience and perspective!
</file>

<file path="system_design_guide/README.md">
# Comprehensive System Design Guide

A systematic, incremental, and engaging guideline for system design covering basic to advanced concepts with practical examples, code snippets, and interview preparation materials.

## Overview

This guide is designed for experienced engineers looking to advance to staff/principal/architect roles, with a special focus on the intersection of software engineering and AI/ML. It provides a structured learning path that builds knowledge incrementally while offering hands-on examples and practical implementations.

## Learning Path Structure

The guide is organized into modules, each building upon the previous ones. Each module contains:

1. **Theoretical Concepts**: Core principles and patterns
2. **Case Studies**: Real-world examples analyzing existing systems
3. **Implementation Exercises**: Hands-on coding exercises
4. **Interview Questions**: Common questions with detailed solutions
5. **AI/ML Integration**: How ML concepts apply to the module topic

## Modules

### Foundation (Weeks 1-2)
- Basic System Design Principles
- Scalability Fundamentals
- Reliability and Fault Tolerance
- Performance Optimization Basics
- API Design

### Data Systems (Weeks 3-4)
- Database Selection and Design
- SQL vs. NoSQL Tradeoffs
- Data Modeling
- Caching Strategies
- Data Partitioning and Sharding

### Distributed Systems (Weeks 5-6)
- Distributed System Fundamentals
- Consistency Models
- Consensus Algorithms
- Distributed Caching
- Message Queues and Event-Driven Architecture

### Scaling Applications (Weeks 7-8)
- Horizontal vs. Vertical Scaling
- Load Balancing Strategies
- Microservices Architecture
- Service Discovery
- API Gateways

### System Resilience (Weeks 9-10)
- Failure Modes and Recovery
- Circuit Breakers
- Rate Limiting
- Retry Strategies
- Chaos Engineering

### ML Systems Design (Weeks 11-12)
- ML System Architecture
- Feature Stores
- Model Serving Infrastructure
- Online vs. Batch Prediction
- ML Monitoring and Observability

### Advanced Topics (Weeks 13-14)
- Global Distribution Strategies
- Multi-Region Architecture
- Data Consistency in Distributed ML
- Real-time Processing Systems
- Large-Scale Data Processing

### Specialized Systems (Weeks 15-16)
- Search Systems
- Recommendation Systems
- Real-time Analytics
- Stream Processing
- Large Language Model Infrastructure

## Learning Approach

Each topic follows this structure:

1. **Concept Introduction**: Clear explanation with diagrams
2. **Design Patterns**: Common patterns and when to use them
3. **Implementation Example**: Code snippets and mini-projects
4. **Case Study**: Analysis of real-world systems
5. **Design Exercise**: Open-ended problem to solve
6. **Interview Practice**: LeetCode-style questions related to the topic

## Getting Started

Each module is contained in its own directory with:
- README.md with theory and concepts
- Code examples in appropriate languages
- Diagrams and visual aids
- Implementation exercises
- Interview question solutions

## Prerequisites

- Strong programming fundamentals
- Basic understanding of distributed systems
- Familiarity with databases and data structures
- Experience with at least one cloud provider
- Basic understanding of ML concepts

## How to Use This Guide

1. Follow the modules in sequence
2. Complete the exercises for each topic
3. Implement the mini-projects
4. Practice the interview questions
5. Apply concepts to your own projects

Let's embark on this learning journey to master system design concepts and prepare for staff/principal/architect roles in top product firms!
</file>

<file path="best_time_to_buysell_stock.py">
class Solution:
    def maxProfit(self, prices: List[int]) -> int:
        sell_list = []
        index_list = []
        for i, buy in enumerate(prices):
            j=i+1
            for sell in prices[i+1:]:
                sold = sell-buy
                if sold > 0:
                    sell_list.append(sold)
                    index_list.append((i,j))
                    j=j+1

        if sell_list:
            print("sell_list", sell_list)
            print("index_list", index_list)
            val = max(sell_list)
            ind = sell_list.index(val)
            res = index_list[ind]
            return res[1]+1
           
        else:
            print("sell_list here", sell_list)
            return 0
</file>

<file path="design_snake_ladder.py">
from random import randint

class LadderGrid():
    def __init__(self,number,positions) -> None:
        self.numberLadder = number
        self.positions = positions

class SnakesGrid():
    def __init__(self, number,positions) -> None:
        self.numberSnakes = number
        self.positions = positions

class Rolldice():
    def __init__(self) -> None:
        pass

    def roll(self,player):
        self.player = player
        self.player.position = self.player.position + randint(1, 6)


class Player():
    def __init__(self, snakegrid, laddergrid, name = ""):
        self.name  = name
        self.position = 0
        self.won = False
        self.snakegrid = snakegrid
        self.laddergrid = laddergrid

    def getPostion(self):
        self.checkSnakesorLadder()
        res = self.gameResult()
        if res is True:
            self.won = True
        return self.position
    
    def checkSnakesorLadder(self):
        if self.position in self.snakegrid.keys():
            newVal = self.snakegrid.get(self.position)
            self.position = newVal
            print("bit by snake, new pos for player {} is".format(self.name), self.position)
        if self.position in self.laddergrid.keys():
            newVal = self.laddergrid.get(self.position)
            self.position = newVal
            print("found a ladder, new pos for player {} is".format(self.name), self.position)

    def gameResult(self):
        if self.position >= 100:
            print("player {}, won the game".format(self.name))
            return True
        return False








S= SnakesGrid(9, {62 : 5,
33 : 6,
49 : 9,
88 : 16,
41 :20,
56 : 53,
98 : 64,
93 : 73,
95  : 75})

L = LadderGrid(8, {2 : 37,
27  : 46,
10 : 32,
51 : 68,
61 : 79,
65 : 84,
71 : 91,
81 : 100}
)

p1 = Player(S.positions, L.positions, "Peeyush")
p2 = Player(S.positions, L.positions, "Nupur")

dice = Rolldice()

while (True):
    dice.roll(p1)
    dice.roll(p2)
    pos1 = p1.getPostion()
    pos2 = p2.getPostion()
    print("position for player 1 is", pos1)
    print("position for player 2 is", pos2)
    if p1.won is True:
        break
    if p2.won is True:
        break
</file>

<file path="file_system_with_regexpy">
'''
1166. Design File System
https://leetcode.com/problems/design-file-system/

You are asked to design a file system that allows you to create new paths and
associate them with different sizes.

The format of a path is one or more concatenated strings of the form: /
followed by one or more lowercase English letters.
For example, "/leetcode" and "/leetcode/problems" are valid paths while an empty string "" and "/" are not.

Implement the FileSystem class:

bool createPath(string path, int size) Creates a new path and associates a size to it if possible and
returns true. Returns false if the path already exists or its parent path doesn't exist.

int get(string path) Returns the size associated with path or returns -1 if the path doesn't exist.

Example 1:

Input: 
["FileSystem","createPath","get"]
[[],["/a",1],["/a"]]
Output: 
[null,true,1]
Explanation: 
FileSystem fileSystem = new FileSystem();

fileSystem.createPath("/a", 1); // return true
fileSystem.get("/a"); // return 1

Example 2:

Input: 
["FileSystem","createPath","createPath","get","createPath","get"]
[[],["/leet",1],["/leet/code",2],["/leet/code"],["/c/d",1],["/c"]]
Output: 
[null,true,true,2,false,-1]
Explanation: 
FileSystem fileSystem = new FileSystem();

fileSystem.createPath("/leet", 1); // return true
fileSystem.createPath("/leet/code", 2); // return true
fileSystem.get("/leet/code"); // return 2
fileSystem.createPath("/c/d", 1); // return false because the parent path "/c" doesn't exist.
fileSystem.get("/c"); // return -1 because this path doesn't exist.

Constraints:
    2 <= path.length <= 100
    1 <= size <= 109
    Each path is valid and consists of lowercase English letters and '/'.
    At most 104 calls in total will be made to createPath and get.
'''
class TreeNode:
    def __init__(self, size):
        self.size = size
        self.size = size
        self.children = {} # { path or file name : TreeNode }
    
class FileSystem:
    def __init__(self):
        self.root = TreeNode(None)

    def createPath(self, path: str, size: int) -> bool:        
        # Be careful! Python string split contains empty strings!
        paths = path.split('/')
        
        node = self.root
        for i, name in enumerate(paths):
            if not name:
                continue
            if name in node.children:
                if i == len(paths) - 1:
                    # file already existed
                    return False
                else:
                    node = node.children[name]
            else:
                if i == len(paths) - 1:
                    node.children[name] = TreeNode(size)
                else:
                    # parent path does not exist
                    return False
        return True
            
    def get(self, path: str) -> int:
        node = self.root
        paths = path.split('/')
        if self.checkregex(paths) is True:
            size = self.findwithregex(node,paths)
        else:
            size = self.findNormal(node,paths)
        return size
        
    
    def findwithregex(self, node, paths):

        regexpathlist = []
        ind = paths.index("*")
        index = 1
        for index, path in enumerate(paths):
            if not path:
                continue
            if path in node.children:
                node = node.children[path]
            else:
                return -1
            
            if index == ind-1:
                subpaths = list(node.children.keys())
                for subpath in subpaths:
                    regexpathlist.append((path,subpath))
                break
                

        size = 0
        node = self.root
        for paths in regexpathlist:
            size += self.findNormal(node,paths)

        return size


        

    def findNormal(self, node,paths):
        for name in paths:
            if not name:
                continue
                
            if name in node.children:
                node = node.children[name]
            else:
                # path does not exist
                return -1
            
        return node.size

    

    def checkregex(self,path):
        if "*" in path:
            return True
        return False
        
    


fs = FileSystem()
ret = fs.createPath("/leet", 100)
# // return true
ret = fs.createPath("/leet/code", 200); 
# // return true
size1 = fs.get("/leet/code"); 
# // return 2
ret = fs.createPath("/c", 300)
ret = fs.createPath("/c/d", 400)
ret = fs.createPath("/c/e", 500)
# // return false because the parent path "/c" doesn't exist.
size2 = fs.get("/c/*")

print("sum of total size", size1+size2)
print("sum of total size ", size1+size2)
# // return -1 because this path doesn't exist.
</file>

<file path="file_system_with_size.py">
'''
1166. Design File System
https://leetcode.com/problems/design-file-system/

You are asked to design a file system that allows you to create new paths and
associate them with different sizes.

The format of a path is one or more concatenated strings of the form: /
followed by one or more lowercase English letters.
For example, "/leetcode" and "/leetcode/problems" are valid paths while an empty string "" and "/" are not.

Implement the FileSystem class:

bool createPath(string path, int size) Creates a new path and associates a size to it if possible and
returns true. Returns false if the path already exists or its parent path doesn't exist.

int get(string path) Returns the size associated with path or returns -1 if the path doesn't exist.

Example 1:

Input: 
["FileSystem","createPath","get"]
[[],["/a",1],["/a"]]
Output: 
[null,true,1]
Explanation: 
FileSystem fileSystem = new FileSystem();

fileSystem.createPath("/a", 1); // return true
fileSystem.get("/a"); // return 1

Example 2:

Input: 
["FileSystem","createPath","createPath","get","createPath","get"]
[[],["/leet",1],["/leet/code",2],["/leet/code"],["/c/d",1],["/c"]]
Output: 
[null,true,true,2,false,-1]
Explanation: 
FileSystem fileSystem = new FileSystem();

fileSystem.createPath("/leet", 1); // return true
fileSystem.createPath("/leet/code", 2); // return true
fileSystem.get("/leet/code"); // return 2
fileSystem.createPath("/c/d", 1); // return false because the parent path "/c" doesn't exist.
fileSystem.get("/c"); // return -1 because this path doesn't exist.

Constraints:
    2 <= path.length <= 100
    1 <= size <= 109
    Each path is valid and consists of lowercase English letters and '/'.
    At most 104 calls in total will be made to createPath and get.
'''
class TreeNode:
    def __init__(self, size):
        self.size = size
        self.size = size
        self.children = {} # { path or file name : TreeNode }
    
class FileSystem:
    def __init__(self):
        self.root = TreeNode(None)

    def createPath(self, path: str, size: int) -> bool:        
        # Be careful! Python string split contains empty strings!
        paths = path.split('/')
        
        node = self.root
        for i, name in enumerate(paths):
            if not name:
                continue
            if name in node.children:
                if i == len(paths) - 1:
                    # file already existed
                    return False
                else:
                    node = node.children[name]
            else:
                if i == len(paths) - 1:
                    node.children[name] = TreeNode(size)
                else:
                    # parent path does not exist
                    return False
        return True
            
    def get(self, path: str) -> int:
        node = self.root
        paths = path.split('/')
        
        for name in paths:
            if not name:
                continue
                
            if name in node.children:
                node = node.children[name]
            else:
                # path does not exist
                return -1
            
        return node.size
    


fs = FileSystem()
ret = fs.createPath("/leet", 100)
# // return true
ret = fs.createPath("/leet/code", 200); 
# // return true
size1 = fs.get("/leet/code"); 
# // return 2
ret = fs.createPath("/c", 300)
ret = fs.createPath("/c/d", 400)
# // return false because the parent path "/c" doesn't exist.
size2 = fs.get("/c")

print("sum of total size", size1+size2)
# // return -1 because this path doesn't exist.
</file>

<file path="file_system.py">
'''
1166. Design File System
https://leetcode.com/problems/design-file-system/

You are asked to design a file system that allows you to create new paths and
associate them with different values.

The format of a path is one or more concatenated strings of the form: /
followed by one or more lowercase English letters.
For example, "/leetcode" and "/leetcode/problems" are valid paths while an empty string "" and "/" are not.

Implement the FileSystem class:

bool createPath(string path, int value) Creates a new path and associates a value to it if possible and
returns true. Returns false if the path already exists or its parent path doesn't exist.

int get(string path) Returns the value associated with path or returns -1 if the path doesn't exist.

Example 1:

Input: 
["FileSystem","createPath","get"]
[[],["/a",1],["/a"]]
Output: 
[null,true,1]
Explanation: 
FileSystem fileSystem = new FileSystem();

fileSystem.createPath("/a", 1); // return true
fileSystem.get("/a"); // return 1

Example 2:

Input: 
["FileSystem","createPath","createPath","get","createPath","get"]
[[],["/leet",1],["/leet/code",2],["/leet/code"],["/c/d",1],["/c"]]
Output: 
[null,true,true,2,false,-1]
Explanation: 
FileSystem fileSystem = new FileSystem();

fileSystem.createPath("/leet", 1); // return true
fileSystem.createPath("/leet/code", 2); // return true
fileSystem.get("/leet/code"); // return 2
fileSystem.createPath("/c/d", 1); // return false because the parent path "/c" doesn't exist.
fileSystem.get("/c"); // return -1 because this path doesn't exist.

Constraints:
    2 <= path.length <= 100
    1 <= value <= 109
    Each path is valid and consists of lowercase English letters and '/'.
    At most 104 calls in total will be made to createPath and get.
'''
class TreeNode:
    def __init__(self, value):
        self.value = value
        self.children = {} # { path or file name : TreeNode }
    
class FileSystem:
    def __init__(self):
        self.root = TreeNode(None)

    def createPath(self, path: str, value: int) -> bool:        
        # Be careful! Python string split contains empty strings!
        paths = path.split('/')
        
        node = self.root
        for i, name in enumerate(paths):
            if not name:
                continue
            if name in node.children:
                if i == len(paths) - 1:
                    # file already existed
                    return False
                else:
                    node = node.children[name]
            else:
                if i == len(paths) - 1:
                    node.children[name] = TreeNode(value)
                else:
                    # parent path does not exist
                    return False
        return True
            
    def get(self, path: str) -> int:
        node = self.root
        paths = path.split('/')
        for name in paths:
            if not name:
                continue
                
            if name in node.children:
                node = node.children[name]
            else:
                # path does not exist
                return -1
            
        return node.value
    


fs = FileSystem()
ret = fs.createPath("/leet", 1);
# // return true
ret = fs.createPath("/leet/code", 2); 
# // return true
ret = fs.get("/leet/code"); 
# // return 2
ret = fs.createPath("/c/d", 1);
# // return false because the parent path "/c" doesn't exist.
ret = fs.get("/c"); 
# // return -1 because this path doesn't exist.
</file>

<file path="hitcounter_interview.py">
from collections import deque

class HitCounter:
    """
    We maintain a queue for this problem. The init and hit (append) method are quite straightforward. 
    The main operation is performed in getHits.
    In getHits, we keep popping elements from the queue (while queue exists), only if the difference
    between the current timestamp and the first element of the queue is greater than or equal to 300.
    What this does is that the queue would remove all elements that have timestamp difference of more 
    than or equal to 300. What we are left is the queue with the closest 300 timestamps to the current
    timestamp. If we encounter a difference of less than 300, then this operation stops. Finally, the 
    length of the queue is the number of "hits" at the current timestamp.
    """
    def __init__(self, time_window: int = 300):
        """Initialize HitCounter with configurable time window (default 5 minutes)"""
        self.queue = deque()
        self.time_window = time_window  # Now configurable in seconds

    def hit(self, timestamp: int) -> None:
        """Record a hit at the given timestamp."""
        # Remove any expired hits before adding new one
        while self.queue and timestamp - self.queue[0] >= self.time_window:
            self.queue.popleft()
        self.queue.append(timestamp)

    def getHits(self, timestamp: int) -> int:
        """Return the number of hits in the past time window."""
        # Remove any expired hits before counting
        while self.queue and timestamp - self.queue[0] >= self.time_window:
            self.queue.popleft()
        return len(self.queue)
</file>

<file path="hitcounter.py">
from threading import Thread, Lock
import time


lock = Lock()

class Hitcounter():
    def __init__(self) -> None:
        self.counterlist = []
        self.start_time = time.time()

    def check(self):
        while True:
            time.sleep(1)
            if time.time() - self.start_time > 10:
                print(time.time())
                self.start_time = time.time()
                if self.counterlist:
                    self.counterlist.pop(0)

    
    def hit(self, val):
        with lock:
            self.counterlist.append(val)

    def getHit(self):
        with lock:
            print("hit counter",len(self.counterlist))
</file>

<file path="logger_ratelimiter.py">
class logger():
    def __init__(self) -> None:
        self.messageMap = {}
   
    def shouldPrintMessage(self, timestamp,message):
        if message not in self.messageMap.keys():
            self.messageMap[message] = self.messageMap.get("message",0) + timestamp
            self.messageMap[message] +=10
            print("next allowed timestamp for message {} is {}".format(message, self.messageMap[message]))
            return True

        if timestamp == self.messageMap[message]:
            self.messageMap[message] += 10
            print("next allowed timestamp for message {} is {}".format(message, self.messageMap[message]))
            return True
        elif timestamp < self.messageMap[message]:
            return False
        else:
            self.messageMap[message] = timestamp + 10
            print("next allowed timestamp for message {} is {}".format(message, self.messageMap[message]))
            return True


lg = logger()

lg.shouldPrintMessage(1, "foo");  
# // return true, next allowed timestamp for "foo" is 1 + 10 = 11
lg.shouldPrintMessage(2, "bar");  
# // return true, next allowed timestamp for "bar" is 2 + 10 = 12
lg.shouldPrintMessage(3, "foo");  
# // 3 < 11, return false
lg.shouldPrintMessage(8, "bar"); 
# // 8 < 12, return false
lg.shouldPrintMessage(10, "foo"); 
# // 10 < 11, return false
lg.shouldPrintMessage(11, "foo");
# return true, next allowed timestamp for "foo" is 11 + 10 = 21
</file>

<file path="LRUcache.py">
class LRUCache:

    def __init__(self, capacity: int):
        self.map = {}
        self.capacity  = capacity
        self.recent = None
        

    def get(self, key: int) -> int:
        if key not in self.map.keys():
            return -1
        else:
            self.recent = [key]
            return self.map[key]


        

    def put(self, key: int, value: int) -> None:
        if len(self.map) == self.capacity:
            to_delete = set(self.map.keys()).difference(self.recent)
            for d in to_delete:
                del self.map[d]
        
        self.map[key] = value
        self.recent = [key]

    def show(self):
        print("cache", self.map)
        


capacity =2
key=2
value=2
obj = LRUCache(capacity)
for i in range(0, 10):
    param_1 = obj.get(key)
    obj.put(key,value)
    obj.show()
    key = key+1
    value = value+1
</file>

<file path="online_election.py">
class TopVotedCandidate:

    def __init__(self, persons: List[int], times: List[int]):
        self.times = times
        self.persons = persons
        

    def q(self, t: int) -> int:
        if t in self.times:
            index = [i for i,v in enumerate(self.times) if v == t][0]
            return self.persons[index]
        else:
            index = [i for i,v in enumerate(self.times) if v > t][0]
            return self.persons[index-1]
        # print("t", t)
        # print("index",index)
        # print("================")
        # return self.persons[index]
        


# Your TopVotedCandidate object will be instantiated and called as such:
# obj = TopVotedCandidate(persons, times)
# param_1 = obj.q(t)

# Input
# ["TopVotedCandidate", "q", "q", "q", "q", "q", "q"]
# [[[0, 1, 1, 0, 0, 1, 0], [0, 5, 10, 15, 20, 25, 30]], [3], [12], [25], [15], [24], [8]]
# Output
# [null, 0, 1, 1, 0, 0, 1]
</file>

<file path="parkinglot.py">
import random
import threading

class Vehicle():
    def __init__(self, params) -> None:
        self.type = params.get("type")
        self.regid = params.get("regid")
        self.color = params.get("color")
        self.parked = False

        

    def getStatus(self):
        return self.parked
    


class TicketSystem():

    _instance_lock = threading.Lock()
    _unique_instance = None
    tickets = []

    def __new__(cls):
        with cls._instance_lock:
            if cls._unique_instance is None:
                cls._unique_instance = super(TicketSystem, cls).__new__(cls)
        return cls._unique_instance
   
    
    def genticket(self, params):
        self.parkid = "P" + str(random.randint(1,50))
        self.ticket = ""
        self.floor = params.get("floor")
        self.type = params.get("type")
        self.ticket = self.parkid + "_" + self.floor + "_" + self.type
        self.tickets.append(self.ticket)
        return self.ticket
    
    @classmethod
    def gettickets(self):
        print(self.tickets)
        


class ParkingSlot():
    def __init__(self,params) -> None:
        self.floors = params.get("floors")
        self.slots  = params.get("slots")
        self.floorslots = {}

    def addslots(self):
        for floor in range(self.floors):
            self.floorslots[floor]  = self.slots
            

    def allotparking(self, vehicle):
        for floor, val in self.floorslots.items():
            print("floor")
            if vehicle.type in val.get("slottype"):
                if val["slottype"][vehicle.type] > 0:
                    params = {"floor":str(floor),"type":vehicle.type}
                    ts = TicketSystem()
                    tkt = ts.genticket(params)
                    val["slottype"][vehicle.type]-=1
                    vehicle.parked  = True
                    return tkt


    def floorstatus(self):
        for floor,val in self.floorslots.items():
            print("Floor status for {} is {}".format(floor, val))



parkingslot = {    "floors" : 3, 
                   "slots": 
                        {
                            "slottype": {"truck":1,"bike":2,"car":4}
                        }
                         
              }

v1 = Vehicle({"type":"truck", "regid":1212, "color":"black"})
v2 = Vehicle({"type":"car", "regid":1313, "color":"red"})
v3 = Vehicle({"type":"bike", "regid":1414, "color":"white"})

pk = ParkingSlot(parkingslot)
pk.addslots()
pk.floorstatus()
tkt = pk.allotparking(v1)
print("tkt is", tkt)
pk.floorstatus()
tkt = pk.allotparking(v2)
print("tkt is", tkt)
pk.floorstatus()
tkt = pk.allotparking(v3)
print("tkt is", tkt)
pk.floorstatus()

TicketSystem.gettickets()

tk = TicketSystem()
print(tk.tickets)
</file>

<file path="rate_limiter_interview.py">
from threading import Thread, Lock
from datetime import datetime
import time
import math

# token capacity/bucketsize  = 10 for Token bucket
# requests allowed  = 10 requests per second per user

userInstance = { }


class Ratelimit():
    def __init__(self) -> None:
        
        self.req_per_sec = 5
        self.lock = Lock()


class TokenBucket(Ratelimit):
    def __init__(self) -> None:
        super().__init__()

        self.capacity = 10
        self.tokens_per_interval = 5
        self.token = 10
        self.last_updated = datetime.now()

    def allowed(self):
        with self.lock:
            if self.token <=0:
                print("request is throttled")
            
            fill_tokens_dynamic = self.fill_rate()
            self.token = min(self.capacity, fill_tokens_dynamic + self.token)
            

            if self.token > 0:
                self.token-=1
                print("request is allowed")
                return True

            

    def fill_rate(self):
        gap = (datetime.now() - self.last_updated).total_seconds()
        print("gap", gap)
        fill_tokens_dynamic = gap* self.tokens_per_interval
        print("dynamic tokens", fill_tokens_dynamic)
        self.last_updated = datetime.now()
        return fill_tokens_dynamic


class FixedTimeWindow(Ratelimit):
    def __init__(self) -> None:
        super().__init__()

        self.interval = 1
        self.count = 0
        self.start_time = datetime.now()

    def allowed(self):
        with self.lock:
            
            if (datetime.now() - self.start_time).total_seconds() > self.interval:
                self.count = 0
                self.start_time = datetime.now()
            
            if self.count > self.req_per_sec:
                print("Fixed time algo, requests throttled")
                return False
            
            self.count+=1
            print("Fixed time algo, request is allowed")
            return True

class SlidingWindow(Ratelimit):
    def __init__(self) -> None:
        super().__init__()

        self.count = 0
        self.last_updated  = datetime.now()
        self.interval = 1
        self.logs = []

    def allowed(self):
        with self.lock:
            # if (datetime.now() - self.last_updated).total_seconds() < self.interval:
            #     if self.count > self.req_per_sec:
            #         print("sliding window, request throttled")
            #         return False
            #     self.count+=1
            #     print("sliding window, request allowed")
            #     return True
            # else:
            #     self.last_updated = datetime.now()
            #     self.count = 0
            curr = datetime.now()
            while len(self.logs)>0 and (curr-self.logs[0]).total_seconds()>self.interval:
                self.logs.pop(0)

            if len(self.logs)>=self.req_per_sec:
                print("sliding window, request throttled")
                return
            
            self.logs.append(curr)
            print("sliding window, request allowed")
            return True


            
            




            




userids = [7,8,9,7,8,9,7,8,9]         
for user in userids:
    if user not in userInstance:
        print("first time user {}".format(user))
        userInstance[user] = SlidingWindow()
    else:
        for i in range(20):
            print("user spotted again {}".format(user))
            res= Thread(target = userInstance[user].allowed)
            time.sleep(0.1)
            res.start()
            print("count at this point", userInstance[user].count)
            # print("Tokens at this point", userInstance[user].token)

        


# tb = TokenBucket()

# for i in range(20):
#     res= Thread(target = tb.allowed)
#     time.sleep(0.1)
#     res.start()
#     print("Tokens at this point", tb.token)


# time.sleep(1)
# while True:
#     res= Thread(target = tb.allowed)
#     time.sleep(0.1)
#     res.start()
</file>

<file path="ratelimiting.py">
import time
import threading

class Packets():
    def __init__(self, stream=[]) -> None:
        self.packets = stream

class bucketdata():

    bucket = []
    _instance_lock = threading.Lock()
    _unique_instance = None

    def __new__(cls):
        with cls._instance_lock:
            if cls._unique_instance is None:
                cls._unique_instance = super(bucketdata, cls).__new__(cls)
        return cls._unique_instance
    

    def forwardPackets(self,stream):
        self.bucket.append(stream)

    @classmethod
    def getbucketstatus(self):
        print("data in bucket is", self.bucket)

class Tokenbucket():
    def __init__(self, rate, tokens=0) -> None:
        self.tokens = tokens
        self.rate = rate #seconds
        self.t1 = time.time()
        # threading.Timer(self.rate, self.addTokens).start()

    def check(self):
        if time.time() - self.t1 > self.rate:
            print("here")
            self.t1 = time.time()
            self.addTokens()


    def addTokens(self):
        self.tokens += 5
        print("no of tokens added")

    def getStatus(self):
        print("no of tokens in the bucket ", self.tokens)

    def process(self, packets):
        if len(packets.packets) <= self.tokens:
            self.tokens = self.tokens - len(packets.packets)
            bt = bucketdata()
            print("data passed", packets.packets )
            bt.forwardPackets(packets.packets)
        else:
            bt = bucketdata()
            print("data dropped", packets.packets[0:self.tokens])
            bt.forwardPackets(packets.packets[0:self.tokens])
            self.tokens = 0




        



tb = Tokenbucket(5)
p1= Packets([1,2,5,8])
p2= Packets([6,7,8,9])

tb.check()
tb.process(p1)
tb.check()
tb.process(p2)
tb.check()
tb.getStatus()
</file>

<file path="README_url_shortener.md">
# URL Shortener

A simple URL shortener implementation with the following features:
- Convert long URLs to short ones
- Redirect from short URLs to original long URLs
- Track basic analytics (number of clicks)
- Set expiration for links
- Custom short codes

## Components

1. **url_shortener.py** - Core implementation of the URL shortener
2. **url_shortener_cli.py** - Command-line interface
3. **url_shortener_web.py** - Web interface using Flask
4. **test_url_shortener.py** - Unit tests

## Requirements

- Python 3.6+
- Flask (for web interface)

## Installation

```bash
pip install flask
```

## Usage

### Command-line Interface

Run the CLI:

```bash
python url_shortener_cli.py
```

Commands:
- `shorten <url> [--custom <code>] [--expires <days>]` - Shorten a URL
- `resolve <code>` - Resolve a shortened URL
- `stats <code>` - Get statistics for a shortened URL
- `list` - List all shortened URLs
- `help` - Show help message
- `exit` - Exit the program

### Web Interface

Run the web server:

```bash
python url_shortener_web.py
```

Then open your browser and navigate to `http://localhost:5000/`

### API Endpoints

- `POST /api/shorten` - Shorten a URL
  - Request body: `{"url": "https://example.com", "custom_code": "optional", "expiration_days": 30}`
  - Response: `{"shortened_url": "http://localhost:5000/abc123"}`

- `GET /<short_code>` - Redirect to the original URL

- `GET /api/stats/<short_code>` - Get statistics for a shortened URL
  - Response: `{"original_url": "https://example.com", "creation_date": "...", "expiration_date": "...", "clicks": 5, "expired": false}`

- `GET /api/urls` - List all URLs
  - Response: JSON object with all shortened URLs and their details

## Running Tests

```bash
python -m unittest test_url_shortener.py
```

## Future Enhancements

1. Persistent storage (database)
2. User authentication
3. More detailed analytics
4. Rate limiting
5. QR code generation for shortened URLs
</file>

<file path="remove_dups.py">
from typing import List
class Solution:
   
   def removeDuplicates(self, nums: List[int]) -> int:
       j = 1
       for i in range(1, len(nums)):
           if nums[i] != nums[i - 1]:
               nums[j] = nums[i]
               j += 1
       return j
</file>

<file path="rev_linkedlist.py">
from typing import Optional, List

class ListNode:
    def __init__(self, val=0):
        self.val = val
        self.next = None



def reverseList(elements: Optional[List]):
    cur = dummy = ListNode(0)
    for e in elements:
        cur.next = ListNode(e)
        cur = cur.next
    head = dummy.next


    # Initialize prev pointer as NULL...
    prev = None
    # Initialize the curr pointer as the head...
    curr = head
    # Run a loop till curr points to NULL...
    while curr:
        # Initialize next pointer as the next pointer of curr...
        next = curr.next
        # Now assign the prev pointer to curr’s next pointer.
        curr.next = prev
        # Assign curr to prev, next to curr...
        prev = curr
        curr = next
    return prev
</file>

<file path="sample.py">
# from collections import OrderedDict

# mydict = OrderedDict()

# for k in mydict.items():
#   print(k)
# print('\n')
# mydict.pop('b')
# mydict['b'] = '2'
# for k in mydict.items():
#   print(k)

from collections import defaultdict

def defval():
  return 'default value'

mydict = defaultdict()
mydict['a'] = 1
mydict['b'] = 2
mydict['c'] = 3

for k in mydict.items():
  print(k)

print('\n') 

# if we try to get 'd' 
print(mydict['d']) 
# with a 'generic' dict this will raise a KeyError exception

print('\n') 

# it also add it to the dict
for k in mydict.items():
  print(k)
</file>

<file path="snake_game_interview.py">
from collections import deque

class SnakeGame():
    def __init__(self, width, height, food) -> None:
        self.w = width
        self.h = height
        self.food = deque()
        for f in food:
            self.food.append(f)
        self.snake = deque()
        self.head = [0,0]
        self.score = 0
        self.snake.append([0,0])


    def move(self, direction):
        if direction == "U":
            self.head[0] = self.head[0] - 1

        elif direction == "D":
            self.head[0] = self.head[0] + 1

        elif direction == "L":
            self.head[1] = self.head[1] - 1

        else:
            self.head[1] = self.head[1] + 1

        if self.head[0] < 0 or self.head[1] > self.w -1 or self.head[0] > self.h -1 or self.head[1] < 0:
            return -1

        
        if len(self.food) > 0:
            food = self.food.popleft()
            new_r, new_C = self.head[0], self.head[1]
            if self.head == food:
                self.snake.append([new_r, new_C])
                self.score+=1
            else:
                self.food.appendleft(food)
                self.snake.append([new_r, new_C])
                self.snake.popleft()
        else:
            return -1
        
        
        return self.score
            
        

        



sg = SnakeGame(3,2,[[1,2],[0,1]])
score  =  sg.move("R")
score  =  sg.move("D")
score  =  sg.move("R")
score  =  sg.move("U")
score  =  sg.move("L")
score  =  sg.move("U")
</file>

<file path="snake_game.py">
from collections import deque
from typing import List

class SnakeGame:

    def __init__(self, width: int, height: int, food: List[List[int]]):
        self.queue = deque()
        self.queue.append((0, 0))
        self.snake = set()
        self.snake.add((0, 0))
        self.width = width
        self.height = height
        self.food = food
        self.dirs = { "U": (-1, 0), "D": (1, 0), "R": (0, 1), "L": (0, -1) }
        self.head = (0, 0)
        self.score = 0
        self.curr_food = 0

    def move(self, direction: str) -> int:
        # Get new head position
        r_d, c_d = self.dirs[direction]
        new_r = self.head[0] + r_d
        new_c = self.head[1] + c_d
        
        # Check if out of bounds
        if 0 > new_r or new_r > self.height - 1 or 0 > new_c or new_c > self.width - 1:
            return -1

        # Increment score and curr food position if food at head
        if self.curr_food < len(self.food) and self.food[self.curr_food] == [new_r, new_c]:
            self.score += 1
            self.curr_food += 1
        # Move tail position away from last cell
        else:
            tail = self.queue.popleft()
            self.snake.remove(tail)
        # Check if self collision
        if (new_r, new_c) in self.snake:
            return -1
        # Move head to new spot
        self.queue.append((new_r, new_c))
        self.snake.add((new_r, new_c))
        self.head = (new_r, new_c)

        return self.score
    

sg = SnakeGame(3,2,[[1,2],[0,1]])
score  =  sg.move("R")
score  =  sg.move("D")
score  =  sg.move("R")
score  =  sg.move("U")
score  =  sg.move("L")
score  =  sg.move("U")
</file>

<file path="splitwise.py">
class User():
    def __init__(self, id, Name = "", email = "", contact =None) -> None:
        for name, value in zip(("id","name","email","contact"), (id, Name, email, contact)):
            setattr(self, name, value)
        self.balance = 0

    def getBal(self):
        message = "Bal for user id {} with user name {} is {}".format(self.id, self.name, self.balance)
        return message


class ShowExpense():
    def __init__(self, userid = None, userids = [] ) -> None:
        self.message = "No expenses"
        self.userid = userid
        self.userids  = userids

    def show(self):
        if self.userid:
            print (self.userid.getBal())
        else:
            for users in self.userids:
                print (users.getBal())


class ExpenseCal():

    def __init__(self, type, paidid = None, amount = 0, share = [], percentage = [], paidfor = []):
        self.splittype = type
        self.paidid = paidid
        self.amount = amount
        self.share = share
        self.percentage = percentage
        self.paidfor = paidfor

    def split(self):

        if self.splittype == "Equal":
            self.ids = self.paidfor
            for uobjelse in self.ids:
                # uobjelse = User(uid)
                uobjelse.balance = uobjelse.balance + round(self.amount / len(self.ids))
        elif self.splittype == "Exact":
            i=0
            for uobjelse in self.paidfor:
                # uobjelse = User(uid)
                uobjelse.balance = uobjelse.balance + self.share[i]
                i+=1
        else:
            i=0
            self.ids = self.paidfor
            for uobjelse in self.ids:
                # uobjelse = User(uid)
                uobjelse.balance = uobjelse.balance + round(self.amount * 0.01*self.percentage[i])
                i+=1



u1ob = User('u1',"Peeyush","pdc.com",123)
u2ob = User('u2',"Nupur","ndc.com",245)
u3ob = User('u3', "Nushka","nudc.com",678)
u4ob = User('u4',"Iyana","idc.com",911)


userids = [u1ob,u2ob,u3ob,u4ob]

sob = ShowExpense()
# sob.show()

eob = ExpenseCal("Equal", u1ob, 100, [],[],[u1ob,u2ob,u3ob,u4ob])
eob.split()
sob1 = ShowExpense(u1ob)
sob1.show()
eob = ExpenseCal("Equal", u1ob, 200, [],[],[u1ob,u2ob,u3ob,u4ob])
eob.split()
sob.show()
eob = ExpenseCal("Exact", u1ob, 200, [100,50,50],[],[u2ob,u3ob,u4ob])
eob.split()
sob.show()
eob = ExpenseCal("Percentage", u1ob, 300, [],[25,25,50],[u2ob,u3ob,u4ob])
eob.split()
sob.show()
</file>

<file path="test_url_shortener.py">
"""
Tests for the URL Shortener implementation.
"""
import unittest
from url_shortener import URLShortener
from datetime import datetime, timedelta
import time

class TestURLShortener(unittest.TestCase):
    """Test cases for the URLShortener class."""
    
    def setUp(self):
        """Set up a new URLShortener instance for each test."""
        self.shortener = URLShortener(domain="test.com/")
    
    def test_shorten_url(self):
        """Test shortening a URL."""
        original_url = "https://example.com"
        shortened_url = self.shortener.shorten_url(original_url)
        
        # Check that the shortened URL starts with the domain
        self.assertTrue(shortened_url.startswith("test.com/"))
        
        # Check that the short code is 6 characters long
        short_code = shortened_url.replace("test.com/", "")
        self.assertEqual(len(short_code), 6)
    
    def test_get_original_url(self):
        """Test retrieving the original URL."""
        original_url = "https://example.com"
        shortened_url = self.shortener.shorten_url(original_url)
        short_code = shortened_url.replace("test.com/", "")
        
        # Check that we can retrieve the original URL
        retrieved_url = self.shortener.get_original_url(short_code)
        self.assertEqual(retrieved_url, original_url)
        
        # Check that the click count was incremented
        stats = self.shortener.get_url_stats(short_code)
        self.assertEqual(stats['clicks'], 1)
    
    def test_custom_code(self):
        """Test using a custom code."""
        original_url = "https://example.com"
        custom_code = "my-custom-code"
        shortened_url = self.shortener.shorten_url(original_url, custom_code)
        
        # Check that the shortened URL contains the custom code
        self.assertEqual(shortened_url, f"test.com/{custom_code}")
        
        # Check that we can retrieve the original URL using the custom code
        retrieved_url = self.shortener.get_original_url(custom_code)
        self.assertEqual(retrieved_url, original_url)
    
    def test_duplicate_custom_code(self):
        """Test using a duplicate custom code."""
        original_url1 = "https://example.com"
        original_url2 = "https://example.org"
        custom_code = "my-custom-code"
        
        # Shorten the first URL with the custom code
        self.shortener.shorten_url(original_url1, custom_code)
        
        # Try to shorten the second URL with the same custom code
        result = self.shortener.shorten_url(original_url2, custom_code)
        self.assertTrue("already in use" in result)
    
    def test_expiration(self):
        """Test URL expiration."""
        original_url = "https://example.com"
        # Set expiration to 0 days (immediate expiration for testing)
        shortened_url = self.shortener.shorten_url(original_url, expiration_days=0)
        short_code = shortened_url.replace("test.com/", "")
        
        # Force the URL to expire by manipulating the expiration date
        url_details = self.shortener.url_mapping[short_code]
        url_details['expiration_date'] = datetime.now() - timedelta(seconds=1)
        
        # Check that the URL has expired
        retrieved_url = self.shortener.get_original_url(short_code)
        self.assertEqual(retrieved_url, "URL has expired.")
        
        # Check that the URL is marked as expired in the stats
        stats = self.shortener.get_url_stats(short_code)
        self.assertTrue(stats['expired'])
    
    def test_nonexistent_url(self):
        """Test retrieving a nonexistent URL."""
        retrieved_url = self.shortener.get_original_url("nonexistent")
        self.assertEqual(retrieved_url, "URL not found.")
        
        stats = self.shortener.get_url_stats("nonexistent")
        self.assertEqual(stats, "URL not found.")
    
    def test_list_all_urls(self):
        """Test listing all URLs."""
        # Shorten some URLs
        self.shortener.shorten_url("https://example.com")
        self.shortener.shorten_url("https://example.org", "custom")
        
        # List all URLs
        urls = self.shortener.list_all_urls()
        
        # Check that we have 2 URLs
        self.assertEqual(len(urls), 2)
        
        # Check that one of them has a custom code
        for code, details in urls.items():
            if 'custom_codes' in details and details['custom_codes']:
                self.assertEqual(details['custom_codes'][0], "custom")
                break
        else:
            self.fail("No URL with custom code found")

if __name__ == "__main__":
    unittest.main()
</file>

<file path="tic-tac-interview.py">
import numpy as np


class TicTacToe:

    def __init__(self, n: int):
        self.grid = np.zeros((n,n))

        self.players_dict = {}

        self.poss_array = [ [(0,0), (0,1), (0,2)] , [(0,0),(1,1),(2,2)] , [(2,0), 
                                    (1,1),(0,2)], [(0,0),(1,0),(2,0)],[(2,0),(2,1),(2,2)], [(0,2),(1,2),(2,2)] ]
                

    def move(self, row: int, col: int, player: int) -> int:
        self.grid[row, col] = player
        if player not in self.players_dict:
            self.players_dict.setdefault(player, [])
            self.players_dict[player].append((row,col))
        else:
            self.players_dict[player].append((row,col))

        print("player moves so far for player {}".format(player),self.players_dict[player])
        for i in self.poss_array:
            if all(a in self.players_dict[player] for a in i):
                if player == 1:
                    return 1
                elif player == 2:
                    return 2
        return 0
    

["TicTacToe","move","move","move","move","move","move","move"]
[[3],[0,0,1],[0,2,2],[2,2,1],[1,1,2],[2,0,1],[1,0,2],[2,1,1]]
</file>

<file path="tic-tac.py">
import numpy as np

grid = np.array([[0, 0, 0],
                    [0, 0, 0],
                    [0, 0, 0]])
player_list =[1,2]

players_dict = {}

for player in player_list:
    players_dict[player] = []

poss_array = [ [(0,0), (0,1), (0,2)] , [(0,0),(1,1),(2,2)] , [(2,0), 
                            (1,1),(0,2)], [(0,0),(1,0),(2,0)],[(2,0),(2,1),(2,2)], [(0,2),(1,2),(2,2)] ]


    
def board():

    return grid
    

def findmove(player):
    indices =  np.random.randint(0, high=3, size=2)

    # Extract the row and column indices
    i = indices[0]
    j = indices[1]

    if grid[i,j] == 0:
        grid[i,j] = player
    else:
        return

    players_dict[player].append((i,j))

    print("grid", grid)
    print("==================")
    print("==================")


def check_win(player):
    print("player moves so far for player {}".format(player),players_dict[player])
    for i in poss_array:
        if all(a in players_dict[player] for a in i):
            return True
    return False
    


b= board()
print("initial grid", b)
print("==================")
while (True):
    flag = 0
    for player in player_list:
        findmove(player)
        if check_win(player) is True:
            print("Player {} has won the game".format(player))
            flag = 1
            break
    if flag == 1:
        break

    if np.any(grid) is True:
        print("no one won")
        break
</file>

<file path="url_shortener_cli.py">
"""
Command-line interface for the URL Shortener.
"""
from url_shortener import URLShortener
import sys

def print_help():
    """Print help information."""
    print("\nURL Shortener CLI")
    print("=================")
    print("Commands:")
    print("  shorten <url> [--custom <code>] [--expires <days>] - Shorten a URL")
    print("  resolve <code> - Resolve a shortened URL")
    print("  stats <code> - Get statistics for a shortened URL")
    print("  list - List all shortened URLs")
    print("  help - Show this help message")
    print("  exit - Exit the program")
    print()

def main():
    """Main function to run the CLI."""
    shortener = URLShortener()
    print("URL Shortener CLI")
    print("Type 'help' for available commands")
    
    while True:
        try:
            command = input("\n> ").strip()
            
            if command == "exit":
                print("Goodbye!")
                break
                
            elif command == "help":
                print_help()
                
            elif command.startswith("shorten "):
                parts = command.split()
                url = parts[1]
                
                custom_code = None
                expiration_days = None
                
                # Parse optional arguments
                i = 2
                while i < len(parts):
                    if parts[i] == "--custom" and i + 1 < len(parts):
                        custom_code = parts[i + 1]
                        i += 2
                    elif parts[i] == "--expires" and i + 1 < len(parts):
                        try:
                            expiration_days = int(parts[i + 1])
                            i += 2
                        except ValueError:
                            print("Error: Expiration days must be a number")
                            i += 2
                    else:
                        i += 1
                
                shortened_url = shortener.shorten_url(url, custom_code, expiration_days)
                print(f"Shortened URL: {shortened_url}")
                
            elif command.startswith("resolve "):
                code = command.split()[1]
                original_url = shortener.get_original_url(code)
                print(f"Original URL: {original_url}")
                
            elif command.startswith("stats "):
                code = command.split()[1]
                stats = shortener.get_url_stats(code)
                if isinstance(stats, str):
                    print(stats)
                else:
                    print("\nURL Statistics:")
                    print(f"Original URL: {stats['original_url']}")
                    print(f"Creation Date: {stats['creation_date']}")
                    print(f"Expiration Date: {stats['expiration_date']}")
                    print(f"Clicks: {stats['clicks']}")
                    print(f"Expired: {stats['expired']}")
                
            elif command == "list":
                urls = shortener.list_all_urls()
                if not urls:
                    print("No URLs have been shortened yet.")
                else:
                    print("\nAll Shortened URLs:")
                    for code, details in urls.items():
                        custom_codes = details.get('custom_codes', [])
                        custom_str = f" (Custom: {', '.join(custom_codes)})" if custom_codes else ""
                        print(f"{code}{custom_str}: {details['original_url']} - Clicks: {details['clicks']}")
                
            else:
                print("Unknown command. Type 'help' for available commands.")
                
        except Exception as e:
            print(f"Error: {e}")

if __name__ == "__main__":
    main()
</file>

<file path="url_shortener_web.py">
"""
Web interface for the URL Shortener using Flask.
"""
from flask import Flask, request, jsonify, redirect, render_template_string
from url_shortener import URLShortener
import os

app = Flask(__name__)
shortener = URLShortener(domain="http://localhost:5000/")

# HTML template for the home page
HOME_TEMPLATE = """
<!DOCTYPE html>
<html>
<head>
    <title>URL Shortener</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            background-color: #f9f9f9;
            border-radius: 5px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #333;
        }
        input, button {
            padding: 10px;
            margin: 5px 0;
        }
        input[type="text"], input[type="number"] {
            width: 100%;
            box-sizing: border-box;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
            border-radius: 3px;
        }
        button:hover {
            background-color: #45a049;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            background-color: #e9f7ef;
            border-radius: 3px;
        }
        .urls-list {
            margin-top: 30px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>URL Shortener</h1>
        
        <form id="shortenForm">
            <div>
                <label for="url">URL to shorten:</label>
                <input type="text" id="url" name="url" placeholder="https://example.com" required>
            </div>
            
            <div>
                <label for="custom_code">Custom code (optional):</label>
                <input type="text" id="custom_code" name="custom_code" placeholder="my-custom-code">
            </div>
            
            <div>
                <label for="expiration_days">Expiration days (optional):</label>
                <input type="number" id="expiration_days" name="expiration_days" placeholder="30">
            </div>
            
            <button type="submit">Shorten URL</button>
        </form>
        
        <div id="result" class="result" style="display: none;"></div>
        
        <div class="urls-list">
            <h2>Recent URLs</h2>
            <table>
                <thead>
                    <tr>
                        <th>Short URL</th>
                        <th>Original URL</th>
                        <th>Clicks</th>
                        <th>Expiration</th>
                    </tr>
                </thead>
                <tbody id="urlsList">
                    {% for code, details in urls.items() %}
                    <tr>
                        <td>
                            <a href="{{ shortener_domain }}{{ code }}" target="_blank">
                                {{ shortener_domain }}{{ code }}
                            </a>
                            {% if details.custom_codes %}
                            <br>
                            <small>Custom: 
                                {% for custom in details.custom_codes %}
                                <a href="{{ shortener_domain }}{{ custom }}" target="_blank">{{ custom }}</a>
                                {% endfor %}
                            </small>
                            {% endif %}
                        </td>
                        <td>{{ details.original_url }}</td>
                        <td>{{ details.clicks }}</td>
                        <td>
                            {% if details.expired %}
                            <span style="color: red;">Expired</span>
                            {% elif details.expiration_date != 'Never' %}
                            {{ details.expiration_date }}
                            {% else %}
                            Never
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    
    <script>
        document.getElementById('shortenForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const url = document.getElementById('url').value;
            const customCode = document.getElementById('custom_code').value;
            const expirationDays = document.getElementById('expiration_days').value;
            
            fetch('/api/shorten', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    url: url,
                    custom_code: customCode || null,
                    expiration_days: expirationDays ? parseInt(expirationDays) : null
                }),
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('result');
                resultDiv.style.display = 'block';
                
                if (data.error) {
                    resultDiv.innerHTML = `<p>Error: ${data.error}</p>`;
                } else {
                    resultDiv.innerHTML = `
                        <p>Shortened URL: <a href="${data.shortened_url}" target="_blank">${data.shortened_url}</a></p>
                    `;
                }
                
                // Reload the page to update the URLs list
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            })
            .catch(error => {
                console.error('Error:', error);
                const resultDiv = document.getElementById('result');
                resultDiv.style.display = 'block';
                resultDiv.innerHTML = `<p>Error: ${error.message}</p>`;
            });
        });
    </script>
</body>
</html>
"""

@app.route('/')
def home():
    """Render the home page."""
    urls = shortener.list_all_urls()
    return render_template_string(
        HOME_TEMPLATE, 
        urls=urls, 
        shortener_domain=shortener.domain
    )

@app.route('/<short_code>')
def redirect_to_url(short_code):
    """Redirect to the original URL."""
    original_url = shortener.get_original_url(short_code)
    if original_url.startswith('http'):
        return redirect(original_url)
    else:
        return jsonify({'error': original_url}), 404

@app.route('/api/shorten', methods=['POST'])
def shorten_url():
    """API endpoint to shorten a URL."""
    data = request.json
    url = data.get('url')
    custom_code = data.get('custom_code')
    expiration_days = data.get('expiration_days')
    
    if not url:
        return jsonify({'error': 'URL is required'}), 400
    
    try:
        shortened_url = shortener.shorten_url(url, custom_code, expiration_days)
        if shortened_url.startswith('Custom code'):
            return jsonify({'error': shortened_url}), 400
        return jsonify({'shortened_url': shortened_url})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/stats/<short_code>')
def get_stats(short_code):
    """API endpoint to get URL statistics."""
    stats = shortener.get_url_stats(short_code)
    if isinstance(stats, str):
        return jsonify({'error': stats}), 404
    return jsonify(stats)

@app.route('/api/urls')
def list_urls():
    """API endpoint to list all URLs."""
    urls = shortener.list_all_urls()
    return jsonify(urls)

if __name__ == '__main__':
    app.run(debug=True)
</file>

<file path="url_shortener.py">
"""
URL Shortener Implementation

This module implements a simple URL shortener service with the following features:
- Convert long URLs to short ones
- Redirect from short URLs to original long URLs
- Track basic analytics (number of clicks)
- Set expiration for links
"""
import random
import string
import time
from datetime import datetime, timedelta

class URLShortener:
    def __init__(self, domain="short.url/"):
        """
        Initialize the URL shortener with a base domain and empty storage.
        
        Args:
            domain (str): The base domain for shortened URLs
        """
        self.domain = domain
        self.url_mapping = {}  # Maps short codes to URL details
        self.custom_mapping = {}  # Maps custom codes to short codes
        
    def generate_short_code(self, length=6):
        """
        Generate a random short code of specified length.
        
        Args:
            length (int): Length of the short code
            
        Returns:
            str: A random string of specified length
        """
        characters = string.ascii_letters + string.digits
        return ''.join(random.choice(characters) for _ in range(length))
    
    def shorten_url(self, original_url, custom_code=None, expiration_days=None):
        """
        Shorten a URL and optionally set a custom code and expiration.
        
        Args:
            original_url (str): The original URL to shorten
            custom_code (str, optional): Custom code for the shortened URL
            expiration_days (int, optional): Number of days until the URL expires
            
        Returns:
            str: The shortened URL
        """
        # Check if URL already exists to avoid duplicates
        for code, details in self.url_mapping.items():
            if details['original_url'] == original_url and not details.get('expired', False):
                return f"{self.domain}{code}"
        
        # Generate a short code
        if custom_code:
            if custom_code in self.custom_mapping:
                return f"Custom code '{custom_code}' already in use."
            short_code = self.generate_short_code()
            self.custom_mapping[custom_code] = short_code
        else:
            short_code = self.generate_short_code()
            while short_code in self.url_mapping:
                short_code = self.generate_short_code()
        
        # Set expiration date if provided
        expiration_date = None
        if expiration_days:
            expiration_date = datetime.now() + timedelta(days=expiration_days)
        
        # Store the URL details
        self.url_mapping[short_code] = {
            'original_url': original_url,
            'creation_date': datetime.now(),
            'expiration_date': expiration_date,
            'clicks': 0,
            'expired': False
        }
        
        return f"{self.domain}{short_code if not custom_code else custom_code}"
    
    def get_original_url(self, short_code):
        """
        Get the original URL from a short code and track the click.
        
        Args:
            short_code (str): The short code part of the shortened URL
            
        Returns:
            str: The original URL or an error message
        """
        # Check if it's a custom code
        if short_code in self.custom_mapping:
            short_code = self.custom_mapping[short_code]
        
        # Check if the short code exists
        if short_code not in self.url_mapping:
            return "URL not found."
        
        url_details = self.url_mapping[short_code]
        
        # Check if the URL has expired
        if url_details.get('expiration_date') and datetime.now() > url_details['expiration_date']:
            url_details['expired'] = True
            return "URL has expired."
        
        # Increment click count
        url_details['clicks'] += 1
        
        return url_details['original_url']
    
    def get_url_stats(self, short_code):
        """
        Get statistics for a shortened URL.
        
        Args:
            short_code (str): The short code part of the shortened URL
            
        Returns:
            dict: Statistics for the URL or an error message
        """
        # Check if it's a custom code
        if short_code in self.custom_mapping:
            short_code = self.custom_mapping[short_code]
        
        # Check if the short code exists
        if short_code not in self.url_mapping:
            return "URL not found."
        
        url_details = self.url_mapping[short_code]
        
        # Check if the URL has expired
        if url_details.get('expiration_date') and datetime.now() > url_details['expiration_date']:
            url_details['expired'] = True
        
        return {
            'original_url': url_details['original_url'],
            'creation_date': url_details['creation_date'],
            'expiration_date': url_details.get('expiration_date', 'Never'),
            'clicks': url_details['clicks'],
            'expired': url_details.get('expired', False)
        }
    
    def list_all_urls(self):
        """
        List all shortened URLs and their details.
        
        Returns:
            dict: A dictionary of all shortened URLs and their details
        """
        result = {}
        for code, details in self.url_mapping.items():
            # Check if the URL has expired
            if details.get('expiration_date') and datetime.now() > details['expiration_date']:
                details['expired'] = True
            
            # Find any custom codes for this short code
            custom_codes = [c for c, sc in self.custom_mapping.items() if sc == code]
            
            result[code] = {
                'original_url': details['original_url'],
                'creation_date': details['creation_date'],
                'expiration_date': details.get('expiration_date', 'Never'),
                'clicks': details['clicks'],
                'expired': details.get('expired', False),
                'custom_codes': custom_codes
            }
        
        return result
</file>

<file path="voting_another_interview.py">
from collections import OrderedDict
import heapq

class VotingSystem():
    def __init__(self, list_of_votes) -> None:
        self.heap = []
        self.voting_list = list_of_votes
        self.countDict = {}
        self.len = len(list_of_votes[0])

    def rankVotes(self):
        final_voting = []
        for ele in self.voting_list:
            temp = list(ele)
            for i, team in enumerate(temp):
                self.countDict[team] = self.countDict.get(team, 0) + i + 1

        for i in self.countDict.keys():
            heapq.heappush(self.heap, (self.countDict[i], i))

        voting_order = heapq.nsmallest(self.len,self.heap)

        print("heap", self.heap)
        print("voting order", voting_order)

        for i,j in voting_order:
            final_voting.append(j)
        return final_voting
        


ob = VotingSystem(["ABC","ACB","ABC","ACB","ACB"])

ob.rankVotes()
</file>

<file path="voting.py">
from collections import OrderedDict

class VotingSystem():
    def __init__(self, teams) -> None:
        self.voting_list = []
        self.countdict = OrderedDict()
        for team in teams:
            self.countdict[team] = []
        

    def vote(self,votefirst,votesecond,votethird):
        self.votefirst = votefirst
        self.votesecond = votesecond
        self.votethird = votethird

        templist  = self.votefirst + self.votesecond + self.votethird

        self.voting_list.append(templist)
        print("voting list",self.voting_list)
        return self.voting_list
        

    def rank(self, result):
        for votes in result:
            i=3
            for c in votes:
                self.countdict[c].append(i)
                i-=1
        print("countdict", self.countdict)
        return self.countdict
    




class ResultVoting():
    def __init__(self) -> None:
        pass

    def resultVote(self, res):
        result_dict = OrderedDict()
        for key, val in res.items():
            result_dict[key] = sum(val)



        result_dict = sorted(result_dict, key=result_dict.get, reverse=True)
        print("result dict", result_dict)
        return "".join(result_dict)




ob = VotingSystem(["A","B","C"])

res = ob.vote("A", "B","C")
res = ob.vote("A", "C","B")
res = ob.vote("A", "C","B")
res = ob.vote("A", "C","B")

ranked = ob.rank(res)

re = ResultVoting()
re.resultVote(ranked)
</file>

</files>
