from collections import OrderedDict
import heapq

class VotingSystem():
    def __init__(self, list_of_votes) -> None:
        self.heap = []
        self.voting_list = list_of_votes
        self.countDict = {}
        self.len = len(list_of_votes[0])

    def rankVotes(self):
        final_voting = []
        for ele in self.voting_list:
            temp = list(ele)
            for i, team in enumerate(temp):
                self.countDict[team] = self.countDict.get(team, 0) + i + 1

        for i in self.countDict.keys():
            heapq.heappush(self.heap, (self.countDict[i], i))

        voting_order = heapq.nsmallest(self.len,self.heap)

        print("heap", self.heap)
        print("voting order", voting_order)

        for i,j in voting_order:
            final_voting.append(j)
        return final_voting
        


ob = VotingSystem(["ABC","ACB","ABC","ACB","ACB"])

ob.rankVotes()


