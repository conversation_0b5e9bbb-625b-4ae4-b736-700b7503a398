from collections import deque
from typing import List

class SnakeGame:

    def __init__(self, width: int, height: int, food: List[List[int]]):
        self.queue = deque()
        self.queue.append((0, 0))
        self.snake = set()
        self.snake.add((0, 0))
        self.width = width
        self.height = height
        self.food = food
        self.dirs = { "U": (-1, 0), "D": (1, 0), "R": (0, 1), "L": (0, -1) }
        self.head = (0, 0)
        self.score = 0
        self.curr_food = 0

    def move(self, direction: str) -> int:
        # Get new head position
        r_d, c_d = self.dirs[direction]
        new_r = self.head[0] + r_d
        new_c = self.head[1] + c_d
        
        # Check if out of bounds
        if 0 > new_r or new_r > self.height - 1 or 0 > new_c or new_c > self.width - 1:
            return -1

        # Increment score and curr food position if food at head
        if self.curr_food < len(self.food) and self.food[self.curr_food] == [new_r, new_c]:
            self.score += 1
            self.curr_food += 1
        # Move tail position away from last cell
        else:
            tail = self.queue.popleft()
            self.snake.remove(tail)
        # Check if self collision
        if (new_r, new_c) in self.snake:
            return -1
        # Move head to new spot
        self.queue.append((new_r, new_c))
        self.snake.add((new_r, new_c))
        self.head = (new_r, new_c)

        return self.score
    

sg = SnakeGame(3,2,[[1,2],[0,1]])
score  =  sg.move("R")
score  =  sg.move("D")
score  =  sg.move("R")
score  =  sg.move("U")
score  =  sg.move("L")
score  =  sg.move("U")