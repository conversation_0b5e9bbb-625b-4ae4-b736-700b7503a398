# Service Boundaries and Decomposition

## Introduction

Defining service boundaries is one of the most critical and challenging aspects of microservices architecture. Poor service boundaries can lead to tight coupling, data inconsistency, and operational complexity. This section explores strategies and principles for identifying and defining effective service boundaries.

## Domain-Driven Design (DDD) Principles

Domain-Driven Design provides a powerful framework for identifying service boundaries through the concept of bounded contexts.

### Bounded Contexts

A bounded context is a logical boundary within which a particular domain model is defined and applicable.

**Key Characteristics**:
- Clear ownership of data and business logic
- Consistent terminology and models
- Well-defined interfaces with other contexts
- Independent evolution capability

**Example: E-commerce System**

```
┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐
│   User Context  │  │ Product Context │  │  Order Context  │
│                 │  │                 │  │                 │
│ - User Profile  │  │ - Product Info  │  │ - Order Details │
│ - Authentication│  │ - Inventory     │  │ - Order Status  │
│ - Preferences   │  │ - Pricing       │  │ - Payment Info  │
│                 │  │                 │  │                 │
└─────────────────┘  └─────────────────┘  └─────────────────┘
```

### Ubiquitous Language

Each bounded context should have its own ubiquitous language - a common vocabulary shared by domain experts and developers.

**Example: Different Contexts, Different Meanings**

```
User Context:
- Customer: A person who has registered an account

Product Context:
- Customer: A business that purchases products for resale

Order Context:
- Customer: The entity responsible for payment and delivery
```

### Aggregates

Aggregates are clusters of domain objects that can be treated as a single unit for data changes.

**Characteristics**:
- Consistency boundary
- Transactional boundary
- Single root entity (aggregate root)
- Internal objects accessed only through the root

**Example: Order Aggregate**

```javascript
// Order aggregate in JavaScript
class Order {
  constructor(customerId, items) {
    this.id = generateId();
    this.customerId = customerId;
    this.items = items.map(item => new OrderItem(item));
    this.status = 'PENDING';
    this.total = this.calculateTotal();
    this.createdAt = new Date();
  }
  
  // Business logic encapsulated within the aggregate
  addItem(productId, quantity, price) {
    if (this.status !== 'PENDING') {
      throw new Error('Cannot modify confirmed order');
    }
    
    const existingItem = this.items.find(item => item.productId === productId);
    if (existingItem) {
      existingItem.updateQuantity(existingItem.quantity + quantity);
    } else {
      this.items.push(new OrderItem({ productId, quantity, price }));
    }
    
    this.total = this.calculateTotal();
  }
  
  confirm() {
    if (this.items.length === 0) {
      throw new Error('Cannot confirm empty order');
    }
    
    this.status = 'CONFIRMED';
    this.confirmedAt = new Date();
    
    // Emit domain event
    this.addEvent(new OrderConfirmedEvent(this.id, this.customerId, this.total));
  }
  
  calculateTotal() {
    return this.items.reduce((total, item) => total + item.getSubtotal(), 0);
  }
}

class OrderItem {
  constructor({ productId, quantity, price }) {
    this.productId = productId;
    this.quantity = quantity;
    this.price = price;
  }
  
  updateQuantity(newQuantity) {
    if (newQuantity <= 0) {
      throw new Error('Quantity must be positive');
    }
    this.quantity = newQuantity;
  }
  
  getSubtotal() {
    return this.quantity * this.price;
  }
}
```

## Business Capability Identification

Services should be organized around business capabilities rather than technical layers.

### Business Capability Mapping

**Process**:
1. Identify core business functions
2. Map organizational structure
3. Analyze data flow and dependencies
4. Define capability boundaries

**Example: E-commerce Business Capabilities**

```
E-commerce Platform
├── Customer Management
│   ├── User Registration
│   ├── Profile Management
│   └── Authentication
├── Product Catalog
│   ├── Product Information
│   ├── Inventory Management
│   └── Pricing
├── Order Management
│   ├── Shopping Cart
│   ├── Order Processing
│   └── Order Tracking
├── Payment Processing
│   ├── Payment Methods
│   ├── Transaction Processing
│   └── Refunds
└── Fulfillment
    ├── Shipping
    ├── Delivery Tracking
    └── Returns
```

### Service Mapping

Each business capability can potentially become a microservice:

```javascript
// Service structure based on business capabilities
const services = {
  userService: {
    responsibilities: [
      'User registration and authentication',
      'Profile management',
      'User preferences'
    ],
    data: ['users', 'profiles', 'preferences'],
    apis: [
      'POST /users/register',
      'POST /users/login',
      'GET /users/{id}/profile',
      'PUT /users/{id}/profile'
    ]
  },
  
  productService: {
    responsibilities: [
      'Product catalog management',
      'Inventory tracking',
      'Pricing management'
    ],
    data: ['products', 'inventory', 'prices'],
    apis: [
      'GET /products',
      'GET /products/{id}',
      'PUT /products/{id}/inventory',
      'GET /products/{id}/price'
    ]
  },
  
  orderService: {
    responsibilities: [
      'Order creation and management',
      'Order status tracking',
      'Order history'
    ],
    data: ['orders', 'order_items'],
    apis: [
      'POST /orders',
      'GET /orders/{id}',
      'PUT /orders/{id}/status',
      'GET /users/{id}/orders'
    ]
  }
};
```

## Data Ownership and Bounded Contexts

Each service should own its data and be the single source of truth for that data.

### Database per Service Pattern

**Principles**:
- Each service has its own database
- Services don't share databases
- Data access only through service APIs
- Different services can use different database technologies

**Example Implementation**:

```javascript
// User Service - Uses PostgreSQL
class UserService {
  constructor() {
    this.db = new PostgreSQLConnection(process.env.USER_DB_URL);
  }
  
  async createUser(userData) {
    const user = await this.db.query(
      'INSERT INTO users (email, name, created_at) VALUES ($1, $2, $3) RETURNING *',
      [userData.email, userData.name, new Date()]
    );
    
    // Publish event for other services
    await this.eventBus.publish('user.created', {
      userId: user.id,
      email: user.email,
      name: user.name
    });
    
    return user;
  }
  
  async getUser(userId) {
    return await this.db.query('SELECT * FROM users WHERE id = $1', [userId]);
  }
}

// Product Service - Uses MongoDB
class ProductService {
  constructor() {
    this.db = new MongoDBConnection(process.env.PRODUCT_DB_URL);
  }
  
  async createProduct(productData) {
    const product = await this.db.collection('products').insertOne({
      ...productData,
      createdAt: new Date(),
      updatedAt: new Date()
    });
    
    await this.eventBus.publish('product.created', {
      productId: product.insertedId,
      name: productData.name,
      category: productData.category
    });
    
    return product;
  }
  
  async getProduct(productId) {
    return await this.db.collection('products').findOne({ _id: productId });
  }
}

// Order Service - Uses PostgreSQL with different schema
class OrderService {
  constructor() {
    this.db = new PostgreSQLConnection(process.env.ORDER_DB_URL);
  }
  
  async createOrder(orderData) {
    const transaction = await this.db.beginTransaction();
    
    try {
      // Create order
      const order = await transaction.query(
        'INSERT INTO orders (user_id, status, total, created_at) VALUES ($1, $2, $3, $4) RETURNING *',
        [orderData.userId, 'PENDING', orderData.total, new Date()]
      );
      
      // Create order items
      for (const item of orderData.items) {
        await transaction.query(
          'INSERT INTO order_items (order_id, product_id, quantity, price) VALUES ($1, $2, $3, $4)',
          [order.id, item.productId, item.quantity, item.price]
        );
      }
      
      await transaction.commit();
      
      await this.eventBus.publish('order.created', {
        orderId: order.id,
        userId: order.user_id,
        total: order.total,
        items: orderData.items
      });
      
      return order;
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }
}
```

### Data Consistency Strategies

When data is distributed across services, maintaining consistency requires careful design.

**Eventual Consistency with Events**:

```javascript
// Event-driven data synchronization
class UserProfileService {
  constructor() {
    this.db = new PostgreSQLConnection(process.env.PROFILE_DB_URL);
    this.eventBus = new EventBus();
    
    // Subscribe to user events from User Service
    this.eventBus.subscribe('user.created', this.handleUserCreated.bind(this));
    this.eventBus.subscribe('user.updated', this.handleUserUpdated.bind(this));
  }
  
  async handleUserCreated(event) {
    // Create a profile record when a user is created
    await this.db.query(
      'INSERT INTO user_profiles (user_id, email, display_name, created_at) VALUES ($1, $2, $3, $4)',
      [event.userId, event.email, event.name, new Date()]
    );
  }
  
  async handleUserUpdated(event) {
    // Update profile when user information changes
    await this.db.query(
      'UPDATE user_profiles SET display_name = $1, updated_at = $2 WHERE user_id = $3',
      [event.name, new Date(), event.userId]
    );
  }
  
  async updateProfile(userId, profileData) {
    const profile = await this.db.query(
      'UPDATE user_profiles SET bio = $1, avatar_url = $2, updated_at = $3 WHERE user_id = $4 RETURNING *',
      [profileData.bio, profileData.avatarUrl, new Date(), userId]
    );
    
    await this.eventBus.publish('profile.updated', {
      userId,
      bio: profileData.bio,
      avatarUrl: profileData.avatarUrl
    });
    
    return profile;
  }
}
```

## Service Sizing Considerations

Determining the right size for microservices is crucial for maintainability and performance.

### The "Two Pizza Team" Rule

Amazon's principle: a service should be maintainable by a team that can be fed with two pizzas (typically 6-8 people).

### Factors Affecting Service Size

1. **Team Size and Structure**
   - Align service boundaries with team boundaries
   - Consider team expertise and domain knowledge
   - Account for operational responsibilities

2. **Business Logic Complexity**
   - Simple services: Single responsibility, minimal logic
   - Complex services: Rich domain logic, multiple related functions

3. **Data Volume and Performance**
   - High-volume services may need to be smaller for better scaling
   - Performance-critical services may need optimization focus

4. **Change Frequency**
   - Frequently changing areas should be isolated
   - Stable areas can be larger services

### Service Size Examples

**Too Small (Nano-services)**:
```javascript
// Anti-pattern: Services that are too granular
class EmailValidationService {
  validateEmail(email) {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  }
}

class PasswordHashingService {
  hashPassword(password) {
    return bcrypt.hash(password, 10);
  }
}
```

**Appropriate Size**:
```javascript
// Better: Cohesive user management service
class UserManagementService {
  async registerUser(userData) {
    // Validate input
    if (!this.validateEmail(userData.email)) {
      throw new Error('Invalid email format');
    }
    
    // Hash password
    const hashedPassword = await this.hashPassword(userData.password);
    
    // Create user
    const user = await this.createUser({
      ...userData,
      password: hashedPassword
    });
    
    // Send welcome email
    await this.sendWelcomeEmail(user);
    
    return user;
  }
  
  async authenticateUser(email, password) {
    const user = await this.getUserByEmail(email);
    if (!user || !await this.verifyPassword(password, user.password)) {
      throw new Error('Invalid credentials');
    }
    
    return this.generateAuthToken(user);
  }
  
  // Private helper methods
  private validateEmail(email) { /* ... */ }
  private hashPassword(password) { /* ... */ }
  private createUser(userData) { /* ... */ }
  private sendWelcomeEmail(user) { /* ... */ }
}
```

## Conway's Law and Organizational Alignment

Conway's Law states that organizations design systems that mirror their communication structure.

### Applying Conway's Law

**Align Service Boundaries with Team Boundaries**:

```
Organization Structure:
├── Frontend Team (Web/Mobile)
├── User Experience Team
├── Product Catalog Team
├── Order Processing Team
├── Payment Team
└── Operations Team

Corresponding Service Architecture:
├── API Gateway (Frontend Team)
├── User Service (User Experience Team)
├── Product Service (Product Catalog Team)
├── Order Service (Order Processing Team)
├── Payment Service (Payment Team)
└── Monitoring/Logging (Operations Team)
```

### Team Topology Patterns

**Stream-Aligned Teams**: Teams aligned to a flow of work from a business domain
**Enabling Teams**: Teams that help stream-aligned teams overcome obstacles
**Complicated Subsystem Teams**: Teams responsible for complex technical subsystems
**Platform Teams**: Teams that provide internal services to other teams

## Decomposition Strategies

### Strangler Fig Pattern

Gradually replace parts of a monolith by intercepting calls and routing them to new services.

**Implementation Example**:

```javascript
// API Gateway implementing Strangler Fig pattern
class StranglerFigGateway {
  constructor() {
    this.routes = new Map();
    this.monolithUrl = process.env.MONOLITH_URL;
    this.setupRoutes();
  }
  
  setupRoutes() {
    // Routes that have been migrated to microservices
    this.routes.set('/api/users', {
      service: 'user-service',
      url: process.env.USER_SERVICE_URL
    });
    
    this.routes.set('/api/products', {
      service: 'product-service',
      url: process.env.PRODUCT_SERVICE_URL
    });
    
    // Orders still handled by monolith
    // this.routes.set('/api/orders', { ... });
  }
  
  async handleRequest(req, res) {
    const route = this.findRoute(req.path);
    
    if (route) {
      // Route to microservice
      return await this.proxyToService(req, res, route);
    } else {
      // Route to monolith
      return await this.proxyToMonolith(req, res);
    }
  }
  
  findRoute(path) {
    for (const [pattern, config] of this.routes) {
      if (path.startsWith(pattern)) {
        return config;
      }
    }
    return null;
  }
  
  async proxyToService(req, res, route) {
    const serviceUrl = `${route.url}${req.path}`;
    // Proxy request to microservice
    return await this.proxy(req, res, serviceUrl);
  }
  
  async proxyToMonolith(req, res) {
    const monolithUrl = `${this.monolithUrl}${req.path}`;
    // Proxy request to monolith
    return await this.proxy(req, res, monolithUrl);
  }
}
```

### Database Decomposition

Split shared databases before extracting services.

**Steps**:
1. Identify data ownership boundaries
2. Create separate schemas or databases
3. Implement data synchronization
4. Extract services

**Example**:

```sql
-- Original monolithic database
CREATE TABLE users (
  id SERIAL PRIMARY KEY,
  email VARCHAR(255) UNIQUE,
  name VARCHAR(255),
  password_hash VARCHAR(255),
  created_at TIMESTAMP
);

CREATE TABLE products (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255),
  description TEXT,
  price DECIMAL(10,2),
  inventory_count INTEGER
);

CREATE TABLE orders (
  id SERIAL PRIMARY KEY,
  user_id INTEGER REFERENCES users(id),
  total DECIMAL(10,2),
  status VARCHAR(50),
  created_at TIMESTAMP
);

-- After decomposition: User Service Database
CREATE TABLE users (
  id SERIAL PRIMARY KEY,
  email VARCHAR(255) UNIQUE,
  name VARCHAR(255),
  password_hash VARCHAR(255),
  created_at TIMESTAMP
);

-- Product Service Database
CREATE TABLE products (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255),
  description TEXT,
  price DECIMAL(10,2),
  inventory_count INTEGER
);

-- Order Service Database
CREATE TABLE orders (
  id SERIAL PRIMARY KEY,
  user_id INTEGER, -- No foreign key constraint
  total DECIMAL(10,2),
  status VARCHAR(50),
  created_at TIMESTAMP
);

CREATE TABLE order_items (
  id SERIAL PRIMARY KEY,
  order_id INTEGER REFERENCES orders(id),
  product_id INTEGER, -- No foreign key constraint
  quantity INTEGER,
  price DECIMAL(10,2)
);
```

## Common Decomposition Pitfalls

### 1. Distributed Monolith

Creating services that are tightly coupled and must be deployed together.

**Anti-pattern**:
```javascript
// Services that are too tightly coupled
class OrderService {
  async createOrder(orderData) {
    // Synchronous calls to multiple services
    const user = await this.userService.getUser(orderData.userId);
    const product = await this.productService.getProduct(orderData.productId);
    const inventory = await this.inventoryService.checkInventory(orderData.productId);
    const price = await this.pricingService.getPrice(orderData.productId);
    
    // If any service is down, order creation fails
    return await this.saveOrder({ user, product, inventory, price, ...orderData });
  }
}
```

**Better Approach**:
```javascript
// Loosely coupled with eventual consistency
class OrderService {
  async createOrder(orderData) {
    // Create order with available data
    const order = await this.saveOrder({
      userId: orderData.userId,
      productId: orderData.productId,
      quantity: orderData.quantity,
      status: 'PENDING_VALIDATION'
    });
    
    // Publish event for asynchronous validation
    await this.eventBus.publish('order.created', {
      orderId: order.id,
      userId: orderData.userId,
      productId: orderData.productId,
      quantity: orderData.quantity
    });
    
    return order;
  }
  
  // Handle validation results asynchronously
  async handleValidationResult(event) {
    if (event.valid) {
      await this.updateOrderStatus(event.orderId, 'CONFIRMED');
    } else {
      await this.updateOrderStatus(event.orderId, 'REJECTED');
    }
  }
}
```

### 2. Chatty Interfaces

Services that require many round trips to complete operations.

**Anti-pattern**:
```javascript
// Multiple API calls to get related data
async function getOrderDetails(orderId) {
  const order = await orderService.getOrder(orderId);
  const user = await userService.getUser(order.userId);
  const items = [];
  
  for (const item of order.items) {
    const product = await productService.getProduct(item.productId);
    items.push({ ...item, product });
  }
  
  return { order, user, items };
}
```

**Better Approach**:
```javascript
// Aggregate data within services or use events
class OrderService {
  async getOrderDetails(orderId) {
    // Return order with denormalized data
    const order = await this.db.query(`
      SELECT o.*, u.name as user_name, u.email as user_email,
             oi.product_id, oi.quantity, oi.price,
             p.name as product_name
      FROM orders o
      JOIN order_users u ON o.user_id = u.user_id
      JOIN order_items oi ON o.id = oi.order_id
      JOIN order_products p ON oi.product_id = p.product_id
      WHERE o.id = $1
    `, [orderId]);
    
    return this.formatOrderDetails(order);
  }
}
```

## Interview Questions

### Question 1: How would you decompose a monolithic e-commerce application into microservices?

**Key Points to Address**:

1. **Domain Analysis**:
   - Identify business capabilities (user management, product catalog, orders, payments, shipping)
   - Map data ownership and relationships
   - Analyze team structure and expertise
   - Consider transaction boundaries

2. **Service Identification**:
   - User/Account Service: Authentication, profiles, preferences
   - Product Service: Catalog, inventory, pricing
   - Order Service: Cart, order processing, order history
   - Payment Service: Payment processing, billing
   - Notification Service: Email, SMS, push notifications
   - Shipping Service: Delivery, tracking

3. **Decomposition Strategy**:
   - Start with Strangler Fig pattern
   - Extract services incrementally
   - Begin with least coupled components
   - Implement event-driven communication

4. **Data Strategy**:
   - Database per service
   - Event sourcing for audit trails
   - Eventual consistency between services
   - Saga pattern for distributed transactions

### Question 2: What are the key considerations when defining service boundaries?

**Key Points**:

1. **Business Alignment**:
   - Align with business capabilities
   - Consider organizational structure (Conway's Law)
   - Map to team responsibilities
   - Ensure clear ownership

2. **Technical Considerations**:
   - Data ownership and consistency requirements
   - Performance and scalability needs
   - Technology stack preferences
   - Operational complexity

3. **Size and Scope**:
   - Single responsibility principle
   - Team size (two pizza rule)
   - Change frequency and deployment independence
   - Avoid distributed monoliths

4. **Communication Patterns**:
   - Minimize inter-service communication
   - Design for loose coupling
   - Consider network latency and reliability
   - Plan for service evolution

## Practical Exercise

### Exercise: Design Service Boundaries for a Social Media Platform

Design microservices for a social media platform with the following features:

1. User registration and profiles
2. Posts and content sharing
3. Following/followers relationships
4. News feed generation
5. Messaging between users
6. Notifications
7. Content moderation

**Requirements**:
- Identify service boundaries using DDD principles
- Define data ownership for each service
- Design APIs for inter-service communication
- Consider scalability and performance requirements
- Plan for eventual consistency

## Next Steps

In the next section, we'll explore [Inter-Service Communication](02_Inter_Service_Communication.md), which covers the various patterns and technologies for enabling services to communicate effectively.

## Resources

1. [Building Microservices](https://www.oreilly.com/library/view/building-microservices/9781491950340/) by Sam Newman
2. [Domain-Driven Design](https://www.domainlanguage.com/ddd/) by Eric Evans
3. [Microservices Patterns](https://microservices.io/patterns/) by Chris Richardson
4. [Team Topologies](https://teamtopologies.com/) by Matthew Skelton and Manuel Pais
5. [Martin Fowler's Microservices Articles](https://martinfowler.com/articles/microservices.html)
