# Inter-Service Communication

## Introduction

In a microservices architecture, services need to communicate with each other to fulfill business requirements. The choice of communication patterns and protocols significantly impacts system performance, reliability, and maintainability. This section explores various communication strategies and their appropriate use cases.

## Communication Patterns

### 1. Synchronous vs. Asynchronous Communication

Understanding when to use synchronous versus asynchronous communication is crucial for building resilient microservices.

#### Synchronous Communication

**Characteristics**:
- Request-response pattern
- Caller waits for response
- Direct coupling between services
- Immediate consistency

**When to Use**:
- Real-time data requirements
- Simple request-response scenarios
- When immediate feedback is needed
- Critical path operations

**Example: REST API Call**

```javascript
// Order Service calling User Service synchronously
class OrderService {
  constructor() {
    this.userServiceUrl = process.env.USER_SERVICE_URL;
    this.httpClient = new HttpClient();
  }
  
  async createOrder(orderData) {
    try {
      // Synchronous call to validate user
      const user = await this.httpClient.get(
        `${this.userServiceUrl}/users/${orderData.userId}`
      );
      
      if (!user.active) {
        throw new Error('User account is inactive');
      }
      
      // Create order with validated user data
      const order = await this.saveOrder({
        ...orderData,
        userEmail: user.email,
        userName: user.name
      });
      
      return order;
    } catch (error) {
      if (error.code === 'SERVICE_UNAVAILABLE') {
        throw new Error('Unable to validate user at this time');
      }
      throw error;
    }
  }
}
```

#### Asynchronous Communication

**Characteristics**:
- Fire-and-forget or publish-subscribe pattern
- Caller doesn't wait for response
- Loose coupling between services
- Eventual consistency

**When to Use**:
- Non-critical operations
- Event-driven workflows
- High-volume scenarios
- When services can operate independently

**Example: Event-Driven Communication**

```javascript
// Order Service publishing events asynchronously
class OrderService {
  constructor() {
    this.eventBus = new EventBus();
  }
  
  async createOrder(orderData) {
    // Create order immediately
    const order = await this.saveOrder({
      ...orderData,
      status: 'PENDING'
    });
    
    // Publish event for other services to handle
    await this.eventBus.publish('order.created', {
      orderId: order.id,
      userId: orderData.userId,
      items: orderData.items,
      total: order.total,
      timestamp: new Date()
    });
    
    return order;
  }
}

// Inventory Service handling order events
class InventoryService {
  constructor() {
    this.eventBus = new EventBus();
    this.eventBus.subscribe('order.created', this.handleOrderCreated.bind(this));
  }
  
  async handleOrderCreated(event) {
    try {
      // Reserve inventory for order items
      for (const item of event.items) {
        await this.reserveInventory(item.productId, item.quantity);
      }
      
      // Publish success event
      await this.eventBus.publish('inventory.reserved', {
        orderId: event.orderId,
        items: event.items
      });
    } catch (error) {
      // Publish failure event
      await this.eventBus.publish('inventory.reservation.failed', {
        orderId: event.orderId,
        reason: error.message
      });
    }
  }
}
```

### 2. Request-Response Pattern

Direct communication where one service sends a request and waits for a response.

#### REST APIs

**Implementation Example**:

```javascript
// Product Service REST API
const express = require('express');
const app = express();

app.use(express.json());

// Get product details
app.get('/products/:id', async (req, res) => {
  try {
    const product = await productRepository.findById(req.params.id);
    if (!product) {
      return res.status(404).json({ error: 'Product not found' });
    }
    res.json(product);
  } catch (error) {
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Update product inventory
app.put('/products/:id/inventory', async (req, res) => {
  try {
    const { quantity } = req.body;
    const product = await productRepository.updateInventory(req.params.id, quantity);
    res.json(product);
  } catch (error) {
    res.status(500).json({ error: 'Failed to update inventory' });
  }
});

// Client code for calling the API
class ProductServiceClient {
  constructor(baseUrl) {
    this.baseUrl = baseUrl;
    this.httpClient = new HttpClient();
  }
  
  async getProduct(productId) {
    const response = await this.httpClient.get(`${this.baseUrl}/products/${productId}`);
    return response.data;
  }
  
  async updateInventory(productId, quantity) {
    const response = await this.httpClient.put(
      `${this.baseUrl}/products/${productId}/inventory`,
      { quantity }
    );
    return response.data;
  }
}
```

#### gRPC

gRPC provides efficient, type-safe communication with built-in features like load balancing and health checking.

**Protocol Buffer Definition**:

```protobuf
// user.proto
syntax = "proto3";

package user;

service UserService {
  rpc GetUser(GetUserRequest) returns (GetUserResponse);
  rpc CreateUser(CreateUserRequest) returns (CreateUserResponse);
  rpc UpdateUser(UpdateUserRequest) returns (UpdateUserResponse);
}

message User {
  string id = 1;
  string email = 2;
  string name = 3;
  bool active = 4;
  int64 created_at = 5;
}

message GetUserRequest {
  string id = 1;
}

message GetUserResponse {
  User user = 1;
}

message CreateUserRequest {
  string email = 1;
  string name = 2;
}

message CreateUserResponse {
  User user = 1;
}
```

**Server Implementation (Node.js)**:

```javascript
const grpc = require('@grpc/grpc-js');
const protoLoader = require('@grpc/proto-loader');

// Load protocol buffer
const packageDefinition = protoLoader.loadSync('user.proto');
const userProto = grpc.loadPackageDefinition(packageDefinition).user;

// Implement service methods
const userService = {
  async getUser(call, callback) {
    try {
      const userId = call.request.id;
      const user = await userRepository.findById(userId);
      
      if (!user) {
        return callback({
          code: grpc.status.NOT_FOUND,
          message: 'User not found'
        });
      }
      
      callback(null, { user });
    } catch (error) {
      callback({
        code: grpc.status.INTERNAL,
        message: 'Internal server error'
      });
    }
  },
  
  async createUser(call, callback) {
    try {
      const userData = call.request;
      const user = await userRepository.create(userData);
      callback(null, { user });
    } catch (error) {
      callback({
        code: grpc.status.INTERNAL,
        message: 'Failed to create user'
      });
    }
  }
};

// Start gRPC server
const server = new grpc.Server();
server.addService(userProto.UserService.service, userService);
server.bindAsync('0.0.0.0:50051', grpc.ServerCredentials.createInsecure(), () => {
  server.start();
  console.log('gRPC server running on port 50051');
});
```

**Client Implementation**:

```javascript
// gRPC client
class UserServiceClient {
  constructor(serverAddress) {
    this.client = new userProto.UserService(
      serverAddress,
      grpc.credentials.createInsecure()
    );
  }
  
  async getUser(userId) {
    return new Promise((resolve, reject) => {
      this.client.getUser({ id: userId }, (error, response) => {
        if (error) {
          reject(error);
        } else {
          resolve(response.user);
        }
      });
    });
  }
  
  async createUser(userData) {
    return new Promise((resolve, reject) => {
      this.client.createUser(userData, (error, response) => {
        if (error) {
          reject(error);
        } else {
          resolve(response.user);
        }
      });
    });
  }
}
```

### 3. Event-Driven Communication

Services communicate through events, enabling loose coupling and scalability.

#### Message Queues

**Using RabbitMQ**:

```javascript
const amqp = require('amqplib');

// Publisher (Order Service)
class OrderEventPublisher {
  constructor() {
    this.connection = null;
    this.channel = null;
  }
  
  async connect() {
    this.connection = await amqp.connect(process.env.RABBITMQ_URL);
    this.channel = await this.connection.createChannel();
    
    // Declare exchange for order events
    await this.channel.assertExchange('orders', 'topic', { durable: true });
  }
  
  async publishOrderCreated(orderData) {
    const event = {
      type: 'order.created',
      data: orderData,
      timestamp: new Date(),
      version: '1.0'
    };
    
    await this.channel.publish(
      'orders',
      'order.created',
      Buffer.from(JSON.stringify(event)),
      { persistent: true }
    );
  }
  
  async publishOrderCancelled(orderId, reason) {
    const event = {
      type: 'order.cancelled',
      data: { orderId, reason },
      timestamp: new Date(),
      version: '1.0'
    };
    
    await this.channel.publish(
      'orders',
      'order.cancelled',
      Buffer.from(JSON.stringify(event)),
      { persistent: true }
    );
  }
}

// Consumer (Email Service)
class EmailEventConsumer {
  constructor() {
    this.connection = null;
    this.channel = null;
  }
  
  async connect() {
    this.connection = await amqp.connect(process.env.RABBITMQ_URL);
    this.channel = await this.connection.createChannel();
    
    // Declare exchange and queue
    await this.channel.assertExchange('orders', 'topic', { durable: true });
    const queue = await this.channel.assertQueue('email-service-orders', { durable: true });
    
    // Bind queue to specific events
    await this.channel.bindQueue(queue.queue, 'orders', 'order.created');
    await this.channel.bindQueue(queue.queue, 'orders', 'order.cancelled');
    
    // Start consuming messages
    await this.channel.consume(queue.queue, this.handleMessage.bind(this), { noAck: false });
  }
  
  async handleMessage(msg) {
    try {
      const event = JSON.parse(msg.content.toString());
      
      switch (event.type) {
        case 'order.created':
          await this.sendOrderConfirmationEmail(event.data);
          break;
        case 'order.cancelled':
          await this.sendOrderCancellationEmail(event.data);
          break;
        default:
          console.log('Unknown event type:', event.type);
      }
      
      // Acknowledge message processing
      this.channel.ack(msg);
    } catch (error) {
      console.error('Error processing message:', error);
      // Reject message and requeue
      this.channel.nack(msg, false, true);
    }
  }
  
  async sendOrderConfirmationEmail(orderData) {
    // Send confirmation email logic
    console.log('Sending order confirmation email for order:', orderData.orderId);
  }
  
  async sendOrderCancellationEmail(orderData) {
    // Send cancellation email logic
    console.log('Sending order cancellation email for order:', orderData.orderId);
  }
}
```

#### Event Streaming with Apache Kafka

**Producer Example**:

```javascript
const kafka = require('kafkajs');

class OrderEventProducer {
  constructor() {
    this.kafka = kafka({
      clientId: 'order-service',
      brokers: [process.env.KAFKA_BROKER]
    });
    this.producer = this.kafka.producer();
  }
  
  async connect() {
    await this.producer.connect();
  }
  
  async publishOrderEvent(eventType, orderData) {
    const event = {
      eventId: generateUUID(),
      eventType,
      aggregateId: orderData.orderId,
      data: orderData,
      timestamp: new Date().toISOString(),
      version: 1
    };
    
    await this.producer.send({
      topic: 'order-events',
      messages: [{
        key: orderData.orderId,
        value: JSON.stringify(event),
        headers: {
          'event-type': eventType,
          'content-type': 'application/json'
        }
      }]
    });
  }
}

// Consumer Example
class InventoryEventConsumer {
  constructor() {
    this.kafka = kafka({
      clientId: 'inventory-service',
      brokers: [process.env.KAFKA_BROKER]
    });
    this.consumer = this.kafka.consumer({ groupId: 'inventory-group' });
  }
  
  async connect() {
    await this.consumer.connect();
    await this.consumer.subscribe({ topic: 'order-events' });
    
    await this.consumer.run({
      eachMessage: async ({ topic, partition, message }) => {
        const event = JSON.parse(message.value.toString());
        await this.handleEvent(event);
      }
    });
  }
  
  async handleEvent(event) {
    switch (event.eventType) {
      case 'order.created':
        await this.reserveInventory(event.data);
        break;
      case 'order.cancelled':
        await this.releaseInventory(event.data);
        break;
      default:
        console.log('Unhandled event type:', event.eventType);
    }
  }
  
  async reserveInventory(orderData) {
    // Reserve inventory logic
    for (const item of orderData.items) {
      await this.inventoryRepository.reserve(item.productId, item.quantity);
    }
  }
}
```

## Error Handling and Resilience

### 1. Circuit Breaker Pattern

Prevent cascading failures by stopping calls to failing services.

**Implementation Example**:

```javascript
class CircuitBreaker {
  constructor(options = {}) {
    this.failureThreshold = options.failureThreshold || 5;
    this.resetTimeout = options.resetTimeout || 60000; // 1 minute
    this.monitoringPeriod = options.monitoringPeriod || 10000; // 10 seconds
    
    this.state = 'CLOSED'; // CLOSED, OPEN, HALF_OPEN
    this.failureCount = 0;
    this.lastFailureTime = null;
    this.successCount = 0;
  }
  
  async execute(operation) {
    if (this.state === 'OPEN') {
      if (Date.now() - this.lastFailureTime > this.resetTimeout) {
        this.state = 'HALF_OPEN';
        this.successCount = 0;
      } else {
        throw new Error('Circuit breaker is OPEN');
      }
    }
    
    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }
  
  onSuccess() {
    this.failureCount = 0;
    
    if (this.state === 'HALF_OPEN') {
      this.successCount++;
      if (this.successCount >= 3) { // Require 3 successes to close
        this.state = 'CLOSED';
      }
    }
  }
  
  onFailure() {
    this.failureCount++;
    this.lastFailureTime = Date.now();
    
    if (this.failureCount >= this.failureThreshold) {
      this.state = 'OPEN';
    }
  }
}

// Usage in service client
class UserServiceClient {
  constructor(baseUrl) {
    this.baseUrl = baseUrl;
    this.httpClient = new HttpClient();
    this.circuitBreaker = new CircuitBreaker({
      failureThreshold: 5,
      resetTimeout: 30000
    });
  }
  
  async getUser(userId) {
    return await this.circuitBreaker.execute(async () => {
      const response = await this.httpClient.get(`${this.baseUrl}/users/${userId}`);
      return response.data;
    });
  }
}
```

### 2. Retry with Exponential Backoff

Automatically retry failed operations with increasing delays.

**Implementation Example**:

```javascript
class RetryableHttpClient {
  constructor(options = {}) {
    this.maxRetries = options.maxRetries || 3;
    this.baseDelay = options.baseDelay || 1000; // 1 second
    this.maxDelay = options.maxDelay || 30000; // 30 seconds
    this.httpClient = new HttpClient();
  }
  
  async request(method, url, data = null, options = {}) {
    let lastError;
    
    for (let attempt = 0; attempt <= this.maxRetries; attempt++) {
      try {
        return await this.httpClient.request(method, url, data, options);
      } catch (error) {
        lastError = error;
        
        // Don't retry on client errors (4xx)
        if (error.status >= 400 && error.status < 500) {
          throw error;
        }
        
        // Don't retry on last attempt
        if (attempt === this.maxRetries) {
          break;
        }
        
        // Calculate delay with exponential backoff and jitter
        const delay = Math.min(
          this.baseDelay * Math.pow(2, attempt),
          this.maxDelay
        );
        const jitter = Math.random() * 0.1 * delay;
        
        await this.sleep(delay + jitter);
      }
    }
    
    throw lastError;
  }
  
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
```

### 3. Timeout Handling

Set appropriate timeouts to prevent hanging requests.

**Implementation Example**:

```javascript
class TimeoutHttpClient {
  constructor(defaultTimeout = 5000) {
    this.defaultTimeout = defaultTimeout;
  }
  
  async request(url, options = {}) {
    const timeout = options.timeout || this.defaultTimeout;
    
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);
    
    try {
      const response = await fetch(url, {
        ...options,
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);
      return response;
    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error.name === 'AbortError') {
        throw new Error(`Request timeout after ${timeout}ms`);
      }
      
      throw error;
    }
  }
}
```

## Service Mesh

Service mesh provides infrastructure-level communication features like load balancing, security, and observability.

### Istio Example Configuration

**Service Definition**:

```yaml
# user-service.yaml
apiVersion: v1
kind: Service
metadata:
  name: user-service
  labels:
    app: user-service
spec:
  ports:
  - port: 80
    targetPort: 3000
    name: http
  selector:
    app: user-service
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: user-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: user-service
  template:
    metadata:
      labels:
        app: user-service
    spec:
      containers:
      - name: user-service
        image: user-service:latest
        ports:
        - containerPort: 3000
```

**Traffic Management**:

```yaml
# virtual-service.yaml
apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: user-service
spec:
  http:
  - match:
    - headers:
        version:
          exact: v2
    route:
    - destination:
        host: user-service
        subset: v2
  - route:
    - destination:
        host: user-service
        subset: v1
---
apiVersion: networking.istio.io/v1alpha3
kind: DestinationRule
metadata:
  name: user-service
spec:
  host: user-service
  subsets:
  - name: v1
    labels:
      version: v1
  - name: v2
    labels:
      version: v2
  trafficPolicy:
    circuitBreaker:
      consecutiveErrors: 3
      interval: 30s
      baseEjectionTime: 30s
```

## Communication Protocols Comparison

| Protocol | Use Case | Pros | Cons |
|----------|----------|------|------|
| HTTP/REST | Web APIs, simple CRUD | Simple, widely supported, cacheable | Verbose, limited type safety |
| gRPC | High-performance RPC | Type-safe, efficient, streaming | HTTP/2 required, less human-readable |
| GraphQL | Flexible data fetching | Single endpoint, client-specified queries | Complex caching, potential N+1 problems |
| Message Queues | Asynchronous processing | Reliable delivery, decoupling | Additional infrastructure, eventual consistency |
| Event Streaming | Real-time data processing | High throughput, replay capability | Complex setup, ordering challenges |

## Best Practices

1. **Choose the Right Pattern**: Use synchronous for immediate consistency, asynchronous for scalability
2. **Implement Timeouts**: Always set appropriate timeouts for external calls
3. **Handle Failures Gracefully**: Use circuit breakers, retries, and fallbacks
4. **Monitor Communication**: Track latency, error rates, and throughput
5. **Version APIs**: Plan for API evolution and backward compatibility
6. **Secure Communication**: Use TLS, authentication, and authorization
7. **Optimize for Performance**: Consider caching, compression, and connection pooling

## Interview Questions

### Question 1: How would you handle a scenario where a critical service dependency is frequently unavailable?

**Key Points to Address**:

1. **Immediate Mitigation**:
   - Implement circuit breaker pattern
   - Add retry logic with exponential backoff
   - Set appropriate timeouts
   - Provide fallback mechanisms

2. **Resilience Patterns**:
   - Cache responses when possible
   - Implement graceful degradation
   - Use bulkhead pattern to isolate failures
   - Consider asynchronous processing

3. **Long-term Solutions**:
   - Analyze root cause of unavailability
   - Implement health checks and monitoring
   - Consider service redundancy
   - Evaluate SLA requirements

4. **Communication Strategy**:
   - Switch to asynchronous communication where possible
   - Implement event-driven architecture
   - Use message queues for reliable delivery
   - Consider eventual consistency models

### Question 2: Compare synchronous and asynchronous communication patterns in microservices. When would you use each?

**Key Points**:

1. **Synchronous Communication**:
   - **When to use**: Real-time requirements, simple request-response, immediate consistency needed
   - **Pros**: Simple to understand, immediate feedback, strong consistency
   - **Cons**: Tight coupling, cascading failures, performance bottlenecks
   - **Examples**: User authentication, payment processing, real-time queries

2. **Asynchronous Communication**:
   - **When to use**: Non-critical operations, high-volume scenarios, loose coupling desired
   - **Pros**: Better scalability, fault isolation, loose coupling
   - **Cons**: Eventual consistency, complex error handling, debugging challenges
   - **Examples**: Email notifications, audit logging, data synchronization

3. **Hybrid Approach**:
   - Use synchronous for critical path operations
   - Use asynchronous for side effects and non-critical operations
   - Implement saga pattern for distributed transactions
   - Consider CQRS for read/write separation

## Practical Exercise

### Exercise: Design Communication Patterns for an E-commerce System

Design communication patterns for an e-commerce system with these services:
- User Service
- Product Service
- Order Service
- Payment Service
- Inventory Service
- Notification Service

**Requirements**:
1. Define synchronous and asynchronous communication patterns
2. Implement error handling and resilience patterns
3. Design event schemas for asynchronous communication
4. Consider performance and scalability requirements
5. Plan for service evolution and versioning

## Next Steps

In the next section, we'll explore [Service Discovery and Registry](03_Service_Discovery.md), which covers how services find and communicate with each other in a dynamic microservices environment.

## Resources

1. [Microservices Communication Patterns](https://microservices.io/patterns/communication-style/)
2. [gRPC Documentation](https://grpc.io/docs/)
3. [Apache Kafka Documentation](https://kafka.apache.org/documentation/)
4. [RabbitMQ Tutorials](https://www.rabbitmq.com/getstarted.html)
5. [Istio Service Mesh](https://istio.io/latest/docs/)
