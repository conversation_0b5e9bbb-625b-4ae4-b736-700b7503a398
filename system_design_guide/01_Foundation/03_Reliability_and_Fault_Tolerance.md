# Reliability and Fault Tolerance

## Introduction

Reliability and fault tolerance are critical aspects of system design that ensure applications continue to function correctly even when components fail. In this module, we'll explore strategies for building resilient systems that can withstand various types of failures.

## Understanding Reliability

Reliability refers to a system's ability to perform its intended functions correctly and consistently over time. A reliable system:

- Continues to work correctly even when things go wrong
- Performs at the expected level under normal conditions
- Can handle invalid inputs or unexpected user behavior
- Prevents unauthorized access and abuse

### Measuring Reliability

Reliability is often measured using these metrics:

**Mean Time Between Failures (MTBF)**: The average time between system failures.

**Mean Time To Recovery (MTTR)**: The average time needed to restore the system after a failure.

**Availability**: The percentage of time a system is operational, often expressed in "nines":
- Two nines (99%): ~87.6 hours of downtime per year
- Three nines (99.9%): ~8.8 hours of downtime per year
- Four nines (99.99%): ~52.6 minutes of downtime per year
- Five nines (99.999%): ~5.3 minutes of downtime per year

**Service Level Objectives (SLOs)**: Target levels of service reliability, usually defined in terms of availability or response time.

## Types of Failures

Understanding different failure types helps in designing appropriate fault tolerance mechanisms:

### 1. Hardware Failures

**Description**: Physical component failures such as server crashes, disk failures, network outages, or power loss.

**Impact**: Can affect single machines or entire data centers.

**Examples**:
- Hard drive crashes causing data loss
- Network switch failures isolating parts of the system
- Power outages affecting entire data centers

### 2. Software Failures

**Description**: Bugs, memory leaks, resource exhaustion, or unhandled exceptions in application code.

**Impact**: Can cause individual services to crash or behave incorrectly.

**Examples**:
- Memory leaks gradually consuming all available RAM
- Unhandled edge cases causing crashes
- Deadlocks preventing progress

### 3. Dependency Failures

**Description**: Failures in external services or systems that your application depends on.

**Impact**: Can propagate through the system, causing cascading failures.

**Examples**:
- Database unavailability affecting all dependent services
- Third-party API outages
- DNS resolution failures

### 4. Network Failures

**Description**: Communication issues between system components.

**Impact**: Can cause partial system failures or inconsistent behavior.

**Examples**:
- Network partitions isolating parts of a distributed system
- Packet loss causing timeouts
- High latency affecting performance

### 5. Human Errors

**Description**: Mistakes made by operators, developers, or users.

**Impact**: Can cause unexpected system behavior or outages.

**Examples**:
- Accidental deletion of data
- Misconfiguration during deployment
- Incorrect manual interventions during incidents

## Fault Tolerance Strategies

### 1. Redundancy

**Concept**: Duplicating critical components to eliminate single points of failure.

**Types**:
- **Hardware redundancy**: Multiple servers, network paths, power supplies
- **Geographic redundancy**: Distributing systems across multiple data centers
- **Data redundancy**: Maintaining multiple copies of data

**Implementation**:
- Deploy applications across multiple availability zones
- Use RAID configurations for storage
- Implement database replication

**Example**:
```
# AWS Multi-AZ deployment in Terraform
resource "aws_db_instance" "example" {
  engine               = "mysql"
  instance_class       = "db.t3.micro"
  allocated_storage    = 20
  name                 = "mydb"
  username             = "admin"
  password             = "password"
  multi_az             = true  # Enable Multi-AZ deployment for redundancy
  backup_retention_period = 7
}
```

### 2. Isolation and Bulkheads

**Concept**: Containing failures to prevent them from cascading through the system.

**Implementation**:
- Separate critical services from non-critical ones
- Use containers or VMs to isolate components
- Implement resource limits per component
- Design independent failure domains

**Example**:
```java
// Resource isolation using thread pools in Java
ThreadPoolExecutor criticalPool = new ThreadPoolExecutor(
    10, 20, 60, TimeUnit.SECONDS, new ArrayBlockingQueue<>(100));

ThreadPoolExecutor nonCriticalPool = new ThreadPoolExecutor(
    5, 10, 60, TimeUnit.SECONDS, new ArrayBlockingQueue<>(100));

// Critical operations use one pool
criticalPool.submit(() -> processCriticalTask());

// Non-critical operations use another pool
nonCriticalPool.submit(() -> processNonCriticalTask());
```

### 3. Timeouts

**Concept**: Setting maximum time limits for operations to prevent indefinite waiting.

**Implementation**:
- Add timeouts to all external calls
- Make timeout values configurable
- Consider the implications of timeouts (e.g., retries)

**Example**:
```python
import requests
from requests.exceptions import Timeout

try:
    # Set a 3-second timeout for this request
    response = requests.get('https://api.example.com/data', timeout=3)
    data = response.json()
except Timeout:
    # Handle the timeout case
    data = get_cached_data()  # Fall back to cached data
```

### 4. Retries with Backoff

**Concept**: Automatically retrying failed operations with increasing delays between attempts.

**Implementation**:
- Use exponential backoff to avoid overwhelming the target
- Add jitter to prevent synchronized retries
- Set a maximum retry limit

**Example**:
```javascript
async function fetchWithRetry(url, maxRetries = 3) {
  let retries = 0;
  
  while (true) {
    try {
      return await fetch(url);
    } catch (error) {
      if (retries >= maxRetries) throw error;
      
      // Exponential backoff with jitter
      const delay = Math.min(100 * Math.pow(2, retries), 5000);
      const jitter = Math.random() * 200;
      await new Promise(resolve => setTimeout(resolve, delay + jitter));
      
      retries++;
    }
  }
}
```

### 5. Circuit Breakers

**Concept**: Automatically stopping operations that are likely to fail, preventing system overload.

**States**:
- **Closed**: Operations proceed normally
- **Open**: Operations fail fast without attempting execution
- **Half-Open**: Limited operations are allowed to test if the problem is resolved

**Implementation**:
- Monitor failure rates
- Trip the circuit when failures exceed a threshold
- Automatically reset after a cooling period

**Example**:
```java
// Using Resilience4j in Java
CircuitBreaker circuitBreaker = CircuitBreakerRegistry.ofDefaults()
    .circuitBreaker("backendService");

Supplier<String> decoratedSupplier = CircuitBreaker
    .decorateSupplier(circuitBreaker, () -> backendService.doSomething());

// The circuit breaker will track failures and open if necessary
String result = Try.ofSupplier(decoratedSupplier)
    .recover(throwable -> "Fallback value").get();
```

### 6. Graceful Degradation

**Concept**: Reducing functionality instead of failing completely when resources are constrained or dependencies are unavailable.

**Implementation**:
- Identify core vs. non-core features
- Implement fallbacks for critical operations
- Design for partial availability

**Example**:
```python
def get_product_details(product_id):
    # Try to get full details with recommendations
    try:
        product = get_basic_product_info(product_id)
        
        # Try to get recommendations, but continue without them if unavailable
        try:
            recommendations = recommendation_service.get_recommendations(product_id, timeout=1)
            product['recommendations'] = recommendations
        except ServiceUnavailableError:
            # Degrade gracefully by omitting recommendations
            product['recommendations'] = []
            log.warning("Recommendation service unavailable")
            
        return product
    except Exception as e:
        # Last resort fallback to cached data
        log.error(f"Failed to get product details: {e}")
        return get_cached_product(product_id)
```

### 7. Load Shedding

**Concept**: Selectively dropping requests when the system is overloaded to prevent complete failure.

**Implementation**:
- Prioritize different types of requests
- Reject low-priority requests during high load
- Implement rate limiting at entry points

**Example**:
```java
// Simple load shedding based on queue size
public Response processRequest(Request request) {
    if (isHighPriorityRequest(request)) {
        // Always process high-priority requests
        return actuallyProcessRequest(request);
    } else {
        // Check current load for low-priority requests
        int queueSize = getRequestQueueSize();
        if (queueSize > LOAD_THRESHOLD) {
            // Shed load by rejecting the request
            return Response.status(503)
                .entity("Service temporarily overloaded, please try again later")
                .build();
        } else {
            return actuallyProcessRequest(request);
        }
    }
}
```

### 8. Fail Fast

**Concept**: Detecting and reporting failures as quickly as possible rather than trying to proceed with likely-to-fail operations.

**Implementation**:
- Validate inputs early
- Check preconditions before starting operations
- Fail immediately when dependencies are unavailable

**Example**:
```csharp
public void ProcessOrder(Order order)
{
    // Fail fast by validating inputs immediately
    if (order == null)
        throw new ArgumentNullException(nameof(order));
        
    if (string.IsNullOrEmpty(order.CustomerId))
        throw new ArgumentException("Customer ID is required");
        
    if (order.Items.Count == 0)
        throw new ArgumentException("Order must contain at least one item");
    
    // Check if inventory service is available before proceeding
    if (!inventoryService.IsAvailable())
        throw new ServiceUnavailableException("Inventory service is down");
    
    // Now proceed with the actual processing
    // ...
}
```

## Designing for Fault Tolerance

### 1. Stateless Design

**Concept**: Designing services that don't store client-specific state between requests, making them easier to replicate and recover.

**Benefits**:
- Any server can handle any request
- Easier horizontal scaling
- Simpler recovery after failures

**Implementation**:
- Store state in external systems (databases, caches)
- Use tokens or cookies for client identification
- Pass necessary context in each request

### 2. Idempotent Operations

**Concept**: Designing operations that can be repeated multiple times without causing additional side effects beyond the first execution.

**Benefits**:
- Safe to retry after failures
- Simplifies error handling
- Improves system reliability

**Example**:
```python
# Idempotent payment processing using a transaction ID
def process_payment(payment_request):
    transaction_id = payment_request.transaction_id
    
    # Check if this transaction was already processed
    if database.transaction_exists(transaction_id):
        return get_existing_transaction(transaction_id)
    
    # Process the payment only if it hasn't been processed before
    result = payment_gateway.charge(
        amount=payment_request.amount,
        card=payment_request.card,
        idempotency_key=transaction_id
    )
    
    # Store the result
    database.save_transaction(transaction_id, result)
    
    return result
```

### 3. Asynchronous Processing

**Concept**: Processing operations in the background rather than during the user request, reducing the impact of failures.

**Benefits**:
- Improved user experience
- Better fault isolation
- Ability to retry failed operations

**Implementation**:
- Use message queues for reliable delivery
- Implement dead letter queues for failed messages
- Design for eventual consistency

**Example**:
```javascript
// Using a message queue for asynchronous order processing
app.post('/orders', async (req, res) => {
  try {
    // Validate the order
    const order = validateOrder(req.body);
    
    // Generate an order ID
    const orderId = generateOrderId();
    
    // Store the order with 'pending' status
    await db.orders.insert({
      id: orderId,
      ...order,
      status: 'pending',
      createdAt: new Date()
    });
    
    // Send to message queue for asynchronous processing
    await messageQueue.sendMessage({
      type: 'ORDER_CREATED',
      payload: {
        orderId,
        order
      }
    });
    
    // Respond to the user immediately
    res.status(202).json({
      orderId,
      status: 'pending',
      message: 'Order received and is being processed'
    });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});
```

### 4. Health Checks and Self-Healing

**Concept**: Continuously monitoring system health and automatically recovering from failures.

**Implementation**:
- Implement health check endpoints
- Use readiness and liveness probes
- Automatically restart failed components
- Implement automated rollbacks

**Example**:
```yaml
# Kubernetes liveness and readiness probes
apiVersion: v1
kind: Pod
metadata:
  name: my-service
spec:
  containers:
  - name: my-service
    image: my-service:1.0
    ports:
    - containerPort: 8080
    livenessProbe:
      httpGet:
        path: /health
        port: 8080
      initialDelaySeconds: 30
      periodSeconds: 10
      failureThreshold: 3
    readinessProbe:
      httpGet:
        path: /ready
        port: 8080
      initialDelaySeconds: 5
      periodSeconds: 5
```

## Testing for Reliability

### 1. Chaos Engineering

**Concept**: Deliberately introducing failures to test system resilience.

**Implementation**:
- Simulate hardware failures
- Inject latency and errors
- Kill random processes
- Block network access

**Tools**:
- Netflix Chaos Monkey
- Gremlin
- Chaos Toolkit

### 2. Load Testing

**Concept**: Testing system behavior under high load to identify breaking points.

**Implementation**:
- Simulate realistic user behavior
- Gradually increase load until failure
- Measure response times and error rates
- Identify bottlenecks

**Tools**:
- Apache JMeter
- Locust
- Gatling

### 3. Fault Injection

**Concept**: Deliberately introducing faults to test recovery mechanisms.

**Implementation**:
- Simulate dependency failures
- Test timeout handling
- Verify retry mechanisms
- Validate circuit breakers

**Example**:
```java
// Using fault injection to test resilience
@Test
public void testDatabaseFailureHandling() {
    // Setup fault injection
    when(databaseService.getData())
        .thenThrow(new DatabaseConnectionException("Simulated failure"));
    
    // Call the service that should handle the failure
    ServiceResponse response = userService.getUserData();
    
    // Verify graceful degradation
    assertNotNull(response);
    assertEquals("fallback-data", response.getData());
    assertEquals(ServiceStatus.DEGRADED, response.getStatus());
}
```

## Case Study: E-commerce Checkout System

Let's apply reliability and fault tolerance principles to an e-commerce checkout system:

### Requirements
- Process customer orders reliably
- Handle payment processing
- Manage inventory updates
- Send order confirmations

### Potential Failures
1. Payment gateway timeouts or failures
2. Inventory service unavailability
3. Email service failures
4. Database connection issues
5. High traffic causing system overload

### Fault-Tolerant Design

**1. Redundancy**
- Deploy the checkout service across multiple availability zones
- Use a database with multi-region replication
- Implement multiple payment providers as fallbacks

**2. Circuit Breakers**
- Add circuit breakers for payment gateway calls
- Implement circuit breakers for inventory service calls
- Monitor failure rates and open circuits when thresholds are exceeded

**3. Asynchronous Processing**
- Use a message queue for order processing
- Process inventory updates asynchronously
- Send confirmation emails via a separate queue

**4. Idempotent Operations**
- Generate unique order IDs client-side
- Make payment processing idempotent using transaction IDs
- Design inventory updates to be safely retryable

**5. Graceful Degradation**
- Allow checkout with delayed inventory verification if the inventory service is down
- Provide estimated delivery dates if precise calculation is unavailable
- Queue email notifications if the email service is unavailable

**6. Timeouts and Retries**
- Set appropriate timeouts for all external calls
- Implement retry with backoff for transient failures
- Limit the number of retries to prevent overwhelming dependencies

### Implementation Example

```java
public class CheckoutService {
    private final PaymentService paymentService;
    private final InventoryService inventoryService;
    private final NotificationService notificationService;
    private final OrderRepository orderRepository;
    private final MessageQueue orderQueue;
    
    public OrderResult processCheckout(Cart cart, PaymentDetails paymentDetails) {
        // Generate a unique order ID
        String orderId = UUID.randomUUID().toString();
        
        try {
            // Validate order (fail fast)
            validateOrder(cart);
            
            // Create order with PENDING status
            Order order = createOrder(orderId, cart);
            orderRepository.save(order);
            
            // Process payment with circuit breaker and retry
            PaymentResult paymentResult = paymentCircuitBreaker.executeWithFallback(
                () -> processPaymentWithRetry(orderId, paymentDetails, cart.getTotalAmount()),
                this::handlePaymentFailure
            );
            
            if (!paymentResult.isSuccessful()) {
                return new OrderResult(orderId, OrderStatus.PAYMENT_FAILED, paymentResult.getMessage());
            }
            
            // Update order status
            order.setStatus(OrderStatus.PAYMENT_RECEIVED);
            orderRepository.update(order);
            
            // Queue for asynchronous processing
            orderQueue.send(new OrderMessage(orderId, OrderAction.PROCESS));
            
            return new OrderResult(orderId, OrderStatus.PROCESSING, "Order is being processed");
        } catch (Exception e) {
            // Log the error
            logger.error("Checkout failed for order " + orderId, e);
            
            // Return appropriate error to the user
            return new OrderResult(orderId, OrderStatus.ERROR, "An unexpected error occurred");
        }
    }
    
    private PaymentResult processPaymentWithRetry(String orderId, PaymentDetails details, BigDecimal amount) {
        RetryPolicy<PaymentResult> retryPolicy = RetryPolicy.<PaymentResult>builder()
            .handle(TransientPaymentException.class)
            .withBackoff(500, 5000, ChronoUnit.MILLIS)
            .withJitter(0.3)
            .withMaxRetries(3)
            .build();
            
        return Failsafe.with(retryPolicy).get(() -> 
            paymentService.processPayment(orderId, details, amount)
        );
    }
    
    private PaymentResult handlePaymentFailure(Exception e) {
        // Graceful degradation - return a failure result
        return new PaymentResult(false, "Payment service unavailable, please try again later");
    }
    
    // Asynchronous order processor
    @MessageListener(queue = "order-queue")
    public void processOrder(OrderMessage message) {
        String orderId = message.getOrderId();
        Order order = orderRepository.findById(orderId);
        
        try {
            // Check inventory with circuit breaker
            boolean inventoryReserved = inventoryCircuitBreaker.executeWithFallback(
                () -> inventoryService.reserveInventory(order.getItems()),
                (e) -> handleInventoryFailure(order)
            );
            
            if (inventoryReserved) {
                order.setStatus(OrderStatus.CONFIRMED);
            } else {
                order.setStatus(OrderStatus.INVENTORY_FAILED);
            }
            
            orderRepository.update(order);
            
            // Send notification (non-critical, can fail)
            try {
                notificationService.sendOrderConfirmation(order);
            } catch (Exception e) {
                // Log but don't fail the order processing
                logger.warn("Failed to send order confirmation for " + orderId, e);
            }
        } catch (Exception e) {
            // Handle unexpected errors
            logger.error("Failed to process order " + orderId, e);
            
            // Retry later by sending back to the queue with a delay
            orderQueue.sendWithDelay(message, Duration.ofMinutes(5));
        }
    }
    
    private boolean handleInventoryFailure(Order order) {
        // Graceful degradation - mark for manual inventory check
        order.setStatus(OrderStatus.PENDING_INVENTORY_CHECK);
        orderRepository.update(order);
        
        // Notify inventory team
        alertService.sendAlert("Inventory service down, manual check required for order " + order.getId());
        
        return false;
    }
}
```

## Interview Questions

### Question 1: How would you design a system to be resilient to database failures?

**Key Points to Address**:

1. **Redundancy**:
   - Use database replication (master-slave or multi-master)
   - Deploy across multiple availability zones or regions
   - Implement read replicas for read operations

2. **Caching**:
   - Add a caching layer (Redis, Memcached) to reduce database load
   - Implement cache-aside pattern for frequently accessed data
   - Consider write-through or write-behind caching for writes

3. **Circuit Breakers**:
   - Implement circuit breakers for database calls
   - Provide fallbacks when the database is unavailable
   - Monitor error rates and response times

4. **Queuing**:
   - Use message queues for write operations
   - Process database updates asynchronously
   - Implement dead letter queues for failed operations

5. **Data Partitioning**:
   - Shard data across multiple database instances
   - Distribute load and risk across multiple servers
   - Isolate failures to specific shards

6. **Retry Mechanisms**:
   - Implement retries with exponential backoff
   - Make operations idempotent to allow safe retries
   - Set appropriate timeout values

7. **Graceful Degradation**:
   - Return cached data when the database is unavailable
   - Disable non-critical features during database outages
   - Provide clear user feedback during degraded operation

### Question 2: Explain the difference between fail-fast and fail-silent approaches. When would you use each?

**Fail-Fast**:
- Immediately reports failures when detected
- Raises exceptions or errors to callers
- Makes problems visible quickly
- Prevents cascading failures by stopping early

**When to use Fail-Fast**:
- During development and testing to catch issues early
- For critical operations where correctness is essential
- When the caller can take meaningful action on failure
- In synchronous operations where immediate feedback is needed

**Fail-Silent**:
- Attempts to continue operation despite failures
- Hides errors from users when possible
- Focuses on maintaining service availability
- May degrade functionality rather than failing completely

**When to use Fail-Silent**:
- For non-critical features in production systems
- When partial functionality is better than none
- In asynchronous operations that can be retried
- For user-facing components where graceful degradation improves experience

### Question 3: How would you implement a circuit breaker pattern in a microservices architecture?

**Key Components**:

1. **Circuit State Management**:
   - Track success/failure counts
   - Maintain circuit state (closed, open, half-open)
   - Implement timeout for resetting to half-open

2. **Failure Detection**:
   - Define what constitutes a failure (exceptions, timeouts, HTTP status codes)
   - Set thresholds for tripping the circuit (e.g., 50% failure rate)
   - Consider sliding window for failure rate calculation

3. **Circuit Behavior**:
   - Closed: Allow requests to pass through normally
   - Open: Fail fast without calling the service
   - Half-Open: Allow limited requests to test recovery

4. **Fallback Mechanisms**:
   - Provide alternative responses when the circuit is open
   - Return cached data, default values, or gracefully degrade
   - Queue requests for later processing if appropriate

5. **Monitoring and Metrics**:
   - Track circuit state changes
   - Monitor success/failure rates
   - Alert on circuit trips

**Implementation Example**:
```java
// Using Resilience4j in a Spring Boot application
@Service
public class ProductService {
    private final RestTemplate restTemplate;
    private final CircuitBreaker circuitBreaker;
    
    public ProductService(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
        
        // Configure circuit breaker
        CircuitBreakerConfig config = CircuitBreakerConfig.custom()
            .failureRateThreshold(50)
            .slidingWindowSize(10)
            .minimumNumberOfCalls(5)
            .waitDurationInOpenState(Duration.ofSeconds(30))
            .permittedNumberOfCallsInHalfOpenState(3)
            .build();
            
        CircuitBreakerRegistry registry = CircuitBreakerRegistry.of(config);
        this.circuitBreaker = registry.circuitBreaker("productService");
    }
    
    public Product getProduct(String productId) {
        // Use the circuit breaker to call the product API
        return Try.ofSupplier(
            CircuitBreaker.decorateSupplier(
                circuitBreaker,
                () -> restTemplate.getForObject(
                    "/products/" + productId,
                    Product.class
                )
            )
        ).recover(throwable -> {
            // Fallback when circuit is open or call fails
            return getCachedProduct(productId);
        }).get();
    }
    
    private Product getCachedProduct(String productId) {
        // Return cached product or a default one
        // ...
    }
}
```

## Practical Exercise

### Exercise: Implement a Fault-Tolerant API Client

Design and implement a fault-tolerant HTTP client for an external API that:

1. Handles transient failures with retries
2. Implements circuit breaking for persistent failures
3. Provides fallbacks when the API is unavailable
4. Includes appropriate timeout handling
5. Logs and monitors failure rates

**Requirements**:
- The client should be configurable (retry attempts, timeouts, etc.)
- It should handle different types of failures appropriately
- The implementation should be testable

**Hint**: Consider using a library like Resilience4j, Hystrix, or Polly depending on your language.

## AI/ML Integration

### Reliability Challenges in ML Systems

1. **Model Serving Reliability**:
   - Models may crash due to unexpected inputs
   - Inference latency can vary significantly
   - Resource consumption (CPU, memory) can be unpredictable

2. **Data Quality Issues**:
   - Input data drift can cause model performance degradation
   - Missing features or corrupted data can break models
   - Outliers may cause unexpected behavior

3. **Model Versioning Challenges**:
   - Rolling back to previous model versions during issues
   - Ensuring consistent model behavior across deployments
   - Managing model dependencies

### Fault Tolerance Strategies for ML Systems

1. **Model Redundancy**:
   - Deploy multiple model versions in parallel
   - Use ensemble methods to combine predictions
   - Implement model fallbacks for different failure scenarios

2. **Input Validation and Preprocessing**:
   - Validate inputs before sending to the model
   - Handle missing features with imputation
   - Normalize or transform outliers

3. **Monitoring and Circuit Breaking**:
   - Monitor prediction quality metrics
   - Implement circuit breakers based on model performance
   - Set up alerts for data drift or model degradation

4. **Graceful Degradation**:
   - Fall back to simpler models when complex ones fail
   - Use rule-based systems as backups
   - Return reasonable defaults with confidence indicators

**Example: Fault-Tolerant Model Serving**:
```python
class FaultTolerantModelServer:
    def __init__(self):
        # Load primary and fallback models
        self.primary_model = load_complex_model()
        self.fallback_model = load_simple_model()
        self.rule_based_fallback = RuleBasedPredictor()
        
        # Initialize circuit breaker
        self.circuit_breaker = CircuitBreaker(
            failure_threshold=5,
            recovery_timeout=300,  # 5 minutes
            expected_exception=ModelPredictionError
        )
        
    def predict(self, features):
        try:
            # Validate input features
            validated_features = self.validate_and_preprocess(features)
            
            # Try primary model with circuit breaker
            if self.circuit_breaker.is_closed():
                try:
                    prediction = self.primary_model.predict(validated_features)
                    self.circuit_breaker.record_success()
                    return {
                        'prediction': prediction,
                        'model': 'primary',
                        'confidence': self.calculate_confidence(prediction)
                    }
                except Exception as e:
                    self.circuit_breaker.record_failure()
                    raise ModelPredictionError(f"Primary model failed: {str(e)}")
            
            # Circuit is open, try fallback model
            try:
                prediction = self.fallback_model.predict(validated_features)
                return {
                    'prediction': prediction,
                    'model': 'fallback',
                    'confidence': self.calculate_confidence(prediction) * 0.8  # Lower confidence
                }
            except Exception:
                # Last resort: rule-based fallback
                prediction = self.rule_based_fallback.predict(validated_features)
                return {
                    'prediction': prediction,
                    'model': 'rule_based',
                    'confidence': 0.5  # Low confidence
                }
                
        except Exception as e:
            # Log the error
            logger.error(f"All prediction methods failed: {str(e)}")
            
            # Return safe default with very low confidence
            return {
                'prediction': self.get_safe_default(),
                'model': 'default',
                'confidence': 0.1
            }
    
    def validate_and_preprocess(self, features):
        # Validate feature names and types
        # Handle missing values
        # Transform outliers
        # ...
        return processed_features
```

## Next Steps

In the next section, we'll explore Performance Optimization, including:
- Performance metrics and measurement
- Profiling and bottleneck identification
- Algorithmic optimization
- Database query optimization
- Network optimization

## Resources

1. [Designing Data-Intensive Applications](https://dataintensive.net/) by Martin Kleppmann
2. [Release It!](https://pragprog.com/titles/mnee2/release-it-second-edition/) by Michael Nygard
3. [Chaos Engineering](https://www.oreilly.com/library/view/chaos-engineering/9781492043867/) by Casey Rosenthal and Nora Jones
4. [Resilience4j Documentation](https://resilience4j.readme.io/docs)
5. [AWS Well-Architected Framework - Reliability Pillar](https://docs.aws.amazon.com/wellarchitected/latest/reliability-pillar/welcome.html)
