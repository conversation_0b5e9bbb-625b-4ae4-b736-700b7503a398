"""
Load Testing Script for Scalable Counter Service

This script performs load testing on the counter service to demonstrate its scalability.
"""
import requests
import threading
import time
import random
import argparse
from concurrent.futures import ThreadPoolExecutor
import matplotlib.pyplot as plt
import numpy as np

def increment_counter(base_url, counter_name):
    """Increment a counter."""
    try:
        response = requests.post(f"{base_url}/counters/{counter_name}")
        return response.status_code == 200
    except Exception as e:
        print(f"Error incrementing counter: {e}")
        return False

def get_counter(base_url, counter_name):
    """Get a counter value."""
    try:
        response = requests.get(f"{base_url}/counters/{counter_name}")
        return response.status_code == 200
    except Exception as e:
        print(f"Error getting counter: {e}")
        return False

def batch_operation(base_url, counter_names):
    """Perform a batch operation."""
    try:
        data = {
            "increment": random.sample(counter_names, random.randint(1, len(counter_names))),
            "get": random.sample(counter_names, random.randint(1, len(counter_names)))
        }
        response = requests.post(f"{base_url}/counters/batch", json=data)
        return response.status_code == 200
    except Exception as e:
        print(f"Error in batch operation: {e}")
        return False

def worker(base_url, counter_names, num_operations):
    """Worker function that performs random operations."""
    operations = ["increment", "get", "batch"]
    results = {"success": 0, "failure": 0}
    
    for _ in range(num_operations):
        op = random.choice(operations)
        success = False
        
        if op == "increment":
            counter = random.choice(counter_names)
            success = increment_counter(base_url, counter)
        elif op == "get":
            counter = random.choice(counter_names)
            success = get_counter(base_url, counter)
        elif op == "batch":
            success = batch_operation(base_url, counter_names)
        
        if success:
            results["success"] += 1
        else:
            results["failure"] += 1
    
    return results

def run_load_test(base_url, num_threads, operations_per_thread, counter_names=None):
    """
    Run a load test with multiple threads.
    
    Args:
        base_url: Base URL of the counter service API
        num_threads: Number of concurrent threads
        operations_per_thread: Number of operations per thread
        counter_names: List of counter names to use (default: generates random names)
    
    Returns:
        Dictionary with test results
    """
    if counter_names is None:
        counter_names = [f"test_counter_{i}" for i in range(10)]
    
    # Reset counters
    for counter in counter_names:
        requests.delete(f"{base_url}/counters/{counter}")
    
    start_time = time.time()
    results = {"success": 0, "failure": 0}
    
    with ThreadPoolExecutor(max_workers=num_threads) as executor:
        futures = []
        for _ in range(num_threads):
            future = executor.submit(worker, base_url, counter_names, operations_per_thread)
            futures.append(future)
        
        for future in futures:
            worker_results = future.result()
            results["success"] += worker_results["success"]
            results["failure"] += worker_results["failure"]
    
    end_time = time.time()
    elapsed_time = end_time - start_time
    
    # Get final counter values
    counter_values = {}
    for counter in counter_names:
        response = requests.get(f"{base_url}/counters/{counter}")
        if response.status_code == 200:
            counter_values[counter] = response.json().get("value", 0)
    
    return {
        "elapsed_time": elapsed_time,
        "operations": results["success"] + results["failure"],
        "success_rate": results["success"] / (results["success"] + results["failure"]) * 100,
        "operations_per_second": (results["success"] + results["failure"]) / elapsed_time,
        "counter_values": counter_values
    }

def run_scalability_test(base_url, max_threads, operations_per_thread=100):
    """
    Run a scalability test with increasing thread counts.
    
    Args:
        base_url: Base URL of the counter service API
        max_threads: Maximum number of threads to test
        operations_per_thread: Number of operations per thread
    
    Returns:
        Dictionary with test results for each thread count
    """
    thread_counts = [1, 2, 5, 10, 20, 50, 100, 200, 500]
    thread_counts = [t for t in thread_counts if t <= max_threads]
    
    counter_names = [f"scalability_test_{i}" for i in range(10)]
    results = {}
    
    for thread_count in thread_counts:
        print(f"Testing with {thread_count} threads...")
        test_result = run_load_test(base_url, thread_count, operations_per_thread, counter_names)
        results[thread_count] = test_result
        print(f"  Operations per second: {test_result['operations_per_second']:.2f}")
        print(f"  Success rate: {test_result['success_rate']:.2f}%")
        print()
    
    return results

def plot_results(results):
    """
    Plot the results of a scalability test.
    
    Args:
        results: Dictionary with test results for each thread count
    """
    thread_counts = sorted(results.keys())
    ops_per_second = [results[t]["operations_per_second"] for t in thread_counts]
    
    plt.figure(figsize=(10, 6))
    
    # Plot operations per second
    plt.subplot(2, 1, 1)
    plt.plot(thread_counts, ops_per_second, 'o-', linewidth=2)
    plt.xlabel('Number of Threads')
    plt.ylabel('Operations per Second')
    plt.title('Scalability Test Results')
    plt.grid(True)
    
    # Plot efficiency (ops per second per thread)
    plt.subplot(2, 1, 2)
    efficiency = [ops / threads for ops, threads in zip(ops_per_second, thread_counts)]
    plt.plot(thread_counts, efficiency, 'o-', linewidth=2)
    plt.xlabel('Number of Threads')
    plt.ylabel('Efficiency (Ops/s per Thread)')
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig('scalability_results.png')
    plt.close()
    
    print(f"Results plot saved to scalability_results.png")

def main():
    parser = argparse.ArgumentParser(description='Load test for Scalable Counter Service')
    parser.add_argument('--url', default='http://localhost:5000', help='Base URL of the counter service API')
    parser.add_argument('--threads', type=int, default=10, help='Number of concurrent threads')
    parser.add_argument('--operations', type=int, default=100, help='Operations per thread')
    parser.add_argument('--scalability-test', action='store_true', help='Run scalability test with increasing thread counts')
    parser.add_argument('--max-threads', type=int, default=100, help='Maximum number of threads for scalability test')
    
    args = parser.parse_args()
    
    if args.scalability_test:
        print(f"Running scalability test with up to {args.max_threads} threads...")
        results = run_scalability_test(args.url, args.max_threads, args.operations)
        plot_results(results)
    else:
        print(f"Running load test with {args.threads} threads, {args.operations} operations per thread...")
        results = run_load_test(args.url, args.threads, args.operations)
        
        print("\nLoad Test Results:")
        print(f"Elapsed time: {results['elapsed_time']:.2f} seconds")
        print(f"Total operations: {results['operations']}")
        print(f"Operations per second: {results['operations_per_second']:.2f}")
        print(f"Success rate: {results['success_rate']:.2f}%")
        
        print("\nFinal counter values:")
        for counter, value in results['counter_values'].items():
            print(f"  {counter}: {value}")

if __name__ == "__main__":
    main()
