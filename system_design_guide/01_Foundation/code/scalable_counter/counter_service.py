"""
Scalable Counter Service

This module implements a scalable counter service that can:
1. Increment counters
2. Retrieve counter values
3. Handle high throughput (thousands of increments per second)
4. Maintain accuracy under concurrent load

The implementation uses Redis for atomic operations and persistence.
"""
import redis
import time
import threading
import random
import os
from typing import Dict, List, Optional, Union, Tuple

class CounterService:
    """A scalable counter service using Redis."""

    def __init__(self, host: str = None, port: int = None, db: int = 0):
        """
        Initialize the counter service with Redis connection.

        Args:
            host: Redis host (defaults to REDIS_HOST env var or 'localhost')
            port: Redis port (defaults to REDIS_PORT env var or 6379)
            db: Redis database number
        """
        # Get connection details from environment variables or use defaults
        redis_host = host or os.environ.get('REDIS_HOST', 'localhost')
        redis_port = port or int(os.environ.get('REDIS_PORT', 6379))

        self.redis_client = redis.Redis(host=redis_host, port=redis_port, db=db)
        self.counter_prefix = "counter:"
        self.lock_prefix = "lock:"

    def increment(self, counter_name: str, amount: int = 1) -> int:
        """
        Increment a counter by the specified amount.

        Args:
            counter_name: Name of the counter
            amount: Amount to increment (default: 1)

        Returns:
            The new counter value
        """
        key = f"{self.counter_prefix}{counter_name}"
        return self.redis_client.incrby(key, amount)

    def get(self, counter_name: str) -> int:
        """
        Get the current value of a counter.

        Args:
            counter_name: Name of the counter

        Returns:
            The current counter value, or 0 if the counter doesn't exist
        """
        key = f"{self.counter_prefix}{counter_name}"
        value = self.redis_client.get(key)
        return int(value) if value else 0

    def reset(self, counter_name: str) -> bool:
        """
        Reset a counter to zero.

        Args:
            counter_name: Name of the counter

        Returns:
            True if the counter was reset, False otherwise
        """
        key = f"{self.counter_prefix}{counter_name}"
        return bool(self.redis_client.set(key, 0))

    def increment_with_lock(self, counter_name: str, amount: int = 1,
                           timeout: int = 10) -> Optional[int]:
        """
        Increment a counter with distributed locking for additional safety.

        Args:
            counter_name: Name of the counter
            amount: Amount to increment (default: 1)
            timeout: Lock timeout in seconds (default: 10)

        Returns:
            The new counter value, or None if the lock couldn't be acquired
        """
        counter_key = f"{self.counter_prefix}{counter_name}"
        lock_key = f"{self.lock_prefix}{counter_name}"

        # Try to acquire the lock
        lock_value = str(time.time())
        acquired = self.redis_client.setnx(lock_key, lock_value)

        if acquired:
            try:
                # Set lock expiration to prevent deadlocks
                self.redis_client.expire(lock_key, timeout)

                # Get current value
                current = self.redis_client.get(counter_key)
                current = int(current) if current else 0

                # Increment
                new_value = current + amount
                self.redis_client.set(counter_key, new_value)

                return new_value
            finally:
                # Release the lock if we still own it
                if self.redis_client.get(lock_key) == lock_value.encode():
                    self.redis_client.delete(lock_key)

        return None

    def increment_batch(self, counter_names: List[str], amount: int = 1) -> Dict[str, int]:
        """
        Increment multiple counters in a single batch operation.

        Args:
            counter_names: List of counter names
            amount: Amount to increment each counter (default: 1)

        Returns:
            Dictionary mapping counter names to their new values
        """
        pipeline = self.redis_client.pipeline()

        # Queue all increments
        for name in counter_names:
            key = f"{self.counter_prefix}{name}"
            pipeline.incrby(key, amount)

        # Execute all commands in a single round-trip
        results = pipeline.execute()

        # Map results back to counter names
        return dict(zip(counter_names, results))

    def get_batch(self, counter_names: List[str]) -> Dict[str, int]:
        """
        Get multiple counter values in a single batch operation.

        Args:
            counter_names: List of counter names

        Returns:
            Dictionary mapping counter names to their values
        """
        pipeline = self.redis_client.pipeline()

        # Queue all get operations
        for name in counter_names:
            key = f"{self.counter_prefix}{name}"
            pipeline.get(key)

        # Execute all commands in a single round-trip
        results = pipeline.execute()

        # Convert results to integers and handle None values
        processed_results = [int(r) if r else 0 for r in results]

        # Map results back to counter names
        return dict(zip(counter_names, processed_results))

    def get_all_counters(self) -> Dict[str, int]:
        """
        Get all counters and their values.

        Returns:
            Dictionary mapping counter names to their values
        """
        # Get all counter keys
        pattern = f"{self.counter_prefix}*"
        keys = self.redis_client.keys(pattern)

        if not keys:
            return {}

        # Get all values in a single batch
        values = self.redis_client.mget(keys)

        # Process results
        result = {}
        for key, value in zip(keys, values):
            # Extract counter name from key
            counter_name = key.decode('utf-8').replace(self.counter_prefix, '')
            result[counter_name] = int(value) if value else 0

        return result

    def increment_with_expiry(self, counter_name: str, amount: int = 1,
                             ttl_seconds: int = 3600) -> int:
        """
        Increment a counter and set an expiration time.

        Args:
            counter_name: Name of the counter
            amount: Amount to increment (default: 1)
            ttl_seconds: Time-to-live in seconds (default: 1 hour)

        Returns:
            The new counter value
        """
        key = f"{self.counter_prefix}{counter_name}"

        # Use a pipeline for atomicity
        pipeline = self.redis_client.pipeline()
        pipeline.incrby(key, amount)
        pipeline.expire(key, ttl_seconds)
        results = pipeline.execute()

        return results[0]  # Return the new counter value

    def get_and_reset(self, counter_name: str) -> int:
        """
        Get the current value of a counter and reset it to zero atomically.

        Args:
            counter_name: Name of the counter

        Returns:
            The counter value before reset
        """
        key = f"{self.counter_prefix}{counter_name}"

        # Use Lua script for atomicity
        script = """
        local current = redis.call('get', KEYS[1])
        redis.call('set', KEYS[1], 0)
        return current
        """

        result = self.redis_client.eval(script, 1, key)
        return int(result) if result else 0


# Example usage and load testing
def load_test(service, num_threads=10, operations_per_thread=1000):
    """
    Run a load test on the counter service.

    Args:
        service: CounterService instance
        num_threads: Number of concurrent threads
        operations_per_thread: Number of operations per thread
    """
    counters = ['users', 'visits', 'clicks', 'purchases', 'errors']

    def worker():
        """Worker function for each thread."""
        for _ in range(operations_per_thread):
            # Randomly choose an operation
            op = random.choice(['increment', 'get', 'batch'])

            if op == 'increment':
                # Increment a random counter
                counter = random.choice(counters)
                service.increment(counter)
            elif op == 'get':
                # Get a random counter
                counter = random.choice(counters)
                service.get(counter)
            elif op == 'batch':
                # Batch increment multiple counters
                batch_size = random.randint(1, len(counters))
                batch_counters = random.sample(counters, batch_size)
                service.increment_batch(batch_counters)

    # Create and start threads
    threads = []
    start_time = time.time()

    for _ in range(num_threads):
        thread = threading.Thread(target=worker)
        threads.append(thread)
        thread.start()

    # Wait for all threads to complete
    for thread in threads:
        thread.join()

    end_time = time.time()

    # Calculate and print results
    total_operations = num_threads * operations_per_thread
    elapsed_time = end_time - start_time
    ops_per_second = total_operations / elapsed_time

    print(f"Load test completed:")
    print(f"- Threads: {num_threads}")
    print(f"- Operations per thread: {operations_per_thread}")
    print(f"- Total operations: {total_operations}")
    print(f"- Elapsed time: {elapsed_time:.2f} seconds")
    print(f"- Operations per second: {ops_per_second:.2f}")

    # Print final counter values
    print("\nFinal counter values:")
    for counter, value in service.get_all_counters().items():
        print(f"- {counter}: {value}")


if __name__ == "__main__":
    # Create counter service
    counter_service = CounterService()

    # Reset counters for clean test
    for counter in ['users', 'visits', 'clicks', 'purchases', 'errors']:
        counter_service.reset(counter)

    # Run load test
    load_test(counter_service, num_threads=10, operations_per_thread=1000)
