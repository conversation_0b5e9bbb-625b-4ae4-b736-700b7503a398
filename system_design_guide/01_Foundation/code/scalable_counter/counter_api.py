"""
Scalable Counter Service REST API

This module implements a REST API for the scalable counter service using Flask.
"""
from flask import Flask, request, jsonify
from counter_service import CounterService
import time
import os

app = Flask(__name__)
# Use environment variables for Redis connection
counter_service = CounterService()

@app.route('/counters/<counter_name>', methods=['POST'])
def increment_counter(counter_name):
    """
    Increment a counter.

    Request body (optional):
    {
        "amount": 5,  # Amount to increment (default: 1)
        "ttl": 3600   # Time-to-live in seconds (optional)
    }
    """
    data = request.json or {}
    amount = data.get('amount', 1)
    ttl = data.get('ttl')

    try:
        if ttl:
            new_value = counter_service.increment_with_expiry(counter_name, amount, ttl)
        else:
            new_value = counter_service.increment(counter_name, amount)

        return jsonify({
            'counter': counter_name,
            'value': new_value,
            'operation': 'increment',
            'amount': amount
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/counters/<counter_name>', methods=['GET'])
def get_counter(counter_name):
    """Get the current value of a counter."""
    try:
        value = counter_service.get(counter_name)
        return jsonify({
            'counter': counter_name,
            'value': value
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/counters/<counter_name>', methods=['DELETE'])
def reset_counter(counter_name):
    """Reset a counter to zero."""
    try:
        counter_service.reset(counter_name)
        return jsonify({
            'counter': counter_name,
            'value': 0,
            'operation': 'reset'
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/counters/<counter_name>/reset-and-get', methods=['POST'])
def get_and_reset(counter_name):
    """Get the current value of a counter and reset it to zero."""
    try:
        value = counter_service.get_and_reset(counter_name)
        return jsonify({
            'counter': counter_name,
            'value': value,
            'operation': 'get_and_reset'
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/counters', methods=['GET'])
def get_all_counters():
    """Get all counters and their values."""
    try:
        counters = counter_service.get_all_counters()
        return jsonify({
            'counters': counters,
            'count': len(counters)
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/counters/batch', methods=['POST'])
def batch_operations():
    """
    Perform batch operations on counters.

    Request body:
    {
        "increment": ["counter1", "counter2"],  # Counters to increment
        "get": ["counter3", "counter4"]         # Counters to get
    }
    """
    data = request.json or {}

    if not data:
        return jsonify({'error': 'Request body is required'}), 400

    result = {}

    # Process increment batch
    if 'increment' in data:
        increment_counters = data['increment']
        if increment_counters:
            try:
                incremented = counter_service.increment_batch(increment_counters)
                result['incremented'] = incremented
            except Exception as e:
                result['increment_error'] = str(e)

    # Process get batch
    if 'get' in data:
        get_counters = data['get']
        if get_counters:
            try:
                values = counter_service.get_batch(get_counters)
                result['values'] = values
            except Exception as e:
                result['get_error'] = str(e)

    return jsonify(result)

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint."""
    try:
        # Try a simple Redis operation to check connectivity
        counter_service.redis_client.ping()
        return jsonify({
            'status': 'healthy',
            'timestamp': time.time()
        })
    except Exception as e:
        return jsonify({
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': time.time()
        }), 500

@app.route('/load-test', methods=['POST'])
def run_load_test():
    """
    Run a load test on the counter service.

    Request body:
    {
        "threads": 10,              # Number of threads
        "operations_per_thread": 1000  # Operations per thread
    }
    """
    from counter_service import load_test
    import threading

    data = request.json or {}
    threads = data.get('threads', 5)
    operations = data.get('operations_per_thread', 100)

    # Run load test in a separate thread to not block the response
    def run_test():
        load_test(counter_service, threads, operations)

    thread = threading.Thread(target=run_test)
    thread.start()

    return jsonify({
        'message': 'Load test started',
        'threads': threads,
        'operations_per_thread': operations,
        'total_operations': threads * operations
    })

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)
