# Scalable Counter Service

This is a practical implementation of a scalable counter service that demonstrates the scalability concepts covered in Module 1.

## Features

- Increment counters with atomic operations
- Retrieve counter values
- Batch operations for efficiency
- Distributed locking for additional safety
- Time-based expiration for counters
- Atomic get-and-reset operations
- REST API for easy integration
- Load testing capabilities

## Components

### 1. CounterService

Core implementation of the counter service using Redis for:
- Atomic operations
- Persistence
- High throughput
- Distributed access

### 2. REST API

Flask-based REST API that exposes the counter service functionality:
- Increment counters
- Get counter values
- Reset counters
- Batch operations
- Health checks
- Load testing

## Scalability Features

### 1. Horizontal Scaling

- Stateless API design allows multiple instances
- Redis handles the shared state
- No instance-specific data

### 2. Efficient Operations

- Batch processing to reduce network overhead
- Pipelining for atomic multi-step operations
- Lua scripts for complex atomic operations

### 3. Performance Optimization

- Minimized round-trips to Redis
- Efficient data structures
- Optimized key naming strategy

### 4. Resilience

- Timeout mechanisms to prevent deadlocks
- Error handling and recovery
- Health check endpoint

## Requirements

- Python 3.6+
- Redis server
- Flask
- redis-py

## Installation

```bash
pip install redis flask
```

## Running the Service

1. Start Redis server:
   ```bash
   redis-server
   ```

2. Run the counter service API:
   ```bash
   python counter_api.py
   ```

## API Endpoints

### Increment a Counter
```
POST /counters/<counter_name>

Request body (optional):
{
    "amount": 5,  # Amount to increment (default: 1)
    "ttl": 3600   # Time-to-live in seconds (optional)
}
```

### Get a Counter Value
```
GET /counters/<counter_name>
```

### Reset a Counter
```
DELETE /counters/<counter_name>
```

### Get and Reset a Counter
```
POST /counters/<counter_name>/reset-and-get
```

### Get All Counters
```
GET /counters
```

### Batch Operations
```
POST /counters/batch

Request body:
{
    "increment": ["counter1", "counter2"],  # Counters to increment
    "get": ["counter3", "counter4"]         # Counters to get
}
```

### Health Check
```
GET /health
```

### Run Load Test
```
POST /load-test

Request body:
{
    "threads": 10,              # Number of threads
    "operations_per_thread": 1000  # Operations per thread
}
```

## Load Testing

The service includes a built-in load testing function that:
1. Creates multiple threads to simulate concurrent users
2. Performs random operations (increment, get, batch)
3. Measures throughput (operations per second)
4. Reports final counter values

## Scaling Considerations

### Redis Scaling

For production use, consider:
- Redis Cluster for horizontal scaling
- Redis Sentinel for high availability
- Redis Enterprise for managed scaling

### API Scaling

For production use, consider:
- Multiple API instances behind a load balancer
- Containerization with Docker
- Orchestration with Kubernetes
- Auto-scaling based on load

## Exercise

Try extending this implementation with the following features:
1. Authentication and authorization
2. Rate limiting to prevent abuse
3. Monitoring and alerting
4. Sharded counters for extreme scale
5. Multi-datacenter replication

## Next Steps

After understanding this implementation, consider how you would modify it to handle:
1. Billions of counters
2. Millions of operations per second
3. Global distribution with low latency
4. Five-nines availability requirements
