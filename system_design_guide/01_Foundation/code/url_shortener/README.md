# URL Shortener Implementation

This is a practical implementation of a URL shortener service that demonstrates the basic system design principles covered in Module 1.

## Design Principles Demonstrated

1. **Separation of Concerns**
   - Data Access Layer (URLRepository)
   - Service Layer (URLService)
   - API Layer (URLShortenerAPI)
   - Web Application Layer (Flask app)

2. **Single Responsibility Principle**
   - Each class has a specific role and responsibility
   - URLRepository handles database operations
   - URLService contains business logic
   - URLShortenerAPI handles API requests

3. **KISS (Keep It Simple, Stupid)**
   - Simple, straightforward implementation
   - No unnecessary complexity
   - Clear, readable code

4. **DRY (Don't Repeat Yourself)**
   - Reusable components
   - Centralized logic for URL validation, short code generation, etc.

5. **Loose Coupling**
   - Components interact through well-defined interfaces
   - Dependencies are injected rather than hardcoded

## Components

### 1. URLRepository

Handles all database operations:
- Creating tables
- Saving URL mappings
- Retrieving original URLs
- Recording click statistics
- Retrieving URL statistics

### 2. URLService

Contains business logic:
- Generating short codes
- Creating short URLs
- Retrieving original URLs
- Managing click tracking

### 3. URLShortenerAPI

Handles API requests:
- URL validation
- Error handling
- Response formatting

### 4. Flask Web Application

Provides a web interface:
- HTML form for URL shortening
- API endpoints for programmatic access
- Redirection from short URLs to original URLs
- Statistics display

## How to Run

1. Install the required dependencies:
   ```
   pip install flask
   ```

2. Run the Flask application:
   ```
   python app.py
   ```

3. Open your browser and navigate to `http://localhost:5000/`

## API Endpoints

- `POST /api/shorten` - Shorten a URL
  - Request body: `{"url": "https://example.com"}`
  - Response: `{"short_url": "http://localhost:5000/abc123"}`

- `GET /<short_code>` - Redirect to the original URL

- `GET /api/stats/<short_code>` - Get statistics for a short URL
  - Response: `{"original_url": "https://example.com", "created_at": "...", "clicks": 5}`

## Database Schema

### url_mappings Table
- `short_code` (TEXT, PRIMARY KEY) - The unique short code
- `original_url` (TEXT) - The original URL
- `created_at` (TIMESTAMP) - When the mapping was created

### click_stats Table
- `id` (INTEGER, PRIMARY KEY) - Auto-incrementing ID
- `short_code` (TEXT, FOREIGN KEY) - The short code that was clicked
- `clicked_at` (TIMESTAMP) - When the click occurred

## Scaling Considerations

This implementation is designed for educational purposes and has several limitations for production use:

1. **Database**: Uses SQLite, which is not suitable for high-concurrency environments. In production, consider:
   - PostgreSQL or MySQL for relational needs
   - Redis for caching frequently accessed URLs
   - NoSQL solutions like MongoDB for horizontal scaling

2. **Short Code Generation**: Uses random generation, which could lead to collisions. In production, consider:
   - Base62 encoding of incremental IDs
   - Distributed ID generation (e.g., Snowflake algorithm)

3. **Caching**: No caching implementation. In production, consider:
   - In-memory cache for hot URLs
   - Distributed cache like Redis or Memcached

4. **Load Balancing**: Single instance only. In production, consider:
   - Multiple application instances
   - Load balancer (e.g., Nginx, HAProxy)
   - Stateless design for horizontal scaling

## Exercise

Try extending this implementation with the following features:
1. Custom short codes (allow users to specify their own short code)
2. Expiration dates for URLs
3. User authentication and URL management
4. Analytics dashboard with more detailed statistics
5. Rate limiting to prevent abuse

## Next Steps

After understanding this implementation, consider how you would modify it to handle:
1. Millions of URLs
2. Thousands of requests per second
3. Global distribution with low latency
4. High availability requirements
