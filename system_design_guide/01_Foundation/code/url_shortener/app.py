"""
Flask web application for the URL Shortener.

This demonstrates how to use the URL Shortener components in a web application.
"""
from flask import Flask, request, jsonify, redirect, render_template_string
from url_shortener import URLRepository, URLService, URLShortenerAPI

app = Flask(__name__)

# Set up the components
repository = URLRepository()
url_service = URLService(repository, base_url="http://localhost:5000/")
api = URLShortenerAPI(url_service)

# HTML template for the home page
HOME_TEMPLATE = """
<!DOCTYPE html>
<html>
<head>
    <title>URL Shortener</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            background-color: #f9f9f9;
            border-radius: 5px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #333;
        }
        input, button {
            padding: 10px;
            margin: 5px 0;
        }
        input[type="text"] {
            width: 100%;
            box-sizing: border-box;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
            border-radius: 3px;
        }
        button:hover {
            background-color: #45a049;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            background-color: #e9f7ef;
            border-radius: 3px;
        }
        .error {
            color: #e74c3c;
        }
        .stats {
            margin-top: 30px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>URL Shortener</h1>
        
        <form id="shortenForm">
            <div>
                <label for="url">URL to shorten:</label>
                <input type="text" id="url" name="url" placeholder="https://example.com" required>
            </div>
            
            <button type="submit">Shorten URL</button>
        </form>
        
        <div id="result" class="result" style="display: none;"></div>
        
        {% if short_code %}
        <div class="stats">
            <h2>URL Statistics</h2>
            <table>
                <tr>
                    <th>Original URL</th>
                    <td>{{ stats.original_url }}</td>
                </tr>
                <tr>
                    <th>Created At</th>
                    <td>{{ stats.created_at }}</td>
                </tr>
                <tr>
                    <th>Clicks</th>
                    <td>{{ stats.clicks }}</td>
                </tr>
            </table>
        </div>
        {% endif %}
    </div>
    
    <script>
        document.getElementById('shortenForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const url = document.getElementById('url').value;
            
            fetch('/api/shorten', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    url: url
                }),
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('result');
                resultDiv.style.display = 'block';
                
                if (data.error) {
                    resultDiv.innerHTML = `<p class="error">Error: ${data.error}</p>`;
                } else {
                    resultDiv.innerHTML = `
                        <p>Shortened URL: <a href="${data.short_url}" target="_blank">${data.short_url}</a></p>
                    `;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                const resultDiv = document.getElementById('result');
                resultDiv.style.display = 'block';
                resultDiv.innerHTML = `<p class="error">Error: ${error.message}</p>`;
            });
        });
    </script>
</body>
</html>
"""

@app.route('/')
def home():
    """Render the home page."""
    short_code = request.args.get('code')
    stats = None
    
    if short_code:
        stats = api.get_url_stats(short_code)
        if 'error' in stats:
            stats = None
    
    return render_template_string(HOME_TEMPLATE, short_code=short_code, stats=stats)

@app.route('/<short_code>')
def redirect_to_url(short_code):
    """Redirect to the original URL."""
    result = api.redirect_to_original(short_code)
    
    if 'error' in result:
        return jsonify(result), 404
    
    return redirect(result['redirect_to'])

@app.route('/api/shorten', methods=['POST'])
def shorten_url():
    """API endpoint to shorten a URL."""
    data = request.json
    url = data.get('url')
    
    if not url:
        return jsonify({'error': 'URL is required'}), 400
    
    result = api.shorten_url(url)
    
    if 'error' in result:
        return jsonify(result), 400
    
    return jsonify(result)

@app.route('/api/stats/<short_code>')
def get_stats(short_code):
    """API endpoint to get URL statistics."""
    result = api.get_url_stats(short_code)
    
    if 'error' in result:
        return jsonify(result), 404
    
    return jsonify(result)

if __name__ == '__main__':
    app.run(debug=True)
