"""
URL Shortener Implementation

This module implements a simple URL shortener service following the principles of:
- Separation of Concerns
- Single Responsibility
- KISS (Keep It Simple, Stupid)
- DRY (Don't Repeat Yourself)
"""
import random
import string
import sqlite3
from datetime import datetime
from typing import Dict, Optional, Tuple, Any

# 1. Data Access Layer
class URLRepository:
    """Handles all database operations for URL mappings."""
    
    def __init__(self, db_path: str = "url_shortener.db"):
        """Initialize the repository with a database connection."""
        self.conn = sqlite3.connect(db_path)
        self._create_tables()
    
    def _create_tables(self) -> None:
        """Create necessary tables if they don't exist."""
        cursor = self.conn.cursor()
        
        # URL mappings table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS url_mappings (
            short_code TEXT PRIMARY KEY,
            original_url TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        # Click statistics table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS click_stats (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            short_code TEXT,
            clicked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (short_code) REFERENCES url_mappings (short_code)
        )
        ''')
        
        self.conn.commit()
    
    def save_url_mapping(self, short_code: str, original_url: str) -> None:
        """Save a new URL mapping."""
        cursor = self.conn.cursor()
        cursor.execute(
            "INSERT INTO url_mappings (short_code, original_url) VALUES (?, ?)",
            (short_code, original_url)
        )
        self.conn.commit()
    
    def get_original_url(self, short_code: str) -> Optional[str]:
        """Retrieve the original URL for a given short code."""
        cursor = self.conn.cursor()
        result = cursor.execute(
            "SELECT original_url FROM url_mappings WHERE short_code = ?",
            (short_code,)
        ).fetchone()
        
        return result[0] if result else None
    
    def record_click(self, short_code: str) -> None:
        """Record a click event for a short URL."""
        cursor = self.conn.cursor()
        cursor.execute(
            "INSERT INTO click_stats (short_code) VALUES (?)",
            (short_code,)
        )
        self.conn.commit()
    
    def get_click_count(self, short_code: str) -> int:
        """Get the number of clicks for a short URL."""
        cursor = self.conn.cursor()
        result = cursor.execute(
            "SELECT COUNT(*) FROM click_stats WHERE short_code = ?",
            (short_code,)
        ).fetchone()
        
        return result[0] if result else 0
    
    def get_url_stats(self, short_code: str) -> Optional[Dict[str, Any]]:
        """Get comprehensive statistics for a short URL."""
        cursor = self.conn.cursor()
        result = cursor.execute(
            """
            SELECT m.original_url, m.created_at, COUNT(s.id) as clicks
            FROM url_mappings m
            LEFT JOIN click_stats s ON m.short_code = s.short_code
            WHERE m.short_code = ?
            GROUP BY m.short_code
            """,
            (short_code,)
        ).fetchone()
        
        if not result:
            return None
        
        return {
            "original_url": result[0],
            "created_at": result[1],
            "clicks": result[2]
        }
    
    def close(self) -> None:
        """Close the database connection."""
        self.conn.close()

# 2. Service Layer
class URLService:
    """Contains business logic for URL shortening."""
    
    def __init__(self, repository: URLRepository, base_url: str = "http://short.url/"):
        """Initialize the service with a repository and base URL."""
        self.repository = repository
        self.base_url = base_url
    
    def create_short_url(self, original_url: str) -> str:
        """Create a short URL for the given original URL."""
        short_code = self._generate_short_code()
        
        # Ensure the short code is unique
        while self.repository.get_original_url(short_code) is not None:
            short_code = self._generate_short_code()
        
        self.repository.save_url_mapping(short_code, original_url)
        
        return f"{self.base_url}{short_code}"
    
    def get_original_url(self, short_code: str) -> Optional[str]:
        """Get the original URL for a given short code."""
        return self.repository.get_original_url(short_code)
    
    def record_click(self, short_code: str) -> None:
        """Record a click for a short URL."""
        self.repository.record_click(short_code)
    
    def get_url_stats(self, short_code: str) -> Optional[Dict[str, Any]]:
        """Get statistics for a short URL."""
        return self.repository.get_url_stats(short_code)
    
    def _generate_short_code(self, length: int = 6) -> str:
        """Generate a random short code."""
        characters = string.ascii_letters + string.digits
        return ''.join(random.choice(characters) for _ in range(length))

# 3. API Layer (simplified for demonstration)
class URLShortenerAPI:
    """Handles API requests for URL shortening."""
    
    def __init__(self, url_service: URLService):
        """Initialize the API with a URL service."""
        self.url_service = url_service
    
    def shorten_url(self, original_url: str) -> Dict[str, str]:
        """Shorten a URL and return the result."""
        if not original_url:
            return {"error": "URL is required"}
        
        if not self._is_valid_url(original_url):
            return {"error": "Invalid URL format"}
        
        short_url = self.url_service.create_short_url(original_url)
        return {"short_url": short_url}
    
    def redirect_to_original(self, short_code: str) -> Dict[str, str]:
        """Get redirection information for a short code."""
        original_url = self.url_service.get_original_url(short_code)
        
        if not original_url:
            return {"error": "URL not found"}
        
        # Record the click
        self.url_service.record_click(short_code)
        
        return {"redirect_to": original_url}
    
    def get_url_stats(self, short_code: str) -> Dict[str, Any]:
        """Get statistics for a short URL."""
        stats = self.url_service.get_url_stats(short_code)
        
        if not stats:
            return {"error": "URL not found"}
        
        return stats
    
    def _is_valid_url(self, url: str) -> bool:
        """Validate URL format (simplified)."""
        return url.startswith(('http://', 'https://'))

# Example usage
def main():
    # Set up the components
    repository = URLRepository()
    url_service = URLService(repository)
    api = URLShortenerAPI(url_service)
    
    # Example: Shorten a URL
    result = api.shorten_url("https://example.com/very/long/url/that/needs/shortening")
    print(f"Shortened URL: {result.get('short_url')}")
    
    # Extract the short code from the result
    short_code = result.get('short_url').split('/')[-1]
    
    # Example: Redirect to original URL
    redirect_result = api.redirect_to_original(short_code)
    print(f"Redirecting to: {redirect_result.get('redirect_to')}")
    
    # Example: Get URL statistics
    stats_result = api.get_url_stats(short_code)
    print(f"URL Statistics: {stats_result}")
    
    # Clean up
    repository.close()

if __name__ == "__main__":
    main()
