"""
Fault-Tolerant API Client

This module implements a fault-tolerant HTTP client for external APIs with:
1. Retry mechanism for transient failures
2. Circuit breaker for persistent failures
3. Fallbacks for when the API is unavailable
4. Timeout handling
5. Logging and monitoring
"""
import requests
import time
import random
import logging
import threading
from enum import Enum
from typing import Any, Callable, Dict, List, Optional, TypeVar, Union
from dataclasses import dataclass

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("fault_tolerant_api")

# Type variable for generic return types
T = TypeVar('T')

class CircuitState(Enum):
    """Possible states for the circuit breaker."""
    CLOSED = "CLOSED"  # Normal operation, requests pass through
    OPEN = "OPEN"      # Circuit is open, requests fail fast
    HALF_OPEN = "HALF_OPEN"  # Testing if the service has recovered


@dataclass
class CircuitBreakerConfig:
    """Configuration for the circuit breaker."""
    failure_threshold: int = 5  # Number of failures before opening circuit
    success_threshold: int = 3  # Number of successes in half-open state to close circuit
    timeout_seconds: int = 60   # Time to wait before transitioning from open to half-open
    

@dataclass
class RetryConfig:
    """Configuration for the retry mechanism."""
    max_retries: int = 3  # Maximum number of retry attempts
    initial_backoff_ms: int = 100  # Initial backoff time in milliseconds
    max_backoff_ms: int = 5000  # Maximum backoff time in milliseconds
    backoff_multiplier: float = 2.0  # Multiplier for exponential backoff
    jitter_factor: float = 0.1  # Random jitter factor to add to backoff


@dataclass
class TimeoutConfig:
    """Configuration for request timeouts."""
    connect_timeout_seconds: float = 3.0  # Timeout for establishing connection
    read_timeout_seconds: float = 10.0  # Timeout for reading response


@dataclass
class ApiClientConfig:
    """Configuration for the API client."""
    base_url: str
    circuit_breaker: CircuitBreakerConfig = CircuitBreakerConfig()
    retry: RetryConfig = RetryConfig()
    timeout: TimeoutConfig = TimeoutConfig()
    headers: Dict[str, str] = None


class CircuitBreaker:
    """
    Implementation of the Circuit Breaker pattern.
    
    Tracks failures and prevents calls to services that are likely to fail.
    """
    
    def __init__(self, config: CircuitBreakerConfig):
        """Initialize the circuit breaker with the given configuration."""
        self.config = config
        self.state = CircuitState.CLOSED
        self.failure_count = 0
        self.success_count = 0
        self.last_failure_time = 0
        self.lock = threading.RLock()
        
    def allow_request(self) -> bool:
        """
        Check if a request should be allowed based on the current circuit state.
        
        Returns:
            bool: True if the request should be allowed, False otherwise
        """
        with self.lock:
            if self.state == CircuitState.CLOSED:
                return True
                
            if self.state == CircuitState.OPEN:
                # Check if timeout has elapsed to transition to half-open
                if time.time() - self.last_failure_time > self.config.timeout_seconds:
                    logger.info("Circuit transitioning from OPEN to HALF_OPEN")
                    self.state = CircuitState.HALF_OPEN
                    self.success_count = 0
                    return True
                return False
                
            if self.state == CircuitState.HALF_OPEN:
                # In half-open state, only allow limited requests to test the service
                return self.success_count < self.config.success_threshold
                
            return True
    
    def record_success(self) -> None:
        """Record a successful request."""
        with self.lock:
            if self.state == CircuitState.HALF_OPEN:
                self.success_count += 1
                if self.success_count >= self.config.success_threshold:
                    logger.info("Circuit transitioning from HALF_OPEN to CLOSED")
                    self.state = CircuitState.CLOSED
                    self.failure_count = 0
                    self.success_count = 0
            elif self.state == CircuitState.CLOSED:
                self.failure_count = 0
    
    def record_failure(self) -> None:
        """Record a failed request."""
        with self.lock:
            self.last_failure_time = time.time()
            
            if self.state == CircuitState.CLOSED:
                self.failure_count += 1
                if self.failure_count >= self.config.failure_threshold:
                    logger.warning("Circuit transitioning from CLOSED to OPEN")
                    self.state = CircuitState.OPEN
                    
            elif self.state == CircuitState.HALF_OPEN:
                logger.warning("Failure in HALF_OPEN state, circuit transitioning back to OPEN")
                self.state = CircuitState.OPEN
                self.success_count = 0
    
    def get_state(self) -> CircuitState:
        """Get the current state of the circuit breaker."""
        with self.lock:
            return self.state


class FaultTolerantApiClient:
    """
    A fault-tolerant HTTP client for external APIs.
    
    Features:
    - Retry mechanism for transient failures
    - Circuit breaker for persistent failures
    - Fallbacks for when the API is unavailable
    - Timeout handling
    - Logging and monitoring
    """
    
    def __init__(self, config: ApiClientConfig):
        """Initialize the API client with the given configuration."""
        self.config = config
        self.circuit_breaker = CircuitBreaker(config.circuit_breaker)
        self.session = requests.Session()
        if config.headers:
            self.session.headers.update(config.headers)
        
        # Metrics for monitoring
        self.metrics = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "circuit_open_count": 0,
            "retry_count": 0,
            "fallback_count": 0,
            "timeout_count": 0
        }
        self.metrics_lock = threading.Lock()
    
    def get(self, path: str, params: Dict[str, Any] = None, 
            fallback: Callable[[], T] = None) -> Union[Dict[str, Any], T]:
        """
        Perform a GET request with fault tolerance.
        
        Args:
            path: The API endpoint path
            params: Query parameters
            fallback: Function to call when the request fails
            
        Returns:
            The API response as a dictionary or the fallback result
        """
        return self._request_with_fallback(
            method="GET",
            path=path,
            params=params,
            fallback=fallback
        )
    
    def post(self, path: str, data: Dict[str, Any] = None, json: Dict[str, Any] = None,
             fallback: Callable[[], T] = None) -> Union[Dict[str, Any], T]:
        """
        Perform a POST request with fault tolerance.
        
        Args:
            path: The API endpoint path
            data: Form data
            json: JSON data
            fallback: Function to call when the request fails
            
        Returns:
            The API response as a dictionary or the fallback result
        """
        return self._request_with_fallback(
            method="POST",
            path=path,
            data=data,
            json=json,
            fallback=fallback
        )
    
    def put(self, path: str, data: Dict[str, Any] = None, json: Dict[str, Any] = None,
            fallback: Callable[[], T] = None) -> Union[Dict[str, Any], T]:
        """
        Perform a PUT request with fault tolerance.
        
        Args:
            path: The API endpoint path
            data: Form data
            json: JSON data
            fallback: Function to call when the request fails
            
        Returns:
            The API response as a dictionary or the fallback result
        """
        return self._request_with_fallback(
            method="PUT",
            path=path,
            data=data,
            json=json,
            fallback=fallback
        )
    
    def delete(self, path: str, fallback: Callable[[], T] = None) -> Union[Dict[str, Any], T]:
        """
        Perform a DELETE request with fault tolerance.
        
        Args:
            path: The API endpoint path
            fallback: Function to call when the request fails
            
        Returns:
            The API response as a dictionary or the fallback result
        """
        return self._request_with_fallback(
            method="DELETE",
            path=path,
            fallback=fallback
        )
    
    def _request_with_fallback(self, method: str, path: str, fallback: Callable[[], T] = None, 
                              **kwargs) -> Union[Dict[str, Any], T]:
        """
        Perform an HTTP request with circuit breaking, retries, and fallback.
        
        Args:
            method: HTTP method (GET, POST, etc.)
            path: API endpoint path
            fallback: Function to call when the request fails
            **kwargs: Additional arguments to pass to the request
            
        Returns:
            The API response as a dictionary or the fallback result
        """
        # Update metrics
        with self.metrics_lock:
            self.metrics["total_requests"] += 1
        
        # Check if circuit breaker allows the request
        if not self.circuit_breaker.allow_request():
            logger.warning(f"Circuit breaker is open, failing fast for {method} {path}")
            with self.metrics_lock:
                self.metrics["circuit_open_count"] += 1
            return self._handle_fallback(fallback, f"Circuit breaker open for {path}")
        
        # Add timeout configuration
        kwargs.setdefault('timeout', (
            self.config.timeout.connect_timeout_seconds,
            self.config.timeout.read_timeout_seconds
        ))
        
        # Construct the full URL
        url = f"{self.config.base_url.rstrip('/')}/{path.lstrip('/')}"
        
        # Try the request with retries
        retry_config = self.config.retry
        retries = 0
        
        while True:
            try:
                logger.debug(f"Attempting {method} request to {url}")
                response = self.session.request(method, url, **kwargs)
                
                # Check if the response indicates success
                response.raise_for_status()
                
                # Record success in circuit breaker
                self.circuit_breaker.record_success()
                
                # Update metrics
                with self.metrics_lock:
                    self.metrics["successful_requests"] += 1
                
                # Return the response data
                return response.json()
                
            except (requests.exceptions.RequestException, ValueError) as e:
                # Determine if we should retry
                should_retry = (
                    retries < retry_config.max_retries and 
                    self._is_retriable_error(e)
                )
                
                if should_retry:
                    retries += 1
                    with self.metrics_lock:
                        self.metrics["retry_count"] += 1
                    
                    # Calculate backoff time with exponential backoff and jitter
                    backoff_ms = min(
                        retry_config.initial_backoff_ms * (retry_config.backoff_multiplier ** (retries - 1)),
                        retry_config.max_backoff_ms
                    )
                    jitter_ms = random.uniform(
                        -retry_config.jitter_factor * backoff_ms,
                        retry_config.jitter_factor * backoff_ms
                    )
                    sleep_time = (backoff_ms + jitter_ms) / 1000.0  # Convert to seconds
                    
                    logger.info(f"Request failed, retrying in {sleep_time:.2f}s ({retries}/{retry_config.max_retries}): {str(e)}")
                    time.sleep(sleep_time)
                    continue
                
                # We've exhausted retries or encountered a non-retriable error
                logger.error(f"Request failed after {retries} retries: {str(e)}")
                
                # Record failure in circuit breaker
                self.circuit_breaker.record_failure()
                
                # Update metrics
                with self.metrics_lock:
                    self.metrics["failed_requests"] += 1
                    if isinstance(e, requests.exceptions.Timeout):
                        self.metrics["timeout_count"] += 1
                
                # Use fallback if provided
                return self._handle_fallback(fallback, str(e))
    
    def _is_retriable_error(self, error: Exception) -> bool:
        """
        Determine if an error should trigger a retry.
        
        Args:
            error: The exception that occurred
            
        Returns:
            True if the error is retriable, False otherwise
        """
        # Network-related errors are generally retriable
        if isinstance(error, (
            requests.exceptions.ConnectionError,
            requests.exceptions.Timeout,
            requests.exceptions.TooManyRedirects
        )):
            return True
        
        # Some HTTP status codes are retriable
        if isinstance(error, requests.exceptions.HTTPError):
            status_code = error.response.status_code
            # 429 (Too Many Requests) and 5xx errors are generally retriable
            return status_code == 429 or 500 <= status_code < 600
        
        return False
    
    def _handle_fallback(self, fallback: Callable[[], T], error_message: str) -> Union[Dict[str, Any], T]:
        """
        Handle fallback logic when a request fails.
        
        Args:
            fallback: Function to call for fallback
            error_message: The error message
            
        Returns:
            The fallback result or an error dictionary
        """
        if fallback:
            logger.info(f"Using fallback for failed request: {error_message}")
            with self.metrics_lock:
                self.metrics["fallback_count"] += 1
            try:
                return fallback()
            except Exception as e:
                logger.error(f"Fallback also failed: {str(e)}")
                return {"error": "Service unavailable", "message": error_message}
        else:
            return {"error": "Service unavailable", "message": error_message}
    
    def get_metrics(self) -> Dict[str, int]:
        """Get the current metrics for monitoring."""
        with self.metrics_lock:
            return self.metrics.copy()
    
    def get_circuit_state(self) -> str:
        """Get the current state of the circuit breaker."""
        return self.circuit_breaker.get_state().value
    
    def reset_metrics(self) -> None:
        """Reset all metrics counters to zero."""
        with self.metrics_lock:
            for key in self.metrics:
                self.metrics[key] = 0


# Example usage
if __name__ == "__main__":
    # Configure the client
    config = ApiClientConfig(
        base_url="https://api.example.com",
        circuit_breaker=CircuitBreakerConfig(
            failure_threshold=3,
            success_threshold=2,
            timeout_seconds=30
        ),
        retry=RetryConfig(
            max_retries=3,
            initial_backoff_ms=200,
            max_backoff_ms=2000,
            backoff_multiplier=2.0,
            jitter_factor=0.2
        ),
        timeout=TimeoutConfig(
            connect_timeout_seconds=2.0,
            read_timeout_seconds=5.0
        ),
        headers={
            "User-Agent": "FaultTolerantApiClient/1.0",
            "Accept": "application/json"
        }
    )
    
    # Create the client
    client = FaultTolerantApiClient(config)
    
    # Example GET request with fallback
    def fallback_data():
        return {"data": "fallback", "source": "cache"}
    
    try:
        # This would normally call the API
        result = client.get("users/123", fallback=fallback_data)
        print(f"Result: {result}")
        
        # Check metrics
        print(f"Metrics: {client.get_metrics()}")
        print(f"Circuit state: {client.get_circuit_state()}")
    except Exception as e:
        print(f"Error: {str(e)}")
