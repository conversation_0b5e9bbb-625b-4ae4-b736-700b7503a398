"""
Tests for the Fault-Tolerant API Client.

This module contains tests that demonstrate the fault tolerance features:
1. Retry mechanism
2. Circuit breaker
3. Fallbacks
4. Timeout handling
"""
import unittest
import time
import responses
import requests
from unittest.mock import patch, MagicMock
from api_client import (
    FaultTolerantApiClient, 
    ApiClientConfig, 
    CircuitBreakerConfig, 
    RetryConfig, 
    TimeoutConfig,
    CircuitState
)

class TestFaultTolerantApiClient(unittest.TestCase):
    """Test cases for the FaultTolerantApiClient."""
    
    def setUp(self):
        """Set up a client for testing."""
        self.config = ApiClientConfig(
            base_url="https://api.example.com",
            circuit_breaker=CircuitBreakerConfig(
                failure_threshold=3,
                success_threshold=2,
                timeout_seconds=1  # Short timeout for testing
            ),
            retry=RetryConfig(
                max_retries=2,
                initial_backoff_ms=10,  # Short backoff for testing
                max_backoff_ms=100,
                backoff_multiplier=2.0,
                jitter_factor=0.1
            ),
            timeout=TimeoutConfig(
                connect_timeout_seconds=0.5,
                read_timeout_seconds=1.0
            )
        )
        self.client = FaultTolerantApiClient(self.config)
    
    @responses.activate
    def test_successful_request(self):
        """Test a successful API request."""
        # Mock a successful response
        responses.add(
            responses.GET,
            "https://api.example.com/users/123",
            json={"id": 123, "name": "Test User"},
            status=200
        )
        
        # Make the request
        result = self.client.get("users/123")
        
        # Verify the result
        self.assertEqual(result, {"id": 123, "name": "Test User"})
        
        # Check metrics
        metrics = self.client.get_metrics()
        self.assertEqual(metrics["total_requests"], 1)
        self.assertEqual(metrics["successful_requests"], 1)
        self.assertEqual(metrics["failed_requests"], 0)
        self.assertEqual(metrics["retry_count"], 0)
        
        # Check circuit state
        self.assertEqual(self.client.get_circuit_state(), CircuitState.CLOSED.value)
    
    @responses.activate
    def test_retry_on_server_error(self):
        """Test retry behavior on server errors."""
        # Mock a server error followed by a success
        responses.add(
            responses.GET,
            "https://api.example.com/users/123",
            json={"error": "Internal Server Error"},
            status=500
        )
        responses.add(
            responses.GET,
            "https://api.example.com/users/123",
            json={"id": 123, "name": "Test User"},
            status=200
        )
        
        # Make the request
        result = self.client.get("users/123")
        
        # Verify the result
        self.assertEqual(result, {"id": 123, "name": "Test User"})
        
        # Check metrics
        metrics = self.client.get_metrics()
        self.assertEqual(metrics["total_requests"], 1)
        self.assertEqual(metrics["successful_requests"], 1)
        self.assertEqual(metrics["failed_requests"], 0)
        self.assertEqual(metrics["retry_count"], 1)
    
    @responses.activate
    def test_fallback_after_max_retries(self):
        """Test fallback after exhausting retries."""
        # Mock multiple server errors
        for _ in range(3):  # Initial request + 2 retries
            responses.add(
                responses.GET,
                "https://api.example.com/users/123",
                json={"error": "Internal Server Error"},
                status=500
            )
        
        # Define a fallback function
        fallback_called = False
        def fallback():
            nonlocal fallback_called
            fallback_called = True
            return {"id": 123, "name": "Fallback User"}
        
        # Make the request with fallback
        result = self.client.get("users/123", fallback=fallback)
        
        # Verify the result
        self.assertEqual(result, {"id": 123, "name": "Fallback User"})
        self.assertTrue(fallback_called)
        
        # Check metrics
        metrics = self.client.get_metrics()
        self.assertEqual(metrics["total_requests"], 1)
        self.assertEqual(metrics["successful_requests"], 0)
        self.assertEqual(metrics["failed_requests"], 1)
        self.assertEqual(metrics["retry_count"], 2)
        self.assertEqual(metrics["fallback_count"], 1)
    
    @responses.activate
    def test_circuit_breaker_opens_after_failures(self):
        """Test that the circuit breaker opens after multiple failures."""
        # Mock multiple server errors
        for _ in range(5):  # More than failure_threshold
            responses.add(
                responses.GET,
                "https://api.example.com/users/123",
                json={"error": "Internal Server Error"},
                status=500
            )
        
        # Make requests until the circuit opens
        for _ in range(3):  # failure_threshold
            result = self.client.get("users/123")
            self.assertEqual(result, {"error": "Service unavailable", "message": "HTTP Error 500: Internal Server Error"})
        
        # Verify circuit is open
        self.assertEqual(self.client.get_circuit_state(), CircuitState.OPEN.value)
        
        # Next request should fail fast without calling the API
        before_count = len(responses.calls)
        result = self.client.get("users/123")
        after_count = len(responses.calls)
        
        # Verify no additional API call was made
        self.assertEqual(before_count, after_count)
        
        # Verify the result indicates circuit open
        self.assertEqual(result["error"], "Service unavailable")
        self.assertTrue("Circuit breaker open" in result["message"])
        
        # Check metrics
        metrics = self.client.get_metrics()
        self.assertEqual(metrics["circuit_open_count"], 1)
    
    @responses.activate
    def test_circuit_half_open_after_timeout(self):
        """Test that the circuit transitions to half-open after timeout."""
        # Mock multiple server errors
        for _ in range(5):
            responses.add(
                responses.GET,
                "https://api.example.com/users/123",
                json={"error": "Internal Server Error"},
                status=500
            )
        
        # Make requests until the circuit opens
        for _ in range(3):  # failure_threshold
            self.client.get("users/123")
        
        # Verify circuit is open
        self.assertEqual(self.client.get_circuit_state(), CircuitState.OPEN.value)
        
        # Wait for timeout
        time.sleep(1.1)  # Just over the timeout_seconds
        
        # Circuit should now be half-open
        self.assertEqual(self.client.get_circuit_state(), CircuitState.HALF_OPEN.value)
        
        # Add a successful response
        responses.add(
            responses.GET,
            "https://api.example.com/users/123",
            json={"id": 123, "name": "Test User"},
            status=200
        )
        
        # Make a request - should be allowed in half-open state
        result = self.client.get("users/123")
        self.assertEqual(result, {"id": 123, "name": "Test User"})
        
        # Add another successful response
        responses.add(
            responses.GET,
            "https://api.example.com/users/123",
            json={"id": 123, "name": "Test User"},
            status=200
        )
        
        # Make another request
        self.client.get("users/123")
        
        # Circuit should now be closed after success_threshold successes
        self.assertEqual(self.client.get_circuit_state(), CircuitState.CLOSED.value)
    
    @patch('requests.Session.request')
    def test_timeout_handling(self, mock_request):
        """Test handling of request timeouts."""
        # Mock a timeout
        mock_request.side_effect = requests.exceptions.Timeout("Request timed out")
        
        # Make the request
        result = self.client.get("users/123")
        
        # Verify the result
        self.assertEqual(result["error"], "Service unavailable")
        self.assertTrue("Request timed out" in result["message"])
        
        # Check metrics
        metrics = self.client.get_metrics()
        self.assertEqual(metrics["timeout_count"], 1)
    
    @responses.activate
    def test_different_http_methods(self):
        """Test that different HTTP methods work correctly."""
        # Mock responses for different methods
        responses.add(
            responses.POST,
            "https://api.example.com/users",
            json={"id": 123, "name": "New User"},
            status=201
        )
        responses.add(
            responses.PUT,
            "https://api.example.com/users/123",
            json={"id": 123, "name": "Updated User"},
            status=200
        )
        responses.add(
            responses.DELETE,
            "https://api.example.com/users/123",
            json={"status": "deleted"},
            status=200
        )
        
        # Test POST
        result = self.client.post("users", json={"name": "New User"})
        self.assertEqual(result, {"id": 123, "name": "New User"})
        
        # Test PUT
        result = self.client.put("users/123", json={"name": "Updated User"})
        self.assertEqual(result, {"id": 123, "name": "Updated User"})
        
        # Test DELETE
        result = self.client.delete("users/123")
        self.assertEqual(result, {"status": "deleted"})
    
    def test_reset_metrics(self):
        """Test resetting metrics."""
        # Update some metrics
        with self.client.metrics_lock:
            self.client.metrics["total_requests"] = 10
            self.client.metrics["successful_requests"] = 8
            self.client.metrics["failed_requests"] = 2
        
        # Reset metrics
        self.client.reset_metrics()
        
        # Verify all metrics are zero
        metrics = self.client.get_metrics()
        for key, value in metrics.items():
            self.assertEqual(value, 0, f"Metric {key} should be 0 after reset")


if __name__ == "__main__":
    unittest.main()
