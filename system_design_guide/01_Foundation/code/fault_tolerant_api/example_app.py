"""
Example Application Using the Fault-Tolerant API Client

This example demonstrates how to use the fault-tolerant API client
in a real-world application scenario.
"""
import time
import logging
from flask import Flask, jsonify, request
from api_client import (
    FaultTolerantApiClient, 
    ApiClientConfig, 
    CircuitBreakerConfig, 
    RetryConfig, 
    TimeoutConfig
)

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("example_app")

# Create Flask app
app = Flask(__name__)

# Configure the API client
weather_api_config = ApiClientConfig(
    base_url="https://api.weatherapi.com/v1",
    circuit_breaker=CircuitBreakerConfig(
        failure_threshold=5,
        success_threshold=3,
        timeout_seconds=60
    ),
    retry=RetryConfig(
        max_retries=3,
        initial_backoff_ms=200,
        max_backoff_ms=2000
    ),
    timeout=TimeoutConfig(
        connect_timeout_seconds=3.0,
        read_timeout_seconds=10.0
    ),
    headers={
        "User-Agent": "WeatherApp/1.0",
        "Accept": "application/json"
    }
)

# Create the API client
weather_api = FaultTolerantApiClient(weather_api_config)

# In-memory cache for weather data
weather_cache = {}

def get_cached_weather(location):
    """Get weather data from cache if available and not expired."""
    if location in weather_cache:
        cache_entry = weather_cache[location]
        # Check if cache entry is less than 30 minutes old
        if time.time() - cache_entry["timestamp"] < 1800:
            logger.info(f"Using cached weather data for {location}")
            return cache_entry["data"]
    return None

def cache_weather_data(location, data):
    """Cache weather data with timestamp."""
    weather_cache[location] = {
        "data": data,
        "timestamp": time.time()
    }

def get_default_weather(location):
    """Provide default weather data when API is unavailable."""
    logger.warning(f"Using default weather data for {location}")
    return {
        "location": location,
        "temperature": 20,
        "condition": "Unknown",
        "humidity": 50,
        "source": "default"
    }

@app.route('/weather/<location>')
def get_weather(location):
    """
    Get weather information for a location.
    
    Uses the fault-tolerant API client to fetch data from the weather API,
    with caching and fallback mechanisms.
    """
    # Check cache first
    cached_data = get_cached_weather(location)
    if cached_data:
        cached_data["source"] = "cache"
        return jsonify(cached_data)
    
    # Define fallback function that uses cache or defaults
    def weather_fallback():
        # Try to use slightly expired cache data as fallback
        for loc, entry in weather_cache.items():
            if loc == location and time.time() - entry["timestamp"] < 86400:  # 24 hours
                logger.info(f"Using expired cache as fallback for {location}")
                data = entry["data"].copy()
                data["source"] = "expired_cache"
                return data
        
        # If no suitable cache entry, return default data
        return get_default_weather(location)
    
    # Fetch from API with fallback
    try:
        # Add API key from request args if provided
        params = {"q": location}
        if "api_key" in request.args:
            params["key"] = request.args["api_key"]
        
        # Make the API request
        weather_data = weather_api.get(
            "current.json", 
            params=params,
            fallback=weather_fallback
        )
        
        # Process and simplify the response
        if "error" in weather_data:
            # API returned an error
            return jsonify(weather_fallback())
        
        # Extract relevant data
        processed_data = {
            "location": weather_data.get("location", {}).get("name", location),
            "temperature": weather_data.get("current", {}).get("temp_c"),
            "condition": weather_data.get("current", {}).get("condition", {}).get("text"),
            "humidity": weather_data.get("current", {}).get("humidity"),
            "source": "api"
        }
        
        # Cache the processed data
        cache_weather_data(location, processed_data)
        
        return jsonify(processed_data)
        
    except Exception as e:
        logger.error(f"Unexpected error getting weather for {location}: {str(e)}")
        return jsonify(weather_fallback())

@app.route('/metrics')
def get_metrics():
    """Get metrics about the API client."""
    return jsonify({
        "api_metrics": weather_api.get_metrics(),
        "circuit_state": weather_api.get_circuit_state(),
        "cache_size": len(weather_cache)
    })

@app.route('/health')
def health_check():
    """Health check endpoint."""
    circuit_state = weather_api.get_circuit_state()
    is_healthy = circuit_state != "OPEN"
    
    return jsonify({
        "status": "healthy" if is_healthy else "degraded",
        "circuit_state": circuit_state,
        "timestamp": time.time()
    }), 200 if is_healthy else 503

if __name__ == "__main__":
    app.run(debug=True, port=5000)
