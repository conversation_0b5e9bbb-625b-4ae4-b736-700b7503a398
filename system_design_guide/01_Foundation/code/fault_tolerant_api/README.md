# Fault-Tolerant API Client

This is a practical implementation of a fault-tolerant HTTP client that demonstrates the reliability and fault tolerance concepts covered in Module 1.

## Features

- **Retry Mechanism**: Automatically retries failed requests with exponential backoff and jitter
- **Circuit Breaker**: Prevents cascading failures by failing fast when a service is unhealthy
- **Fallbacks**: Provides alternative responses when the primary service is unavailable
- **Timeout Handling**: Sets appropriate timeouts for connections and reads
- **Metrics Collection**: Tracks success/failure rates and other important metrics
- **Configurable Behavior**: Allows customization of all fault tolerance parameters

## Components

### 1. FaultTolerantApiClient

The main client class that provides fault-tolerant HTTP requests:
- Wraps the Python `requests` library with additional reliability features
- Supports standard HTTP methods (GET, POST, PUT, DELETE)
- Handles errors gracefully with configurable fallbacks
- Collects metrics for monitoring

### 2. CircuitBreaker

Implementation of the Circuit Breaker pattern:
- Tracks failure rates and prevents calls to failing services
- Supports three states: CLOSED, OPEN, and HALF-OPEN
- Automatically transitions between states based on success/failure patterns
- Provides fast failure when a service is known to be down

### 3. Configuration Classes

Structured configuration for different aspects of fault tolerance:
- `ApiClientConfig`: Overall client configuration
- `CircuitBreakerConfig`: Circuit breaker parameters
- `RetryConfig`: Retry behavior settings
- `TimeoutConfig`: Connection and read timeout values

## Example Application

The repository includes an example weather application that demonstrates how to use the fault-tolerant API client in a real-world scenario:
- Fetches weather data from an external API
- Implements caching as an additional reliability mechanism
- Provides graceful degradation when the API is unavailable
- Exposes metrics for monitoring

## Usage

### Basic Usage

```python
from api_client import FaultTolerantApiClient, ApiClientConfig

# Configure the client
config = ApiClientConfig(
    base_url="https://api.example.com",
    # Additional configuration options...
)

# Create the client
client = FaultTolerantApiClient(config)

# Make a request with a fallback
def fallback_data():
    return {"data": "fallback", "source": "cache"}

result = client.get("users/123", fallback=fallback_data)
```

### Configuration Options

```python
from api_client import (
    ApiClientConfig, 
    CircuitBreakerConfig, 
    RetryConfig, 
    TimeoutConfig
)

config = ApiClientConfig(
    base_url="https://api.example.com",
    circuit_breaker=CircuitBreakerConfig(
        failure_threshold=5,    # Number of failures before opening circuit
        success_threshold=3,    # Successes in half-open state to close circuit
        timeout_seconds=60      # Time before transitioning from open to half-open
    ),
    retry=RetryConfig(
        max_retries=3,          # Maximum retry attempts
        initial_backoff_ms=200, # Initial backoff time
        max_backoff_ms=2000,    # Maximum backoff time
        backoff_multiplier=2.0, # Multiplier for exponential backoff
        jitter_factor=0.1       # Random jitter factor
    ),
    timeout=TimeoutConfig(
        connect_timeout_seconds=3.0,  # Connection timeout
        read_timeout_seconds=10.0     # Read timeout
    ),
    headers={                   # Default headers for all requests
        "User-Agent": "MyApp/1.0",
        "Accept": "application/json"
    }
)
```

### Monitoring

```python
# Get current metrics
metrics = client.get_metrics()
print(f"Total requests: {metrics['total_requests']}")
print(f"Success rate: {metrics['successful_requests'] / metrics['total_requests'] * 100:.2f}%")

# Get circuit breaker state
circuit_state = client.get_circuit_state()
print(f"Circuit state: {circuit_state}")
```

## Requirements

- Python 3.7+
- requests
- Flask (for example application)
- responses (for tests)

## Running the Tests

```bash
pip install responses
python -m unittest test_api_client.py
```

## Running the Example Application

```bash
pip install flask
python example_app.py
```

Then visit:
- `http://localhost:5000/weather/London` to get weather data
- `http://localhost:5000/metrics` to see API client metrics
- `http://localhost:5000/health` for a health check endpoint

## Fault Tolerance Patterns Demonstrated

### 1. Circuit Breaker Pattern

The circuit breaker prevents cascading failures by:
- Tracking failure rates
- Opening the circuit when failures exceed a threshold
- Allowing limited testing in half-open state
- Automatically recovering when the service returns to health

### 2. Retry Pattern with Exponential Backoff

The retry mechanism handles transient failures by:
- Automatically retrying failed requests
- Using exponential backoff to avoid overwhelming the service
- Adding jitter to prevent synchronized retries
- Distinguishing between retriable and non-retriable errors

### 3. Fallback Pattern

The fallback mechanism provides graceful degradation by:
- Allowing custom fallback functions for each request
- Executing fallbacks when all retries fail
- Supporting different fallback strategies (caching, defaults, etc.)

### 4. Timeout Pattern

The timeout handling prevents hanging requests by:
- Setting appropriate connection and read timeouts
- Treating timeouts as retriable errors
- Providing metrics on timeout frequency

## Exercise

Try extending this implementation with the following features:
1. Bulkhead pattern to isolate different API endpoints
2. Request caching with time-based expiration
3. Distributed circuit breaker state using Redis
4. Adaptive timeout based on recent response times
5. Comprehensive logging with correlation IDs

## Next Steps

After understanding this implementation, consider how you would modify it to handle:
1. Asynchronous requests using `asyncio` and `aiohttp`
2. OAuth authentication with token refresh
3. GraphQL queries in addition to REST
4. Streaming responses for large data sets
5. WebSocket connections with reconnection logic
