# Database Optimization

## Introduction

Database operations are often the primary bottleneck in application performance. This section covers key strategies for optimizing database performance, from query optimization to schema design and beyond.

## Query Optimization

### 1. Understanding Query Execution Plans

Query execution plans show how the database will execute a query, including:
- Access methods (table scans, index scans)
- Join algorithms and order
- Filter application
- Sort operations

**Example (PostgreSQL):**
```sql
EXPLAIN ANALYZE
SELECT customers.name, SUM(orders.amount) 
FROM customers 
JOIN orders ON customers.id = orders.customer_id
WHERE customers.region = 'North'
GROUP BY customers.name
ORDER BY SUM(orders.amount) DESC;
```

**Key Components to Analyze:**
- Sequential vs. index scans
- Join types (nested loop, hash join, merge join)
- Filter efficiency
- Sort operations and memory usage

### 2. Index Optimization

Indexes accelerate data retrieval but come with maintenance costs.

**Index Types:**
- B-tree indexes: General-purpose, good for equality and range queries
- Hash indexes: Fast equality lookups
- GIN/GiST indexes: Full-text search, complex data types
- Spatial indexes: Geographical data
- Partial indexes: Index subset of rows
- Covering indexes: Include all needed columns

**Index Creation Example:**
```sql
-- Basic index
CREATE INDEX idx_customers_email ON customers(email);

-- Composite index
CREATE INDEX idx_orders_customer_date ON orders(customer_id, order_date);

-- Partial index
CREATE INDEX idx_orders_high_value ON orders(order_date)
WHERE amount > 1000;

-- Covering index
CREATE INDEX idx_products_search ON products(category, name)
INCLUDE (price, stock_status);
```

**Index Best Practices:**
- Index columns used in WHERE, JOIN, and ORDER BY clauses
- Put most selective columns first in composite indexes
- Don't over-index (each index adds overhead to writes)
- Consider covering indexes for frequent queries
- Regularly analyze index usage and remove unused indexes

### 3. Common Query Anti-patterns

**N+1 Query Problem:**
```javascript
// Inefficient: N+1 queries
const users = await db.query('SELECT * FROM users');
for (const user of users) {
  // This executes one query per user!
  const orders = await db.query('SELECT * FROM orders WHERE user_id = ?', [user.id]);
  user.orders = orders;
}

// Efficient: Single join query
const usersWithOrders = await db.query(`
  SELECT u.*, o.* 
  FROM users u
  LEFT JOIN orders o ON u.id = o.user_id
`);
// Process results to group orders by user
```

**Inefficient LIKE Queries:**
```sql
-- Inefficient: Can't use index effectively
SELECT * FROM products WHERE name LIKE '%keyboard%';

-- Better: Use full-text search
SELECT * FROM products 
WHERE to_tsvector('english', name) @@ to_tsquery('english', 'keyboard');
```

**Unnecessary Columns:**
```sql
-- Inefficient: Retrieving all columns
SELECT * FROM large_table WHERE condition;

-- Efficient: Retrieve only needed columns
SELECT id, name, status FROM large_table WHERE condition;
```

## Schema Optimization

### 1. Normalization vs. Denormalization

**Normalization Benefits:**
- Reduces data redundancy
- Ensures data integrity
- Simplifies updates

**Denormalization Benefits:**
- Reduces join operations
- Improves read performance
- Simplifies queries

**When to Normalize:**
- Write-heavy applications
- When data integrity is critical
- When storage is a concern

**When to Denormalize:**
- Read-heavy applications
- When query performance is critical
- When joins become a bottleneck

**Example of Denormalization:**
```sql
-- Normalized schema (two tables)
CREATE TABLE authors (
  id SERIAL PRIMARY KEY,
  name VARCHAR(100),
  bio TEXT
);

CREATE TABLE books (
  id SERIAL PRIMARY KEY,
  title VARCHAR(200),
  author_id INTEGER REFERENCES authors(id),
  published_date DATE
);

-- Denormalized schema (redundant author data)
CREATE TABLE books_denormalized (
  id SERIAL PRIMARY KEY,
  title VARCHAR(200),
  author_id INTEGER REFERENCES authors(id),
  author_name VARCHAR(100),  -- Denormalized from authors
  published_date DATE
);
```

### 2. Appropriate Data Types

Choosing the right data types impacts both storage and performance.

**Best Practices:**
- Use the smallest data type that fits your needs
- Consider storage alignment
- Use fixed-length types for frequently accessed columns
- Use variable-length types for large or variable content

**Examples:**
```sql
-- Inefficient
CREATE TABLE users (
  id VARCHAR(36),  -- UUID as string
  name VARCHAR(255),  -- Oversized for names
  active VARCHAR(5),  -- 'true'/'false' as string
  login_count VARCHAR(20),  -- Number as string
  description TEXT  -- For a small field
);

-- Optimized
CREATE TABLE users (
  id UUID,  -- Native UUID type
  name VARCHAR(100),  -- Reasonable size
  active BOOLEAN,  -- Boolean type
  login_count INTEGER,  -- Integer type
  description VARCHAR(500)  -- Limited size if appropriate
);
```

### 3. Partitioning

Partitioning divides large tables into smaller, more manageable pieces.

**Partitioning Strategies:**
- **Range Partitioning**: Based on value ranges (e.g., date ranges)
- **List Partitioning**: Based on discrete values (e.g., regions)
- **Hash Partitioning**: Based on hash of column value
- **Composite Partitioning**: Combining multiple strategies

**Example (PostgreSQL):**
```sql
-- Range partitioning by date
CREATE TABLE orders (
  id SERIAL,
  customer_id INTEGER,
  order_date DATE,
  amount DECIMAL(10,2)
) PARTITION BY RANGE (order_date);

-- Create partitions
CREATE TABLE orders_2021 PARTITION OF orders
  FOR VALUES FROM ('2021-01-01') TO ('2022-01-01');

CREATE TABLE orders_2022 PARTITION OF orders
  FOR VALUES FROM ('2022-01-01') TO ('2023-01-01');
```

**Benefits of Partitioning:**
- Improved query performance through partition pruning
- Easier management of large tables
- Efficient archiving of old data
- Parallel query execution across partitions

## Database Connection Management

### 1. Connection Pooling

Connection pooling reuses database connections to avoid the overhead of establishing new connections.

**Key Concepts:**
- Pool size configuration
- Connection lifecycle management
- Connection validation
- Statement caching

**Example (Java with HikariCP):**
```java
HikariConfig config = new HikariConfig();
config.setJdbcUrl("*************************************");
config.setUsername("user");
config.setPassword("password");
config.setMaximumPoolSize(10);
config.setMinimumIdle(5);
config.setIdleTimeout(300000); // 5 minutes
config.setConnectionTimeout(10000); // 10 seconds
config.addDataSourceProperty("cachePrepStmts", "true");
config.addDataSourceProperty("prepStmtCacheSize", "250");

HikariDataSource dataSource = new HikariDataSource(config);
```

**Best Practices:**
- Size the pool appropriately (too small: waiting; too large: resource waste)
- Set appropriate timeouts
- Validate connections before use
- Monitor pool metrics

### 2. Prepared Statements

Prepared statements improve performance and security by separating SQL from data.

**Benefits:**
- Reduced parsing overhead
- Protection against SQL injection
- Potential for server-side caching
- Type safety

**Example (Java):**
```java
// Inefficient: String concatenation
String query = "SELECT * FROM users WHERE email = '" + userEmail + "'";
Statement stmt = connection.createStatement();
ResultSet rs = stmt.executeQuery(query);

// Efficient: Prepared statement
String query = "SELECT * FROM users WHERE email = ?";
PreparedStatement pstmt = connection.prepareStatement(query);
pstmt.setString(1, userEmail);
ResultSet rs = pstmt.executeQuery();
```

## Caching Strategies

### 1. Database Query Cache

Many databases offer built-in query caching.

**MySQL Query Cache Example:**
```sql
-- Check if query cache is enabled
SHOW VARIABLES LIKE 'query_cache_type';

-- Set query cache size
SET GLOBAL query_cache_size = 67108864; -- 64MB

-- Query that will be cached
SELECT * FROM products WHERE category = 'electronics';
```

**PostgreSQL Statement Caching:**
```sql
-- Enable prepared statement caching
SET prepared_statement_cache_size = 100;
```

### 2. Application-Level Caching

Implementing caching in the application layer.

**Example (Redis with Node.js):**
```javascript
const redis = require('redis');
const client = redis.createClient();
const { promisify } = require('util');
const getAsync = promisify(client.get).bind(client);
const setAsync = promisify(client.set).bind(client);

async function getUserById(id) {
  // Try to get from cache first
  const cacheKey = `user:${id}`;
  const cachedUser = await getAsync(cacheKey);
  
  if (cachedUser) {
    return JSON.parse(cachedUser);
  }
  
  // If not in cache, get from database
  const user = await db.query('SELECT * FROM users WHERE id = ?', [id]);
  
  // Store in cache for future requests (expire after 1 hour)
  await setAsync(cacheKey, JSON.stringify(user), 'EX', 3600);
  
  return user;
}
```

**Caching Considerations:**
- Cache invalidation strategy
- Time-to-live (TTL) settings
- Memory usage
- Cache hit ratio monitoring
- Distributed caching for scaled applications

### 3. Materialized Views

Materialized views store the results of a query for faster access.

**Example (PostgreSQL):**
```sql
-- Create a materialized view
CREATE MATERIALIZED VIEW order_summary AS
SELECT 
  customer_id,
  COUNT(*) as order_count,
  SUM(amount) as total_spent,
  MAX(order_date) as last_order_date
FROM orders
GROUP BY customer_id;

-- Create an index on the materialized view
CREATE INDEX idx_order_summary_customer ON order_summary(customer_id);

-- Refresh the materialized view
REFRESH MATERIALIZED VIEW order_summary;
```

**Benefits:**
- Precomputed query results
- Significant performance improvement for complex queries
- Reduced load on the database

## Read/Write Splitting

Separating read and write operations to different database instances.

**Architecture:**
- Primary database handles writes
- Read replicas handle read queries
- Load balancer distributes read queries

**Example (Java with Spring):**
```java
@Configuration
public class DataSourceConfig {
    
    @Bean
    @Primary
    @ConfigurationProperties("spring.datasource.write")
    public DataSource writeDataSource() {
        return DataSourceBuilder.create().build();
    }
    
    @Bean
    @ConfigurationProperties("spring.datasource.read")
    public DataSource readDataSource() {
        return DataSourceBuilder.create().build();
    }
    
    @Bean
    public DataSource routingDataSource() {
        ReplicationRoutingDataSource routingDataSource = new ReplicationRoutingDataSource();
        
        Map<Object, Object> dataSources = new HashMap<>();
        dataSources.put("write", writeDataSource());
        dataSources.put("read", readDataSource());
        
        routingDataSource.setTargetDataSources(dataSources);
        routingDataSource.setDefaultTargetDataSource(writeDataSource());
        
        return routingDataSource;
    }
}

// Custom routing data source
public class ReplicationRoutingDataSource extends AbstractRoutingDataSource {
    @Override
    protected Object determineCurrentLookupKey() {
        return TransactionSynchronizationManager.isCurrentTransactionReadOnly() 
               ? "read" : "write";
    }
}
```

**Benefits:**
- Scales read capacity
- Reduces load on primary database
- Improves read performance
- Provides read availability during primary maintenance

## Practical Example: Optimizing a Product Search API

Let's optimize a product search API that's experiencing performance issues.

### Initial Implementation

```java
@RestController
@RequestMapping("/api/products")
public class ProductController {
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    @GetMapping("/search")
    public List<Product> searchProducts(
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String category,
            @RequestParam(required = false) Double minPrice,
            @RequestParam(required = false) Double maxPrice) {
        
        StringBuilder sql = new StringBuilder("SELECT * FROM products WHERE 1=1");
        List<Object> params = new ArrayList<>();
        
        if (name != null && !name.isEmpty()) {
            sql.append(" AND name LIKE ?");
            params.add("%" + name + "%");
        }
        
        if (category != null && !category.isEmpty()) {
            sql.append(" AND category = ?");
            params.add(category);
        }
        
        if (minPrice != null) {
            sql.append(" AND price >= ?");
            params.add(minPrice);
        }
        
        if (maxPrice != null) {
            sql.append(" AND price <= ?");
            params.add(maxPrice);
        }
        
        return jdbcTemplate.query(
            sql.toString(), 
            params.toArray(), 
            (rs, rowNum) -> new Product(
                rs.getLong("id"),
                rs.getString("name"),
                rs.getString("category"),
                rs.getDouble("price"),
                rs.getString("description")
            )
        );
    }
}
```

### Optimized Implementation

```java
@RestController
@RequestMapping("/api/products")
public class ProductController {
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    @Autowired
    private RedisTemplate<String, List<Product>> redisTemplate;
    
    @GetMapping("/search")
    public List<Product> searchProducts(
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String category,
            @RequestParam(required = false) Double minPrice,
            @RequestParam(required = false) Double maxPrice) {
        
        // Generate cache key based on search parameters
        String cacheKey = generateCacheKey(name, category, minPrice, maxPrice);
        
        // Try to get from cache
        List<Product> cachedResults = redisTemplate.opsForValue().get(cacheKey);
        if (cachedResults != null) {
            return cachedResults;
        }
        
        // Build optimized query
        StringBuilder sql = new StringBuilder();
        List<Object> params = new ArrayList<>();
        
        // Select only needed columns
        sql.append("SELECT id, name, category, price FROM products");
        
        // Use WHERE 1=1 only if we have conditions
        boolean hasConditions = false;
        
        if (name != null && !name.isEmpty()) {
            sql.append(hasConditions ? " AND" : " WHERE");
            // Use full-text search if available
            sql.append(" to_tsvector('english', name) @@ plainto_tsquery('english', ?)");
            params.add(name);
            hasConditions = true;
        }
        
        if (category != null && !category.isEmpty()) {
            sql.append(hasConditions ? " AND" : " WHERE");
            sql.append(" category = ?");
            params.add(category);
            hasConditions = true;
        }
        
        if (minPrice != null) {
            sql.append(hasConditions ? " AND" : " WHERE");
            sql.append(" price >= ?");
            params.add(minPrice);
            hasConditions = true;
        }
        
        if (maxPrice != null) {
            sql.append(hasConditions ? " AND" : " WHERE");
            sql.append(" price <= ?");
            params.add(maxPrice);
            hasConditions = true;
        }
        
        // Add limit to prevent excessive results
        sql.append(" LIMIT 100");
        
        // Execute query
        List<Product> results = jdbcTemplate.query(
            sql.toString(), 
            params.toArray(), 
            (rs, rowNum) -> new Product(
                rs.getLong("id"),
                rs.getString("name"),
                rs.getString("category"),
                rs.getDouble("price"),
                null  // Don't load description unless needed
            )
        );
        
        // Cache results for 10 minutes
        redisTemplate.opsForValue().set(cacheKey, results, 10, TimeUnit.MINUTES);
        
        return results;
    }
    
    private String generateCacheKey(String name, String category, Double minPrice, Double maxPrice) {
        return String.format("products:search:%s:%s:%s:%s",
            name == null ? "" : name,
            category == null ? "" : category,
            minPrice == null ? "" : minPrice,
            maxPrice == null ? "" : maxPrice
        );
    }
    
    // Separate endpoint for getting product details
    @GetMapping("/{id}")
    public Product getProductDetails(@PathVariable Long id) {
        return jdbcTemplate.queryForObject(
            "SELECT * FROM products WHERE id = ?",
            new Object[]{id},
            (rs, rowNum) -> new Product(
                rs.getLong("id"),
                rs.getString("name"),
                rs.getString("category"),
                rs.getDouble("price"),
                rs.getString("description")
            )
        );
    }
}
```

### Database Optimizations

```sql
-- Create indexes for search fields
CREATE INDEX idx_products_category ON products(category);
CREATE INDEX idx_products_price ON products(price);

-- Create full-text search index
CREATE INDEX idx_products_name_fts ON products USING GIN (to_tsvector('english', name));

-- Create a materialized view for common searches
CREATE MATERIALIZED VIEW popular_products AS
SELECT id, name, category, price
FROM products
WHERE price BETWEEN 10 AND 100
  AND category IN ('electronics', 'home', 'clothing')
ORDER BY popularity_score DESC
LIMIT 1000;

-- Create index on the materialized view
CREATE INDEX idx_popular_products_category ON popular_products(category);
CREATE INDEX idx_popular_products_price ON popular_products(price);

-- Refresh the materialized view periodically
REFRESH MATERIALIZED VIEW CONCURRENTLY popular_products;
```

### Optimizations Applied

1. **Caching**: Added Redis caching for search results
2. **Query Optimization**: 
   - Selected only needed columns
   - Used full-text search instead of LIKE
   - Added LIMIT to prevent excessive results
3. **Indexing**: Added appropriate indexes for search fields
4. **Materialized Views**: Created for common search patterns
5. **Separation of Concerns**: Split detailed product view from search results

## Interview Questions

### Question 1: How would you diagnose and fix a slow database query?

**Key Points to Address**:

1. **Diagnosis Steps**:
   - Use EXPLAIN (ANALYZE) to understand the query execution plan
   - Check for missing indexes or inefficient index usage
   - Look for full table scans, especially on large tables
   - Identify expensive operations (sorts, hash joins, etc.)
   - Check actual vs. estimated row counts

2. **Common Issues and Solutions**:
   - **Missing Indexes**: Add appropriate indexes for WHERE, JOIN, and ORDER BY clauses
   - **Inefficient Joins**: Ensure proper join conditions and join order
   - **Suboptimal Query Structure**: Rewrite to avoid correlated subqueries or excessive joins
   - **Data Skew**: Update statistics or consider partitioning
   - **Inefficient Functions**: Avoid functions on indexed columns in WHERE clauses

3. **Monitoring and Tools**:
   - Slow query logs
   - Performance schema or system catalogs
   - Query analyzers and visualization tools
   - Database monitoring solutions

### Question 2: What strategies would you use to scale a database for high read and write loads?

**Key Points**:

1. **Read Scaling**:
   - Read replicas for distributing read queries
   - Caching frequently accessed data
   - Materialized views for complex queries
   - Content delivery networks for static content
   - Database query result caching

2. **Write Scaling**:
   - Vertical scaling (more powerful database server)
   - Sharding data across multiple databases
   - Write-behind caching
   - Batch processing for bulk operations
   - Command-query responsibility segregation (CQRS)

3. **General Scaling Techniques**:
   - Connection pooling optimization
   - Database parameter tuning
   - Schema optimization
   - Asynchronous processing for non-critical operations
   - Microservices with dedicated databases

4. **Specific Database Technologies**:
   - NoSQL databases for specific workloads
   - Distributed databases (e.g., Cassandra, CockroachDB)
   - Time-series databases for metrics and logs
   - In-memory databases for ultra-low latency

### Question 3: Explain the tradeoffs between different database indexing strategies.

**Key Points**:

1. **B-tree Indexes**:
   - **Pros**: Good for range queries and equality, works with most data types
   - **Cons**: Slower for writes, larger storage overhead
   - **Best for**: General-purpose indexing, ordered data access

2. **Hash Indexes**:
   - **Pros**: Very fast for equality lookups, smaller than B-trees
   - **Cons**: Useless for range queries, no ordering
   - **Best for**: Exact match lookups, caching layers

3. **Bitmap Indexes**:
   - **Pros**: Efficient for low-cardinality columns, good for AND/OR operations
   - **Cons**: High maintenance cost for high-cardinality or frequently updated data
   - **Best for**: Data warehousing, columns with few distinct values

4. **Full-Text Indexes**:
   - **Pros**: Optimized for text search, supports relevance ranking
   - **Cons**: Higher storage and maintenance overhead
   - **Best for**: Search functionality, document databases

5. **Spatial Indexes**:
   - **Pros**: Efficient for geographic and multidimensional data
   - **Cons**: Specialized use case, complex implementation
   - **Best for**: Geographic information systems, location-based services

## Next Steps

In the next section, we'll explore [Network Optimization](05_Network_Optimization.md), which focuses on improving the performance of network communications, another critical aspect of system performance.

## Resources

1. [Use the Index, Luke!](https://use-the-index-luke.com/) - A guide to database performance for developers
2. [High Performance MySQL](https://www.oreilly.com/library/view/high-performance-mysql/9781492080503/) by Baron Schwartz, Peter Zaitsev, and Vadim Tkachenko
3. [PostgreSQL Documentation: Performance Tips](https://www.postgresql.org/docs/current/performance-tips.html)
4. [SQL Performance Explained](https://sql-performance-explained.com/) by Markus Winand
5. [Database Internals](https://www.databass.dev/) by Alex Petrov
