# Network Optimization

## Introduction

Network performance is often a critical factor in overall system performance, especially for distributed applications. This section covers essential techniques for optimizing network communications to reduce latency, increase throughput, and improve reliability.

## Key Network Performance Metrics

### 1. Latency

**Definition**: The time it takes for data to travel from source to destination.

**Key Metrics**:
- Round-trip time (RTT)
- Time to first byte (TTFB)
- DNS lookup time
- Connection establishment time

**Measurement Tools**:
- `ping` for basic RTT
- `traceroute` for path analysis
- Browser developer tools for web requests

### 2. Throughput

**Definition**: The amount of data transferred per unit of time.

**Key Metrics**:
- Bandwidth (maximum theoretical throughput)
- Actual data transfer rate
- Packets per second

**Measurement Tools**:
- `iperf` for network throughput testing
- `netstat` for connection statistics
- Browser network panels for HTTP requests

### 3. Reliability

**Definition**: The consistency and dependability of network communications.

**Key Metrics**:
- Packet loss rate
- Error rate
- Retransmission rate
- Connection stability

**Measurement Tools**:
- `mtr` (My Traceroute) for combined ping and traceroute
- `tcpdump` for packet analysis
- Network monitoring systems

## Content Delivery Optimization

### 1. Content Delivery Networks (CDNs)

CDNs distribute content across multiple geographically dispersed servers to reduce latency and increase availability.

**Key Benefits**:
- Reduced latency by serving content from edge locations
- Increased reliability through redundancy
- Protection against traffic spikes and DDoS attacks
- Offloading of origin servers

**Implementation Example**:
```html
<!-- Before: Serving assets from origin server -->
<img src="https://myapp.com/images/logo.png">
<script src="https://myapp.com/js/app.js"></script>

<!-- After: Serving assets from CDN -->
<img src="https://cdn.myapp.com/images/logo.png">
<script src="https://cdn.myapp.com/js/app.js"></script>
```

**Best Practices**:
- Use CDNs for static assets (images, CSS, JavaScript)
- Configure appropriate cache headers
- Use versioned URLs for cache busting
- Consider multi-CDN strategies for critical applications

### 2. Compression

Compression reduces the size of data transmitted over the network.

**Common Compression Techniques**:
- **HTTP Compression** (gzip, Brotli) for text-based content
- **Image Optimization** (WebP, AVIF, responsive images)
- **Video Compression** (adaptive bitrate streaming)
- **Minification** for JavaScript, CSS, and HTML

**HTTP Compression Example**:
```javascript
// Node.js Express server with compression
const express = require('express');
const compression = require('compression');
const app = express();

// Enable compression
app.use(compression({
  // Compress all responses over 1KB
  threshold: 1024,
  // Only compress text-based content types
  filter: (req, res) => {
    const contentType = res.getHeader('Content-Type');
    return /text|json|javascript|css/.test(contentType);
  }
}));

app.listen(3000);
```

**Image Optimization Example**:
```html
<!-- Responsive images with WebP support -->
<picture>
  <source srcset="image.webp" type="image/webp">
  <source srcset="image.jpg" type="image/jpeg">
  <img src="image.jpg" alt="Description" loading="lazy">
</picture>
```

## Protocol Optimization

### 1. HTTP/2 and HTTP/3

Modern HTTP protocols offer significant performance improvements over HTTP/1.1.

**HTTP/2 Benefits**:
- Multiplexing (multiple requests over single connection)
- Header compression
- Server push
- Binary protocol instead of text

**HTTP/3 Benefits**:
- Built on QUIC protocol using UDP
- Improved connection establishment
- Better performance on unreliable networks
- Reduced head-of-line blocking

**Implementation Example (Nginx HTTP/2)**:
```nginx
server {
    listen 443 ssl http2;
    server_name example.com;

    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;

    # HTTP/2 specific optimizations
    http2_push_preload on;
    http2_max_concurrent_streams 128;
}
```

### 2. WebSockets and Server-Sent Events

For real-time applications, specialized protocols can be more efficient than HTTP polling.

**WebSockets**:
- Full-duplex communication
- Persistent connection
- Low overhead after initial handshake

**Server-Sent Events (SSE)**:
- Server-to-client only
- Built on HTTP
- Automatic reconnection

**WebSocket Example (Node.js)**:
```javascript
// Server
const WebSocket = require('ws');
const wss = new WebSocket.Server({ port: 8080 });

wss.on('connection', (ws) => {
  ws.on('message', (message) => {
    console.log('Received:', message);
    // Echo back to client
    ws.send(`Echo: ${message}`);
  });
});

// Client
const socket = new WebSocket('ws://localhost:8080');

socket.onopen = () => {
  socket.send('Hello Server!');
};

socket.onmessage = (event) => {
  console.log('Message from server:', event.data);
};
```

## Request Optimization

### 1. Request Batching and Bundling

Combining multiple requests into a single request reduces overhead.

**API Batching Example**:
```javascript
// Instead of multiple individual requests
fetch('/api/users/1')
  .then(response => response.json())
  .then(user => console.log(user));

fetch('/api/users/2')
  .then(response => response.json())
  .then(user => console.log(user));

// Batch multiple requests into one
fetch('/api/users?ids=1,2,3')
  .then(response => response.json())
  .then(users => console.log(users));
```

**Asset Bundling Example**:
```javascript
// Before: Multiple script requests
<script src="/js/utils.js"></script>
<script src="/js/components.js"></script>
<script src="/js/app.js"></script>

// After: Single bundled request
<script src="/js/bundle.js"></script>
```

### 2. Lazy Loading and Progressive Loading

Loading resources only when needed improves initial load times.

**Lazy Loading Example**:
```javascript
// Lazy load images
document.addEventListener('DOMContentLoaded', () => {
  const lazyImages = document.querySelectorAll('img[data-src]');
  
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const img = entry.target;
        img.src = img.dataset.src;
        observer.unobserve(img);
      }
    });
  });
  
  lazyImages.forEach(img => observer.observe(img));
});
```

**Code Splitting Example (React)**:
```javascript
// Instead of importing everything upfront
import LargeComponent from './LargeComponent';

// Use dynamic imports for code splitting
const LargeComponent = React.lazy(() => import('./LargeComponent'));

function MyComponent() {
  return (
    <React.Suspense fallback={<div>Loading...</div>}>
      <LargeComponent />
    </React.Suspense>
  );
}
```

## Caching Strategies

### 1. Browser Caching

Proper HTTP cache headers can significantly reduce network requests.

**Cache-Control Header Example**:
```javascript
// Express.js example
app.use(express.static('public', {
  maxAge: '1d',  // Cache for 1 day
  setHeaders: (res, path) => {
    if (path.endsWith('.html')) {
      // Don't cache HTML files
      res.setHeader('Cache-Control', 'no-cache');
    } else if (path.match(/\.(js|css|jpg|png|webp)$/)) {
      // Cache immutable assets for 1 year
      res.setHeader('Cache-Control', 'public, max-age=31536000, immutable');
    }
  }
}));
```

**ETag Example**:
```javascript
const express = require('express');
const app = express();

app.get('/api/data', (req, res) => {
  const data = { /* ... */ };
  const etag = computeETag(data);
  
  // Check if client has current version
  if (req.headers['if-none-match'] === etag) {
    return res.status(304).end(); // Not Modified
  }
  
  res.setHeader('ETag', etag);
  res.json(data);
});
```

### 2. Service Workers

Service workers enable advanced caching strategies and offline functionality.

**Basic Service Worker Example**:
```javascript
// Register service worker
if ('serviceWorker' in navigator) {
  navigator.serviceWorker.register('/sw.js')
    .then(registration => console.log('SW registered:', registration))
    .catch(error => console.log('SW registration failed:', error));
}

// Service worker (sw.js)
const CACHE_NAME = 'my-site-cache-v1';
const urlsToCache = [
  '/',
  '/styles/main.css',
  '/scripts/main.js',
  '/images/logo.png'
];

self.addEventListener('install', event => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(cache => cache.addAll(urlsToCache))
  );
});

self.addEventListener('fetch', event => {
  event.respondWith(
    caches.match(event.request)
      .then(response => {
        // Cache hit - return response
        if (response) {
          return response;
        }
        return fetch(event.request);
      }
    )
  );
});
```

## Connection Optimization

### 1. Connection Pooling

Reusing connections reduces the overhead of establishing new connections.

**HTTP Client Connection Pooling Example (Java)**:
```java
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;

public class HttpClientExample {
    public static CloseableHttpClient createHttpClient() {
        PoolingHttpClientConnectionManager cm = new PoolingHttpClientConnectionManager();
        
        // Maximum total connections
        cm.setMaxTotal(100);
        
        // Maximum connections per route
        cm.setDefaultMaxPerRoute(20);
        
        return HttpClients.custom()
                .setConnectionManager(cm)
                .build();
    }
}
```

### 2. Connection Keep-Alive

Keeping connections open for reuse improves performance for multiple requests.

**HTTP Keep-Alive Example (Node.js)**:
```javascript
const http = require('http');
const agent = new http.Agent({
  keepAlive: true,
  maxSockets: 50,
  keepAliveMsecs: 30000 // 30 seconds
});

function makeRequest() {
  const options = {
    hostname: 'api.example.com',
    port: 80,
    path: '/data',
    method: 'GET',
    agent: agent
  };
  
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => resolve(data));
    });
    
    req.on('error', (e) => reject(e));
    req.end();
  });
}
```

## Mobile and Low-Bandwidth Optimization

### 1. Responsive Design and Adaptive Serving

Delivering optimized content based on device capabilities and network conditions.

**Responsive Images Example**:
```html
<img 
  srcset="
    image-small.jpg 400w,
    image-medium.jpg 800w,
    image-large.jpg 1200w
  "
  sizes="(max-width: 600px) 400px, (max-width: 1200px) 800px, 1200px"
  src="image-medium.jpg"
  alt="Responsive image"
>
```

**Network Information API Example**:
```javascript
if ('connection' in navigator) {
  const connection = navigator.connection;
  
  if (connection.saveData) {
    // User has requested reduced data usage
    loadLowResolutionImages();
  } else if (connection.effectiveType === '4g') {
    // Fast connection, load high-quality assets
    loadHighQualityAssets();
  } else {
    // Slower connection, load optimized assets
    loadOptimizedAssets();
  }
}
```

### 2. Data Saving Techniques

Minimizing data transfer for bandwidth-constrained environments.

**Techniques**:
- Text-only versions of content
- Reduced image quality
- Deferred loading of non-critical resources
- Offline-first approach with local storage

**Example (Progressive Enhancement)**:
```javascript
// Base functionality with minimal resources
const app = {
  init() {
    this.loadCoreContent();
    
    // Check if we can load enhanced features
    if (this.hasGoodConnection()) {
      this.loadEnhancedFeatures();
    }
    
    // Listen for connection changes
    if ('connection' in navigator) {
      navigator.connection.addEventListener('change', () => {
        if (this.hasGoodConnection()) {
          this.loadEnhancedFeatures();
        }
      });
    }
  },
  
  hasGoodConnection() {
    return !navigator.connection || 
           !navigator.connection.saveData && 
           (navigator.connection.effectiveType === '4g');
  },
  
  loadCoreContent() {
    // Load essential content and functionality
  },
  
  loadEnhancedFeatures() {
    // Load additional features, high-res images, etc.
  }
};

app.init();
```

## Interview Question

### How would you optimize a web application for network performance?

**Key Points to Address**:

1. **Content Delivery**:
   - Implement a CDN for static assets
   - Enable compression (gzip/Brotli)
   - Optimize images and media (WebP, responsive images)
   - Minify and bundle CSS/JavaScript

2. **Protocol Optimization**:
   - Upgrade to HTTP/2 or HTTP/3
   - Use WebSockets for real-time communication
   - Implement server push for critical resources

3. **Request Optimization**:
   - Reduce the number of HTTP requests
   - Implement API batching
   - Use GraphQL to request only needed data
   - Implement lazy loading for non-critical resources

4. **Caching Strategy**:
   - Set appropriate cache headers
   - Implement service workers for offline support
   - Use local storage for application state
   - Implement ETags for efficient validation

5. **Connection Management**:
   - Enable keep-alive connections
   - Implement connection pooling
   - Use DNS prefetching and preconnect hints
   - Consider HTTP/3 for improved connection handling

6. **Mobile Optimization**:
   - Implement responsive design
   - Adapt content based on network conditions
   - Reduce payload size for mobile users
   - Prioritize above-the-fold content

7. **Monitoring and Measurement**:
   - Implement real user monitoring (RUM)
   - Set up synthetic monitoring from multiple locations
   - Establish performance budgets
   - Regularly audit network performance

## Next Steps

This completes our exploration of Performance Optimization. The next module in our system design guide will cover API Design, including REST principles, GraphQL, versioning strategies, and authentication/authorization.

## Resources

1. [Web.dev Performance](https://web.dev/performance/) - Google's web performance guides
2. [High Performance Browser Networking](https://hpbn.co/) by Ilya Grigorik
3. [MDN HTTP](https://developer.mozilla.org/en-US/docs/Web/HTTP) - Mozilla's HTTP documentation
4. [Cloudflare Learning Center](https://www.cloudflare.com/learning/) - CDN and network optimization resources
5. [WebPageTest](https://www.webpagetest.org/) - Web performance testing tool
