# Algorithmic Optimization

## Introduction

Algorithmic optimization focuses on improving the efficiency of algorithms and data structures to reduce computational complexity and resource usage. Even with powerful hardware, inefficient algorithms can lead to poor performance, especially as data volumes grow.

## Understanding Algorithmic Complexity

### Time Complexity

Time complexity measures how the runtime of an algorithm grows as the input size increases.

**Common Complexity Classes**:
- **O(1)** - Constant time: Runtime doesn't depend on input size
- **O(log n)** - Logarithmic time: Runtime grows logarithmically with input size
- **O(n)** - Linear time: Runtime grows linearly with input size
- **O(n log n)** - Linearithmic time: Common for efficient sorting algorithms
- **O(n²)** - Quadratic time: Runtime grows with the square of input size
- **O(2ⁿ)** - Exponential time: Runtime doubles with each additional input element

**Complexity Comparison**:

| Input Size (n) | O(1) | O(log n) | O(n) | O(n log n) | O(n²) | O(2ⁿ) |
|----------------|------|----------|------|------------|-------|--------|
| 10             | 1    | 3        | 10   | 30         | 100   | 1024   |
| 100            | 1    | 7        | 100  | 700        | 10K   | 2¹⁰⁰   |
| 1,000          | 1    | 10       | 1K   | 10K        | 1M    | 2¹⁰⁰⁰  |
| 1,000,000      | 1    | 20       | 1M   | 20M        | 1T    | 2¹⁰⁰⁰⁰⁰⁰|

### Space Complexity

Space complexity measures how memory usage grows with input size.

**Key Considerations**:
- Auxiliary space (extra space used by the algorithm)
- Input space (space required to store the input)
- Total space (auxiliary + input)

## Core Optimization Strategies

### 1. Choose the Right Algorithm

Selecting an appropriate algorithm is often the most impactful optimization.

**Example: Finding an Element in a Collection**

```python
# O(n) - Linear search (unordered data)
def linear_search(arr, target):
    for i, item in enumerate(arr):
        if item == target:
            return i
    return -1

# O(log n) - Binary search (ordered data)
def binary_search(arr, target):
    left, right = 0, len(arr) - 1
    while left <= right:
        mid = (left + right) // 2
        if arr[mid] == target:
            return mid
        elif arr[mid] < target:
            left = mid + 1
        else:
            right = mid - 1
    return -1
```

**When to Use**:
- Linear search: Unordered data, small datasets
- Binary search: Ordered data, larger datasets

### 2. Use Appropriate Data Structures

Choosing the right data structure can dramatically improve performance.

**Common Data Structures and Their Strengths**:

| Operation | Array | Linked List | Hash Table | Binary Search Tree | Heap |
|-----------|-------|-------------|------------|-------------------|------|
| Access    | O(1)  | O(n)        | O(1)*      | O(log n)          | O(1) |
| Search    | O(n)  | O(n)        | O(1)*      | O(log n)          | O(n) |
| Insert    | O(n)  | O(1)        | O(1)*      | O(log n)          | O(log n) |
| Delete    | O(n)  | O(1)        | O(1)*      | O(log n)          | O(log n) |

*Average case for hash tables; worst case can be O(n)

**Example: Counting Word Frequencies**

```python
# Inefficient: Using list - O(n²)
def count_words_list(text):
    words = text.lower().split()
    result = []
    
    for word in words:
        found = False
        for entry in result:
            if entry[0] == word:
                entry[1] += 1
                found = True
                break
        if not found:
            result.append([word, 1])
    
    return result

# Efficient: Using dictionary (hash table) - O(n)
def count_words_dict(text):
    words = text.lower().split()
    result = {}
    
    for word in words:
        if word in result:
            result[word] += 1
        else:
            result[word] = 1
    
    return result
```

### 3. Avoid Redundant Computations

Eliminating repeated calculations can significantly improve performance.

**Techniques**:
- Caching/memoization
- Precomputation
- Early termination

**Example: Fibonacci with Memoization**

```python
# Inefficient: O(2ⁿ) - Exponential time
def fibonacci_recursive(n):
    if n <= 1:
        return n
    return fibonacci_recursive(n-1) + fibonacci_recursive(n-2)

# Efficient: O(n) - Linear time with memoization
def fibonacci_memoized(n, memo={}):
    if n in memo:
        return memo[n]
    if n <= 1:
        return n
    memo[n] = fibonacci_memoized(n-1, memo) + fibonacci_memoized(n-2, memo)
    return memo[n]
```

### 4. Optimize Loops

Loops often dominate execution time in algorithms.

**Optimization Techniques**:
- Reduce work inside loops
- Minimize loop iterations
- Unroll loops for performance
- Use appropriate loop constructs

**Example: Matrix Multiplication Optimization**

```java
// Less efficient: Poor cache locality
public static int[][] multiplyMatrices(int[][] A, int[][] B) {
    int n = A.length;
    int[][] C = new int[n][n];
    
    for (int i = 0; i < n; i++) {
        for (int j = 0; j < n; j++) {
            for (int k = 0; k < n; k++) {
                C[i][j] += A[i][k] * B[k][j];
            }
        }
    }
    
    return C;
}

// More efficient: Better cache locality
public static int[][] multiplyMatricesOptimized(int[][] A, int[][] B) {
    int n = A.length;
    int[][] C = new int[n][n];
    
    for (int i = 0; i < n; i++) {
        for (int k = 0; k < n; k++) {
            for (int j = 0; j < n; j++) {
                C[i][j] += A[i][k] * B[k][j];
            }
        }
    }
    
    return C;
}
```

## Common Algorithmic Optimizations

### 1. String Manipulation

String operations are often performance bottlenecks.

**Optimization Techniques**:
- Use StringBuilder/StringBuffer for concatenation
- Minimize string creation in loops
- Use efficient string matching algorithms

**Example: String Concatenation**

```java
// Inefficient: Creates many intermediate strings - O(n²)
String buildString(int n) {
    String result = "";
    for (int i = 0; i < n; i++) {
        result += "item" + i;
    }
    return result;
}

// Efficient: Uses StringBuilder - O(n)
String buildStringOptimized(int n) {
    StringBuilder result = new StringBuilder();
    for (int i = 0; i < n; i++) {
        result.append("item").append(i);
    }
    return result.toString();
}
```

### 2. Collection Processing

Efficient collection handling is crucial for data-intensive applications.

**Optimization Techniques**:
- Use bulk operations
- Filter early to reduce data volume
- Process streams efficiently
- Consider lazy evaluation

**Example: Java Stream Processing**

```java
// Less efficient: Multiple passes over the data
List<Integer> getEvenSquares(List<Integer> numbers) {
    List<Integer> filtered = new ArrayList<>();
    for (Integer num : numbers) {
        if (num % 2 == 0) {
            filtered.add(num);
        }
    }
    
    List<Integer> result = new ArrayList<>();
    for (Integer num : filtered) {
        result.add(num * num);
    }
    
    return result;
}

// More efficient: Single pass with streams
List<Integer> getEvenSquaresOptimized(List<Integer> numbers) {
    return numbers.stream()
                 .filter(num -> num % 2 == 0)
                 .map(num -> num * num)
                 .collect(Collectors.toList());
}
```

### 3. Sorting and Searching

Choosing the right sorting or searching algorithm can have a major impact.

**Sorting Algorithm Selection**:
- **Quick Sort**: General-purpose, in-place sorting - O(n log n) average
- **Merge Sort**: Stable sorting with guaranteed O(n log n) performance
- **Heap Sort**: In-place sorting with O(n log n) worst-case
- **Insertion Sort**: Efficient for small or nearly sorted data - O(n²)
- **Counting/Radix Sort**: Linear time for specific data types - O(n)

**Example: Choosing the Right Sort**

```python
# For small arrays or nearly sorted data
def insertion_sort(arr):
    for i in range(1, len(arr)):
        key = arr[i]
        j = i - 1
        while j >= 0 and arr[j] > key:
            arr[j + 1] = arr[j]
            j -= 1
        arr[j + 1] = key
    return arr

# For general-purpose sorting
def quick_sort(arr):
    if len(arr) <= 1:
        return arr
    pivot = arr[len(arr) // 2]
    left = [x for x in arr if x < pivot]
    middle = [x for x in arr if x == pivot]
    right = [x for x in arr if x > pivot]
    return quick_sort(left) + middle + quick_sort(right)

# For integer data with limited range
def counting_sort(arr, max_val):
    count = [0] * (max_val + 1)
    for num in arr:
        count[num] += 1
    
    sorted_arr = []
    for i in range(max_val + 1):
        sorted_arr.extend([i] * count[i])
    
    return sorted_arr
```

## Space-Time Tradeoffs

Many optimizations involve trading memory for speed or vice versa.

### 1. Caching and Memoization

Storing results of expensive computations for reuse.

**Example: API Response Caching**

```python
# Simple in-memory cache
cache = {}

def get_user_data(user_id):
    # Check if result is in cache
    if user_id in cache:
        return cache[user_id]
    
    # Expensive operation (e.g., database query or API call)
    result = fetch_user_from_database(user_id)
    
    # Store in cache for future use
    cache[user_id] = result
    
    return result
```

### 2. Precomputation

Computing values in advance to avoid runtime calculations.

**Example: Precomputed Lookup Table**

```java
public class TrigTable {
    private static final int TABLE_SIZE = 360;
    private static final double[] SIN_TABLE = new double[TABLE_SIZE];
    private static final double[] COS_TABLE = new double[TABLE_SIZE];
    
    static {
        // Precompute values
        for (int i = 0; i < TABLE_SIZE; i++) {
            double radians = Math.toRadians(i);
            SIN_TABLE[i] = Math.sin(radians);
            COS_TABLE[i] = Math.cos(radians);
        }
    }
    
    // Fast lookup instead of calculation
    public static double sin(int degrees) {
        return SIN_TABLE[degrees % TABLE_SIZE];
    }
    
    public static double cos(int degrees) {
        return COS_TABLE[degrees % TABLE_SIZE];
    }
}
```

## Practical Example: Optimizing a Search Function

Let's optimize a function that searches for products matching certain criteria.

### Initial Implementation

```java
// Initial implementation - inefficient
public List<Product> findProducts(List<Product> allProducts, 
                                 String nameFilter, 
                                 double minPrice, 
                                 double maxPrice, 
                                 List<String> categories) {
    List<Product> result = new ArrayList<>();
    
    for (Product product : allProducts) {
        // Check name filter
        boolean nameMatches = nameFilter == null || 
                             nameFilter.isEmpty() || 
                             product.getName().toLowerCase().contains(nameFilter.toLowerCase());
        
        // Check price range
        boolean priceMatches = product.getPrice() >= minPrice && 
                              product.getPrice() <= maxPrice;
        
        // Check categories
        boolean categoryMatches = categories == null || 
                                 categories.isEmpty() || 
                                 categories.contains(product.getCategory());
        
        // Add product if all criteria match
        if (nameMatches && priceMatches && categoryMatches) {
            result.add(product);
        }
    }
    
    return result;
}
```

### Optimized Implementation

```java
// Optimized implementation
public List<Product> findProductsOptimized(List<Product> allProducts, 
                                          String nameFilter, 
                                          double minPrice, 
                                          double maxPrice, 
                                          List<String> categories) {
    // Early return for empty input
    if (allProducts == null || allProducts.isEmpty()) {
        return Collections.emptyList();
    }
    
    // Prepare filters for efficiency
    final String nameLower = nameFilter == null ? null : nameFilter.toLowerCase();
    final Set<String> categorySet = categories == null ? null : 
                                   new HashSet<>(categories);
    
    // Use stream for efficient filtering
    return allProducts.stream()
        // Apply most restrictive filters first for short-circuiting
        .filter(p -> minPrice <= p.getPrice() && p.getPrice() <= maxPrice)
        .filter(p -> nameLower == null || nameLower.isEmpty() || 
                    p.getName().toLowerCase().contains(nameLower))
        .filter(p -> categorySet == null || categorySet.isEmpty() || 
                    categorySet.contains(p.getCategory()))
        .collect(Collectors.toList());
}
```

### Optimizations Applied

1. **Early termination** for empty input
2. **Preprocessing** filters before the loop
3. **Using a HashSet** for O(1) category lookups
4. **Ordering filters** by restrictiveness
5. **Stream processing** for cleaner code and potential parallelization

## Interview Questions

### Question 1: How would you optimize an algorithm that frequently searches for items in a large collection?

**Key Points to Address**:

1. **Data Structure Selection**:
   - Use hash tables (HashSet/HashMap) for O(1) lookups when exact matches are needed
   - Use balanced trees (TreeSet/TreeMap) for O(log n) lookups with ordering
   - Consider specialized structures like Bloom filters for membership testing

2. **Indexing Strategies**:
   - Create indexes on frequently searched fields
   - Use multi-level indexing for composite searches
   - Consider inverted indexes for text search

3. **Caching**:
   - Cache frequent search results
   - Implement LRU or other eviction policies
   - Consider distributed caching for scale

4. **Search Algorithm Selection**:
   - Binary search for sorted data
   - Trie structures for prefix searches
   - Approximate algorithms for fuzzy matching

5. **Preprocessing**:
   - Sort data if multiple binary searches will be performed
   - Precompute derived values used in search criteria
   - Denormalize data to avoid joins during search

### Question 2: Explain the tradeoffs between different sorting algorithms and when you would choose each one.

**Key Points**:

1. **Quick Sort**:
   - **Pros**: Fast average case O(n log n), in-place sorting
   - **Cons**: Worst case O(n²), not stable
   - **When to use**: General-purpose sorting, especially when space is a concern

2. **Merge Sort**:
   - **Pros**: Guaranteed O(n log n) performance, stable sort
   - **Cons**: Requires O(n) extra space
   - **When to use**: When stability is required, linked lists, external sorting

3. **Heap Sort**:
   - **Pros**: O(n log n) worst-case, in-place sorting
   - **Cons**: Slower than Quick Sort in practice, not stable
   - **When to use**: When guaranteed O(n log n) performance is needed with limited space

4. **Insertion Sort**:
   - **Pros**: Simple implementation, efficient for small datasets, O(n) for nearly sorted data
   - **Cons**: O(n²) average and worst case
   - **When to use**: Small arrays (n < 20), nearly sorted data, as part of hybrid algorithms

5. **Counting/Radix Sort**:
   - **Pros**: O(n) time complexity for specific data types
   - **Cons**: Limited to integers or strings, uses extra space
   - **When to use**: When data has a limited range of values

### Question 3: How would you optimize a function that needs to perform expensive calculations repeatedly with the same inputs?

**Key Points**:

1. **Memoization**:
   - Cache results of function calls based on input parameters
   - Implement with a hash map or similar structure
   - Consider cache size limits and eviction policies

2. **Implementation Approaches**:
   - Function wrappers that add caching behavior
   - Decorators in languages that support them
   - Built-in memoization libraries

3. **Considerations**:
   - Memory usage vs. computation time tradeoff
   - Cache invalidation strategy for changing data
   - Thread safety for concurrent access

4. **Example Implementation**:
   ```python
   def memoize(func):
       cache = {}
       def wrapper(*args):
           if args not in cache:
               cache[args] = func(*args)
           return cache[args]
       return wrapper
   
   @memoize
   def expensive_calculation(x, y):
       # Expensive operation here
       return x * y
   ```

## Next Steps

In the next section, we'll explore [Database Optimization](04_Database_Optimization.md), which focuses on improving the performance of database operations, a common bottleneck in many applications.

## Resources

1. [Introduction to Algorithms](https://mitpress.mit.edu/books/introduction-algorithms-third-edition) by Cormen, Leiserson, Rivest, and Stein
2. [Effective Java](https://www.oreilly.com/library/view/effective-java-3rd/9780134686097/) by Joshua Bloch
3. [High Performance Python](https://www.oreilly.com/library/view/high-performance-python/9781492055013/) by Micha Gorelick and Ian Ozsvald
4. [Algorithm Design Manual](http://www.algorist.com/) by Steven Skiena
