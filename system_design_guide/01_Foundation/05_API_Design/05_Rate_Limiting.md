# Rate Limiting and Throttling

## Introduction

Rate limiting and throttling are essential techniques for protecting APIs from abuse, ensuring fair usage, and maintaining service availability. These mechanisms control how many requests clients can make to an API within a specific time period, preventing any single client from overwhelming the system.

## Why Implement Rate Limiting?

Rate limiting serves several important purposes:

1. **Prevent Abuse**: Protect against malicious attacks like brute force or denial of service
2. **Ensure Fair Usage**: Prevent any single client from consuming excessive resources
3. **Control Costs**: Manage infrastructure expenses by limiting traffic
4. **Improve Reliability**: Maintain service availability during traffic spikes
5. **Meet Dependencies' Limits**: Stay within the constraints of downstream services

## Rate Limiting Algorithms

### 1. Fixed Window

The simplest approach, which counts requests in fixed time intervals (e.g., 100 requests per minute).

**How It Works**:
- Divide time into fixed windows (e.g., 1-minute intervals)
- Count requests in the current window
- Reset the counter at the start of each new window

**Implementation Example**:
```javascript
class FixedWindowRateLimiter {
  constructor(limit, windowMs) {
    this.limit = limit;
    this.windowMs = windowMs;
    this.clients = new Map(); // client_id -> {count, windowStart}
  }
  
  isAllowed(clientId) {
    const now = Date.now();
    
    if (!this.clients.has(clientId)) {
      // First request from this client
      this.clients.set(clientId, {
        count: 1,
        windowStart: now
      });
      return true;
    }
    
    const client = this.clients.get(clientId);
    
    // Check if we're in a new window
    if (now - client.windowStart >= this.windowMs) {
      // Reset for new window
      client.count = 1;
      client.windowStart = now;
      return true;
    }
    
    // Check if client is within limit
    if (client.count < this.limit) {
      client.count++;
      return true;
    }
    
    return false; // Rate limit exceeded
  }
}

// Usage
const rateLimiter = new FixedWindowRateLimiter(100, 60 * 1000); // 100 requests per minute

function handleRequest(req, res) {
  const clientId = req.ip; // Or use API key, user ID, etc.
  
  if (rateLimiter.isAllowed(clientId)) {
    // Process the request
    res.send('Request processed');
  } else {
    res.status(429).send('Too Many Requests');
  }
}
```

**Pros**:
- Simple to understand and implement
- Low memory usage
- Works well for most APIs

**Cons**:
- Can allow bursts at window boundaries (e.g., 100 requests at the end of one window and 100 more at the start of the next)
- Not ideal for strict rate control

### 2. Sliding Window

An improvement over fixed window that provides smoother rate limiting by considering a moving time frame.

**How It Works**:
- Track requests in the current window and the previous window
- Calculate a weighted rate based on how much of the previous window overlaps with the sliding window
- Allow requests if this calculated rate is below the limit

**Implementation Example**:
```javascript
class SlidingWindowRateLimiter {
  constructor(limit, windowMs) {
    this.limit = limit;
    this.windowMs = windowMs;
    this.clients = new Map(); // client_id -> {currentCount, previousCount, windowStart}
  }
  
  isAllowed(clientId) {
    const now = Date.now();
    
    if (!this.clients.has(clientId)) {
      // First request from this client
      this.clients.set(clientId, {
        currentCount: 1,
        previousCount: 0,
        windowStart: now
      });
      return true;
    }
    
    const client = this.clients.get(clientId);
    const windowElapsed = now - client.windowStart;
    
    // Check if we're in a new window
    if (windowElapsed >= this.windowMs) {
      // Shift to new window
      client.previousCount = client.currentCount;
      client.currentCount = 1;
      client.windowStart = now;
      return true;
    }
    
    // Calculate the weighted rate
    const weightPrevious = Math.max(0, (this.windowMs - windowElapsed) / this.windowMs);
    const weightedCount = client.previousCount * weightPrevious + client.currentCount;
    
    // Check if client is within limit
    if (weightedCount < this.limit) {
      client.currentCount++;
      return true;
    }
    
    return false; // Rate limit exceeded
  }
}
```

**Pros**:
- Smoother rate limiting without sharp spikes at window boundaries
- More accurate rate control
- Still relatively simple to implement

**Cons**:
- Slightly higher computational overhead
- More complex to understand

### 3. Token Bucket

A flexible algorithm that allows for bursts of traffic while maintaining a long-term rate limit.

**How It Works**:
- Each client has a bucket that holds tokens
- Tokens are added to the bucket at a fixed rate (the refill rate)
- Each request consumes one or more tokens
- Requests are allowed if enough tokens are available
- The bucket has a maximum capacity to limit burst size

**Implementation Example**:
```javascript
class TokenBucketRateLimiter {
  constructor(bucketSize, refillRate) {
    this.bucketSize = bucketSize;      // Maximum tokens
    this.refillRate = refillRate;      // Tokens per millisecond
    this.clients = new Map();          // client_id -> {tokens, lastRefill}
  }
  
  isAllowed(clientId, tokensRequired = 1) {
    const now = Date.now();
    
    if (!this.clients.has(clientId)) {
      // First request from this client, initialize with a full bucket
      this.clients.set(clientId, {
        tokens: this.bucketSize - tokensRequired,
        lastRefill: now
      });
      return true;
    }
    
    const client = this.clients.get(clientId);
    
    // Refill tokens based on time elapsed
    const timeElapsed = now - client.lastRefill;
    const tokensToAdd = timeElapsed * this.refillRate;
    
    client.tokens = Math.min(this.bucketSize, client.tokens + tokensToAdd);
    client.lastRefill = now;
    
    // Check if enough tokens are available
    if (client.tokens >= tokensRequired) {
      client.tokens -= tokensRequired;
      return true;
    }
    
    return false; // Rate limit exceeded
  }
}

// Usage
// Allow 10 requests initially and 1 request per second thereafter
const rateLimiter = new TokenBucketRateLimiter(10, 0.001); // 0.001 tokens/ms = 1 token/second
```

**Pros**:
- Allows for controlled bursts of traffic
- Provides smooth rate limiting over time
- Flexible for different types of requests (can consume different numbers of tokens)

**Cons**:
- More complex to implement
- Higher memory usage per client

### 4. Leaky Bucket

Similar to token bucket but with a constant outflow rate, which smooths out traffic.

**How It Works**:
- Requests enter a queue (the bucket)
- Requests are processed at a constant rate
- If the bucket is full, new requests are rejected
- The bucket has a maximum capacity to limit queue size

**Implementation Example**:
```javascript
class LeakyBucketRateLimiter {
  constructor(capacity, leakRate) {
    this.capacity = capacity;        // Maximum queue size
    this.leakRate = leakRate;        // Requests processed per millisecond
    this.clients = new Map();        // client_id -> {queue, lastLeakTime}
  }
  
  isAllowed(clientId) {
    const now = Date.now();
    
    if (!this.clients.has(clientId)) {
      // First request from this client
      this.clients.set(clientId, {
        queue: 1,
        lastLeakTime: now
      });
      return true;
    }
    
    const client = this.clients.get(clientId);
    
    // Calculate leakage based on time elapsed
    const timeElapsed = now - client.lastLeakTime;
    const leakedRequests = timeElapsed * this.leakRate;
    
    client.queue = Math.max(0, client.queue - leakedRequests);
    client.lastLeakTime = now;
    
    // Check if there's room in the bucket
    if (client.queue < this.capacity) {
      client.queue++;
      return true;
    }
    
    return false; // Rate limit exceeded
  }
}

// Usage
// Allow 5 requests in queue and process 2 requests per second
const rateLimiter = new LeakyBucketRateLimiter(5, 0.002); // 0.002 requests/ms = 2 requests/second
```

**Pros**:
- Smooths out traffic to a constant rate
- Good for protecting downstream services that need consistent load
- Can act as a queue for requests

**Cons**:
- May introduce latency as requests wait in the queue
- More complex to implement correctly
- Higher memory usage per client

## Client Identification Strategies

To apply rate limits, you need to identify clients consistently. Common approaches include:

### 1. IP Address

**Implementation**:
```javascript
function getClientIdentifier(req) {
  return req.ip;
}
```

**Considerations**:
- Simple to implement
- May block legitimate users behind shared IPs (NAT, proxies)
- Can be circumvented by changing IP addresses

### 2. API Keys

**Implementation**:
```javascript
function getClientIdentifier(req) {
  return req.header('X-API-Key') || 'anonymous';
}
```

**Considerations**:
- More accurate client identification
- Requires authentication system
- Better for B2B APIs

### 3. User ID

**Implementation**:
```javascript
function getClientIdentifier(req) {
  return req.user ? req.user.id : req.ip;
}
```

**Considerations**:
- Accurate for authenticated users
- Requires authentication
- Can fall back to IP for unauthenticated requests

### 4. Combined Identifiers

**Implementation**:
```javascript
function getClientIdentifier(req) {
  const userId = req.user ? req.user.id : 'anonymous';
  const apiKey = req.header('X-API-Key') || 'none';
  return `${userId}:${apiKey}:${req.ip}`;
}
```

**Considerations**:
- More granular control
- Higher storage requirements
- Can handle complex scenarios

## Response Headers and Error Handling

Properly communicating rate limits to clients helps them adapt their behavior.

### Standard Rate Limit Headers

```javascript
function setRateLimitHeaders(res, limit, remaining, reset) {
  res.set('X-RateLimit-Limit', limit.toString());
  res.set('X-RateLimit-Remaining', remaining.toString());
  res.set('X-RateLimit-Reset', reset.toString());
}

// When rate limited
function handleRateLimited(req, res) {
  const resetTime = Math.ceil(getResetTimeForClient(req.clientId) / 1000);
  
  setRateLimitHeaders(res, 100, 0, resetTime);
  
  // Add Retry-After header (in seconds)
  res.set('Retry-After', Math.max(1, resetTime - Math.floor(Date.now() / 1000)));
  
  return res.status(429).json({
    error: 'Too Many Requests',
    message: 'You have exceeded the rate limit. Please try again later.',
    retry_after: resetTime - Math.floor(Date.now() / 1000)
  });
}
```

## Distributed Rate Limiting

In a distributed system with multiple API servers, rate limiting requires coordination.

### Using Redis for Distributed Rate Limiting

```javascript
const redis = require('redis');
const { promisify } = require('util');
const client = redis.createClient(process.env.REDIS_URL);

// Promisify Redis commands
const incrAsync = promisify(client.incr).bind(client);
const expireAsync = promisify(client.expire).bind(client);
const ttlAsync = promisify(client.ttl).bind(client);

async function isAllowed(clientId, limit, windowSeconds) {
  const key = `ratelimit:${clientId}:${Math.floor(Date.now() / (windowSeconds * 1000))}`;
  
  try {
    // Increment counter for this window
    const count = await incrAsync(key);
    
    // Set expiration if this is a new key
    if (count === 1) {
      await expireAsync(key, windowSeconds);
    }
    
    // Get remaining TTL
    const ttl = await ttlAsync(key);
    
    // Set rate limit headers
    setRateLimitHeaders(res, limit, Math.max(0, limit - count), Math.floor(Date.now() / 1000) + ttl);
    
    // Check if client is within limit
    return count <= limit;
  } catch (err) {
    console.error('Redis error:', err);
    // Fail open or closed depending on your policy
    return true; // Fail open in this example
  }
}

// Express middleware
function rateLimitMiddleware(limit, windowSeconds) {
  return async (req, res, next) => {
    const clientId = getClientIdentifier(req);
    
    if (await isAllowed(clientId, limit, windowSeconds)) {
      next();
    } else {
      handleRateLimited(req, res);
    }
  };
}

// Apply middleware to routes
app.get('/api/public', rateLimitMiddleware(100, 60), (req, res) => {
  res.json({ message: 'Public API response' });
});

app.get('/api/premium', rateLimitMiddleware(1000, 60), (req, res) => {
  res.json({ message: 'Premium API response' });
});
```

## Tiered Rate Limiting

Different clients may have different rate limits based on their tier or subscription.

**Implementation Example**:
```javascript
// Define rate limit tiers
const rateLimitTiers = {
  'free': { limit: 100, window: 3600 },    // 100 requests per hour
  'basic': { limit: 1000, window: 3600 },  // 1000 requests per hour
  'premium': { limit: 10000, window: 3600 } // 10000 requests per hour
};

// Middleware that applies tier-specific rate limits
function tieredRateLimitMiddleware() {
  return async (req, res, next) => {
    // Get client identifier
    const clientId = getClientIdentifier(req);
    
    // Determine client's tier (from database, JWT claim, etc.)
    const tier = await getClientTier(clientId) || 'free';
    
    // Get rate limit config for this tier
    const { limit, window } = rateLimitTiers[tier];
    
    // Check rate limit
    if (await isAllowed(clientId, limit, window)) {
      next();
    } else {
      handleRateLimited(req, res);
    }
  };
}

// Apply middleware to routes
app.use('/api', tieredRateLimitMiddleware());
```

## Implementing Backpressure

Backpressure is a technique to handle overload by pushing back on clients when the system is under stress.

**Implementation Example**:
```javascript
function dynamicRateLimitMiddleware() {
  return (req, res, next) => {
    // Get system metrics
    const currentLoad = getSystemLoad();
    const memoryUsage = getMemoryUsage();
    
    // Adjust rate limits based on system health
    let effectiveLimit = BASE_RATE_LIMIT;
    
    if (currentLoad > 0.8) {
      // Reduce limit by 50% when CPU load is high
      effectiveLimit = Math.floor(effectiveLimit * 0.5);
    }
    
    if (memoryUsage > 0.9) {
      // Reduce limit by 75% when memory usage is very high
      effectiveLimit = Math.floor(effectiveLimit * 0.25);
    }
    
    // Apply the dynamic rate limit
    if (isAllowed(getClientIdentifier(req), effectiveLimit, RATE_LIMIT_WINDOW)) {
      next();
    } else {
      handleRateLimited(req, res);
    }
  };
}
```

## Best Practices

1. **Be Transparent**: Clearly document your rate limits and communicate them via response headers
2. **Graceful Degradation**: Under heavy load, consider serving cached or simplified responses rather than rejecting requests
3. **Different Limits for Different Endpoints**: Apply stricter limits to resource-intensive operations
4. **Gradual Escalation**: Implement progressive penalties for repeated abuse
5. **Monitor and Adjust**: Regularly review rate limit effectiveness and adjust as needed

## Practical Example: Express.js Rate Limiting

Here's a complete example using the popular `express-rate-limit` package:

```javascript
const express = require('express');
const rateLimit = require('express-rate-limit');
const RedisStore = require('rate-limit-redis');
const Redis = require('ioredis');

const app = express();
const redis = new Redis(process.env.REDIS_URL);

// Create a rate limiter with Redis store
const apiLimiter = rateLimit({
  store: new RedisStore({
    client: redis,
    prefix: 'rate-limit:',
    // Use a more precise expiry for sliding windows
    expiry: 60, // 1 minute in seconds
  }),
  windowMs: 60 * 1000, // 1 minute
  max: 100, // limit each IP to 100 requests per windowMs
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
  keyGenerator: (req) => {
    // Use API key if available, otherwise IP
    return req.headers['x-api-key'] || req.ip;
  },
  handler: (req, res) => {
    res.status(429).json({
      error: 'Too Many Requests',
      message: 'You have exceeded the rate limit. Please try again later.'
    });
  }
});

// Apply rate limiting to all API routes
app.use('/api/', apiLimiter);

// Different limit for authentication endpoints
const authLimiter = rateLimit({
  store: new RedisStore({ client: redis, prefix: 'auth-limit:' }),
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // limit each IP to 5 login attempts per 15 minutes
  standardHeaders: true,
  legacyHeaders: false,
  message: {
    error: 'Too Many Login Attempts',
    message: 'Too many login attempts from this IP. Please try again later.'
  }
});

// Apply stricter rate limiting to authentication endpoints
app.use('/api/auth/', authLimiter);

// Regular routes
app.get('/api/public', (req, res) => {
  res.json({ message: 'Public API response' });
});

app.post('/api/auth/login', (req, res) => {
  // Login logic
  res.json({ token: 'example-token' });
});

app.listen(3000, () => {
  console.log('Server running on port 3000');
});
```

## Interview Question

### How would you design a rate limiting system for a high-traffic API with millions of users?

**Key Points to Address**:

1. **Scalable Architecture**:
   - Distributed rate limiting using Redis or similar
   - Consistent hashing for client identification
   - Sharding rate limit data by client ID
   - Eventual consistency model for rate limit counters

2. **Algorithm Selection**:
   - Token bucket for most endpoints (allows controlled bursts)
   - Sliding window for sensitive operations
   - Dynamic rate adjustment based on system load
   - Different algorithms for different types of endpoints

3. **Performance Optimization**:
   - Local caching to reduce Redis calls
   - Batched updates to rate limit counters
   - Efficient data structures (sorted sets, hash maps)
   - Asynchronous rate limit enforcement where possible

4. **Client Identification**:
   - Multi-factor client identification (API key + IP + user agent)
   - Handling of shared IPs and proxies
   - Rate limiting at multiple levels (user, organization, IP)
   - Tiered limits based on subscription level

5. **Operational Considerations**:
   - Monitoring and alerting on rate limit events
   - Analytics to identify abuse patterns
   - Circuit breakers for dependent services
   - Graceful degradation under extreme load

## Next Steps

This completes our exploration of API Design. The next module in our system design guide will cover Microservices Architecture, including service boundaries, inter-service communication, and deployment strategies.

## Resources

1. [Rate Limiting: A Useful Tool with Distributed Systems](https://cloud.google.com/architecture/rate-limiting-strategies-techniques)
2. [GitHub API Rate Limiting](https://docs.github.com/en/rest/overview/resources-in-the-rest-api#rate-limiting)
3. [Stripe API Rate Limits](https://stripe.com/docs/rate-limits)
4. [Redis Rate Limiting Pattern](https://redis.io/commands/incr#pattern-rate-limiter)
5. [Cloudflare Rate Limiting](https://www.cloudflare.com/rate-limiting/)
