# API Versioning Strategies

## Introduction

API versioning is a critical aspect of API design that enables evolution while maintaining backward compatibility. As business requirements change and technologies advance, APIs need to evolve without breaking existing client integrations. This section explores different API versioning strategies, their pros and cons, and best practices for implementing them.

## Why Version APIs?

Versioning is necessary for several reasons:

1. **Breaking Changes**: When modifications would break existing clients
2. **Feature Evolution**: Adding new capabilities while supporting legacy clients
3. **Technical Debt**: Phasing out deprecated patterns or implementations
4. **Client Migration**: Allowing clients to migrate at their own pace
5. **Experimentation**: Testing new API designs with a subset of users

## Types of API Changes

Not all changes require a new version. Understanding the types of changes helps determine when versioning is necessary.

### Non-Breaking Changes

These changes typically don't require a new version:

- Adding new endpoints
- Adding optional request parameters
- Adding new fields to response objects
- Adding new resources
- Relaxing validation rules

### Breaking Changes

These changes typically require versioning:

- Removing or renaming fields, endpoints, or resources
- Changing field types or formats
- Adding required request parameters
- Changing response status codes
- Modifying authentication requirements
- Changing error response formats

## Versioning Strategies

### 1. URI Path Versioning

Including the version in the URI path.

**Example**:
```
GET /v1/users/123
GET /v2/users/123
```

**Implementation (Node.js/Express)**:
```javascript
// v1 routes
app.use('/v1/users', v1UserRoutes);
app.use('/v1/products', v1ProductRoutes);

// v2 routes
app.use('/v2/users', v2UserRoutes);
app.use('/v2/products', v2ProductRoutes);

// v1 implementation
const v1UserRoutes = express.Router();
v1UserRoutes.get('/:id', (req, res) => {
  // v1 implementation
  res.json({
    id: req.params.id,
    name: 'John Doe',
    email: '<EMAIL>'
  });
});

// v2 implementation
const v2UserRoutes = express.Router();
v2UserRoutes.get('/:id', (req, res) => {
  // v2 implementation with additional fields
  res.json({
    id: req.params.id,
    name: 'John Doe',
    email: '<EMAIL>',
    profile_picture: 'https://example.com/profiles/johndoe.jpg',
    last_login: '2023-06-15T10:30:00Z'
  });
});
```

**Pros**:
- Simple to implement
- Explicit and visible
- Easy for clients to understand
- Works with all HTTP methods
- Cacheable by intermediaries

**Cons**:
- Clutters URI namespace
- Can lead to code duplication
- Doesn't align with REST principles (resources shouldn't change location)

### 2. Query Parameter Versioning

Specifying the version as a query parameter.

**Example**:
```
GET /users/123?version=1
GET /users/123?version=2
```

**Implementation (Node.js/Express)**:
```javascript
app.get('/users/:id', (req, res) => {
  const version = req.query.version || '1'; // Default to version 1
  
  if (version === '1') {
    return res.json({
      id: req.params.id,
      name: 'John Doe',
      email: '<EMAIL>'
    });
  } else if (version === '2') {
    return res.json({
      id: req.params.id,
      name: 'John Doe',
      email: '<EMAIL>',
      profile_picture: 'https://example.com/profiles/johndoe.jpg',
      last_login: '2023-06-15T10:30:00Z'
    });
  } else {
    return res.status(400).json({ error: 'Unsupported API version' });
  }
});
```

**Pros**:
- Doesn't change resource location
- Optional parameter (can default to latest)
- Easy to implement

**Cons**:
- Easy to miss in documentation
- Less visible than URI versioning
- Can be dropped in some scenarios (e.g., redirects)
- Complicates caching

### 3. HTTP Header Versioning

Using custom HTTP headers to specify the API version.

**Example**:
```
GET /users/123
X-API-Version: 1

GET /users/123
X-API-Version: 2
```

**Implementation (Node.js/Express)**:
```javascript
app.get('/users/:id', (req, res) => {
  const version = req.header('X-API-Version') || '1'; // Default to version 1
  
  if (version === '1') {
    return res.json({
      id: req.params.id,
      name: 'John Doe',
      email: '<EMAIL>'
    });
  } else if (version === '2') {
    return res.json({
      id: req.params.id,
      name: 'John Doe',
      email: '<EMAIL>',
      profile_picture: 'https://example.com/profiles/johndoe.jpg',
      last_login: '2023-06-15T10:30:00Z'
    });
  } else {
    return res.status(400).json({ error: 'Unsupported API version' });
  }
});
```

**Pros**:
- Keeps URI clean
- Doesn't change resource location
- Follows HTTP design principles
- Works with all HTTP methods

**Cons**:
- Less visible than URI versioning
- More complex for clients to implement
- May be stripped by proxies
- Complicates testing via browser

### 4. Accept Header Versioning (Content Negotiation)

Using the HTTP Accept header to request a specific version.

**Example**:
```
GET /users/123
Accept: application/vnd.company.app-v1+json

GET /users/123
Accept: application/vnd.company.app-v2+json
```

**Implementation (Node.js/Express)**:
```javascript
app.get('/users/:id', (req, res) => {
  const acceptHeader = req.header('Accept');
  
  if (acceptHeader.includes('application/vnd.company.app-v1+json')) {
    return res.json({
      id: req.params.id,
      name: 'John Doe',
      email: '<EMAIL>'
    });
  } else if (acceptHeader.includes('application/vnd.company.app-v2+json')) {
    return res.json({
      id: req.params.id,
      name: 'John Doe',
      email: '<EMAIL>',
      profile_picture: 'https://example.com/profiles/johndoe.jpg',
      last_login: '2023-06-15T10:30:00Z'
    });
  } else {
    // Default to latest version
    return res.json({
      id: req.params.id,
      name: 'John Doe',
      email: '<EMAIL>',
      profile_picture: 'https://example.com/profiles/johndoe.jpg',
      last_login: '2023-06-15T10:30:00Z'
    });
  }
});
```

**Pros**:
- Follows HTTP content negotiation standards
- Keeps URI clean
- Doesn't change resource location
- Allows for format negotiation simultaneously

**Cons**:
- Complex to implement correctly
- Less intuitive for API consumers
- Difficult to test in browser
- May be stripped by some proxies

### 5. Hostname Versioning

Using different hostnames for different API versions.

**Example**:
```
GET https://api-v1.example.com/users/123
GET https://api-v2.example.com/users/123
```

**Implementation**:
This typically involves DNS configuration and routing at the infrastructure level.

**Pros**:
- Complete separation between versions
- Can deploy on different infrastructure
- Simplifies load balancing and monitoring

**Cons**:
- Requires additional DNS management
- More complex infrastructure
- Higher maintenance overhead
- Potential cross-origin issues

## Semantic Versioning for APIs

Semantic Versioning (SemVer) provides a standardized way to communicate the nature of changes in your API.

### SemVer Format

```
MAJOR.MINOR.PATCH
```

- **MAJOR**: Incompatible API changes (breaking changes)
- **MINOR**: Added functionality in a backward-compatible manner
- **PATCH**: Backward-compatible bug fixes

### Applying SemVer to APIs

**Example**:
- **1.0.0**: Initial stable release
- **1.1.0**: Added new optional fields to responses
- **1.1.1**: Fixed a bug in error responses
- **2.0.0**: Changed authentication mechanism (breaking change)

## Implementing Multiple Versions

### 1. Code Organization

#### Separate Files/Modules

Organize code by version to maintain clarity.

**Example Directory Structure**:
```
/api
  /v1
    users.js
    products.js
  /v2
    users.js
    products.js
  index.js
```

#### Inheritance and Composition

Use inheritance to extend functionality while minimizing duplication.

**Example (Java)**:
```java
// Base controller with common functionality
public abstract class BaseUserController {
    protected UserService userService;
    
    public BaseUserController(UserService userService) {
        this.userService = userService;
    }
    
    protected UserDto mapToDto(User user) {
        // Common mapping logic
    }
}

// V1 implementation
@RestController
@RequestMapping("/v1/users")
public class UserControllerV1 extends BaseUserController {
    
    public UserControllerV1(UserService userService) {
        super(userService);
    }
    
    @GetMapping("/{id}")
    public UserDtoV1 getUser(@PathVariable Long id) {
        User user = userService.findById(id);
        return mapToV1Dto(user);
    }
    
    private UserDtoV1 mapToV1Dto(User user) {
        UserDtoV1 dto = new UserDtoV1();
        dto.setId(user.getId());
        dto.setName(user.getName());
        dto.setEmail(user.getEmail());
        return dto;
    }
}

// V2 implementation
@RestController
@RequestMapping("/v2/users")
public class UserControllerV2 extends BaseUserController {
    
    public UserControllerV2(UserService userService) {
        super(userService);
    }
    
    @GetMapping("/{id}")
    public UserDtoV2 getUser(@PathVariable Long id) {
        User user = userService.findById(id);
        return mapToV2Dto(user);
    }
    
    private UserDtoV2 mapToV2Dto(User user) {
        UserDtoV2 dto = new UserDtoV2();
        dto.setId(user.getId());
        dto.setName(user.getName());
        dto.setEmail(user.getEmail());
        dto.setProfilePicture(user.getProfilePicture());
        dto.setLastLogin(user.getLastLogin());
        return dto;
    }
}
```

### 2. Version Routing

Implement middleware or interceptors to route requests based on version.

**Example (Node.js/Express)**:
```javascript
// Version routing middleware
function versionRouter(req, res, next) {
  // Check for version in different places
  const version = req.params.version || 
                 req.query.version || 
                 req.header('X-API-Version') || 
                 '1'; // Default version
  
  // Attach version to request object
  req.apiVersion = version;
  
  next();
}

// Apply middleware
app.use(versionRouter);

// Route handler uses version information
app.get('/users/:id', (req, res) => {
  if (req.apiVersion === '1') {
    // v1 implementation
  } else if (req.apiVersion === '2') {
    // v2 implementation
  } else {
    res.status(400).json({ error: 'Unsupported API version' });
  }
});
```

### 3. Feature Toggles

Use feature toggles to gradually roll out new API versions.

**Example**:
```javascript
const featureFlags = {
  'enhanced-user-response': {
    enabled: true,
    enabledForUsers: ['user123', 'user456'],
    enabledPercentage: 25 // Enable for 25% of users
  }
};

app.get('/users/:id', (req, res) => {
  const user = getUserById(req.params.id);
  
  // Check if enhanced response is enabled
  const useEnhancedResponse = 
    featureFlags['enhanced-user-response'].enabled &&
    (featureFlags['enhanced-user-response'].enabledForUsers.includes(req.user.id) ||
     Math.random() < featureFlags['enhanced-user-response'].enabledPercentage / 100);
  
  if (useEnhancedResponse) {
    // Return enhanced (v2) response
    return res.json({
      id: user.id,
      name: user.name,
      email: user.email,
      profile_picture: user.profilePicture,
      last_login: user.lastLogin
    });
  } else {
    // Return standard (v1) response
    return res.json({
      id: user.id,
      name: user.name,
      email: user.email
    });
  }
});
```

## Deprecation and Sunsetting

### 1. Deprecation Process

1. **Announce Deprecation**: Inform users through documentation, headers, and responses
2. **Set Timeline**: Provide clear dates for end-of-life
3. **Offer Migration Path**: Document how to upgrade to newer versions
4. **Monitor Usage**: Track usage of deprecated endpoints
5. **Send Notifications**: Proactively contact users of deprecated features

**Example Deprecation Header**:
```
Deprecation: true
Sunset: Sat, 31 Dec 2023 23:59:59 GMT
Link: <https://api.example.com/v2/users>; rel="successor-version"
```

### 2. Implementation

**Example (Node.js/Express)**:
```javascript
// Deprecation middleware
function deprecationCheck(req, res, next) {
  if (req.originalUrl.startsWith('/v1/')) {
    // Add deprecation headers
    res.set('Deprecation', 'true');
    res.set('Sunset', 'Sat, 31 Dec 2023 23:59:59 GMT');
    res.set('Link', '<https://api.example.com/v2' + req.path + '>; rel="successor-version"');
    
    // Log deprecation access for monitoring
    logger.warn('Deprecated API access', {
      path: req.originalUrl,
      user: req.user?.id,
      client: req.get('User-Agent')
    });
  }
  
  next();
}

// Apply middleware
app.use(deprecationCheck);
```

### 3. Documentation

Clearly mark deprecated features in documentation.

**Example (OpenAPI)**:
```yaml
paths:
  /v1/users/{id}:
    get:
      deprecated: true
      description: |
        **DEPRECATED**: This endpoint will be removed on December 31, 2023.
        Please use /v2/users/{id} instead.
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: User information
```

## Versioning in Different API Styles

### REST API Versioning

REST APIs typically use one of the strategies outlined above, with URI path versioning being the most common.

### GraphQL API Versioning

GraphQL has different versioning approaches:

1. **Schema Versioning**: Evolve the schema without breaking changes
   - Add new fields (non-breaking)
   - Mark fields as deprecated
   - Use schema directives

   ```graphql
   type User {
     id: ID!
     name: String!
     username: String @deprecated(reason: "Use email instead")
     email: String!
     # New field in v2
     profilePicture: String
   }
   ```

2. **Type Versioning**: Create new types for major changes

   ```graphql
   type UserV1 {
     id: ID!
     name: String!
     email: String!
   }
   
   type UserV2 {
     id: ID!
     name: String!
     email: String!
     profilePicture: String
     lastLogin: String
   }
   
   type Query {
     userV1(id: ID!): UserV1
     userV2(id: ID!): UserV2
     # Alias for the latest version
     user(id: ID!): UserV2
   }
   ```

3. **Versioned Fields**: Add versioned fields to existing types

   ```graphql
   type User {
     id: ID!
     name: String!
     email: String!
     # V2 fields
     profilePictureV2: String
     lastLoginV2: String
   }
   ```

### gRPC API Versioning

gRPC uses Protocol Buffers which have built-in versioning support:

```protobuf
// v1 definition
syntax = "proto3";
package example.v1;

message User {
  string id = 1;
  string name = 2;
  string email = 3;
}

// v2 definition
syntax = "proto3";
package example.v2;

message User {
  string id = 1;
  string name = 2;
  string email = 3;
  string profile_picture = 4;
  string last_login = 5;
}
```

## Best Practices

### 1. Choose a Consistent Strategy

Select one versioning approach and use it consistently across your API.

### 2. Default to the Latest Stable Version

When no version is specified, serve the latest stable version.

### 3. Version at the Right Level

Consider versioning at different levels:
- **API Level**: Major architectural changes
- **Resource Level**: Changes to specific resources
- **Field Level**: Changes to specific fields

### 4. Document Everything

Provide clear documentation for:
- Available versions
- Differences between versions
- Deprecation schedules
- Migration guides

### 5. Use Semantic Versioning

Follow SemVer principles to communicate the nature of changes.

### 6. Test All Supported Versions

Maintain automated tests for all supported API versions.

### 7. Monitor Version Usage

Track which clients are using which versions to inform deprecation decisions.

## Case Study: API Versioning at Stripe

Stripe's API versioning approach is widely regarded as a best practice:

1. **Date-Based Versions**: Versions are dates (e.g., `2020-08-27`)
2. **Explicit Version Specification**: Clients specify version in requests
3. **Backward Compatibility**: Old clients continue to work with old behavior
4. **Gradual Evolution**: Small, incremental changes over time
5. **Comprehensive Documentation**: Each version's changes are documented

**Example Request**:
```
GET /v1/customers/cus_123
Stripe-Version: 2020-08-27
```

**Implementation Approach**:
- Each API version is a snapshot of behavior
- New features are added without breaking existing versions
- Changes are accumulated and released periodically as new dated versions
- Old versions are supported for years

## Interview Questions

### Question 1: What factors would you consider when choosing an API versioning strategy?

**Key Points to Address**:

1. **Client Requirements**:
   - Developer experience and ease of use
   - Client capabilities (ability to set headers, etc.)
   - Client update cycles and flexibility

2. **Technical Considerations**:
   - Caching requirements
   - Proxy and firewall configurations
   - Infrastructure constraints
   - Existing patterns in your organization

3. **Business Factors**:
   - Release frequency and development pace
   - Support and maintenance resources
   - Deprecation policies
   - Contractual obligations to clients

4. **API Design Philosophy**:
   - REST principles adherence
   - Consistency with other APIs
   - Long-term evolution strategy
   - Documentation approach

5. **Implementation Complexity**:
   - Development effort
   - Testing requirements
   - Deployment considerations
   - Monitoring capabilities

### Question 2: How would you handle a situation where you need to make breaking changes to an API with thousands of existing clients?

**Key Points**:

1. **Planning Phase**:
   - Identify all breaking changes
   - Assess impact on different client segments
   - Develop a timeline with sufficient migration period
   - Create detailed migration documentation

2. **Communication Strategy**:
   - Announce changes well in advance
   - Provide clear migration guides
   - Offer direct support for major clients
   - Use multiple communication channels

3. **Technical Implementation**:
   - Implement the new version alongside the old
   - Add deprecation notices and headers
   - Provide feature flags for gradual rollout
   - Create migration tools or libraries if possible

4. **Monitoring and Feedback**:
   - Track usage of deprecated endpoints
   - Collect feedback on migration challenges
   - Adjust timeline if necessary
   - Proactively contact clients still using old versions

5. **Sunset Process**:
   - Send escalating warnings as sunset date approaches
   - Consider extending support for critical clients
   - Implement graceful degradation before full removal
   - Maintain archived documentation for legacy versions

### Question 3: Explain how you would version a GraphQL API compared to a REST API.

**Key Points**:

1. **Fundamental Differences**:
   - GraphQL schema vs. REST endpoints
   - GraphQL's built-in deprecation features
   - Single endpoint vs. multiple endpoints
   - Client-specified queries vs. server-defined responses

2. **GraphQL Versioning Approaches**:
   - Schema evolution without explicit versions
   - Type versioning for major changes
   - Field deprecation with `@deprecated` directive
   - Schema directives for version-specific behavior

3. **REST Versioning Approaches**:
   - URI path versioning
   - Header-based versioning
   - Content negotiation
   - Query parameter versioning

4. **Implementation Considerations**:
   - GraphQL schema management tools
   - REST routing and middleware
   - Documentation generation
   - Client library support

5. **Best Practices Comparison**:
   - GraphQL: Evolve schema without breaking changes
   - REST: Explicit versioning for breaking changes
   - GraphQL: Field-level deprecation
   - REST: Resource-level versioning

## Practical Exercise

### Exercise: Implement API Versioning for a User Management System

Design and implement versioning for a user management API with the following requirements:

1. **Version 1 (Current)**:
   - Basic user properties (id, name, email)
   - Simple authentication (username/password)
   - Limited user roles (admin, user)

2. **Version 2 (New)**:
   - Enhanced user properties (add profile picture, preferences)
   - OAuth2 authentication
   - Granular permissions system
   - New endpoints for user analytics

**Requirements**:
- Choose an appropriate versioning strategy
- Implement both versions to run concurrently
- Provide a migration path from v1 to v2
- Include deprecation notices for v1
- Document both versions clearly

## Next Steps

In the next section, we'll explore [Authentication and Authorization](04_Auth_Strategies.md), which is essential for securing your APIs and controlling access to resources.

## Resources

1. [Microsoft REST API Guidelines - Versioning](https://github.com/microsoft/api-guidelines/blob/vNext/Guidelines.md#12-versioning)
2. [Stripe API Versioning](https://stripe.com/docs/api/versioning)
3. [Evolving HTTP APIs](https://www.mnot.net/blog/2012/12/04/api-evolution)
4. [GraphQL Versioning](https://graphql.org/learn/best-practices/#versioning)
5. [API Versioning Methods, a Brief Reference](https://blog.restcase.com/restful-api-versioning-insights/)
