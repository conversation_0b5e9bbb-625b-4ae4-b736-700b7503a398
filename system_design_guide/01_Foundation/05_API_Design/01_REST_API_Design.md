# REST API Design Principles

## Introduction

Representational State Transfer (REST) is an architectural style for designing networked applications. Introduced by <PERSON> in his 2000 doctoral dissertation, REST has become the dominant approach for building web APIs. This section explores the core principles of RESTful API design and best practices for implementing them.

## Core REST Principles

### 1. Resource-Based

REST APIs are organized around resources, which are any kind of object, data, or service that can be accessed by the client.

**Key Concepts**:
- Resources are identified by URIs (Uniform Resource Identifiers)
- Resources should be nouns, not verbs
- Collection resources represent a group of similar items
- Instance resources represent a single item

**Examples**:
- Collection: `/users`, `/articles`, `/products`
- Instance: `/users/123`, `/articles/abc`, `/products/xyz`

### 2. Standard HTTP Methods

REST uses standard HTTP methods to perform operations on resources.

| Method | Purpose | Properties |
|--------|---------|------------|
| GET | Retrieve a resource | Safe, Idempotent |
| POST | Create a new resource | Neither safe nor idempotent |
| PUT | Update a resource by replacing it entirely | Idempotent |
| PATCH | Partially update a resource | Not inherently idempotent |
| DELETE | Remove a resource | Idempotent |

**Safe**: Operation does not modify resources
**Idempotent**: Multiple identical requests have the same effect as a single request

### 3. Statelessness

Each request from client to server must contain all information needed to understand and complete the request. The server does not store client state between requests.

**Benefits**:
- Scalability: Servers don't need to maintain session state
- Reliability: Requests can be routed to any server instance
- Simplicity: Easier to understand and debug

**Implementation**:
- Authentication tokens in headers
- Query parameters for filtering/pagination
- Complete resource representations in requests

### 4. Representation-Focused

Resources can have multiple representations (JSON, XML, HTML, etc.) that are negotiated between client and server.

**Content Negotiation**:
- Use `Accept` header to specify desired format
- Use `Content-Type` header to specify request format

**Example**:
```
GET /users/123 HTTP/1.1
Accept: application/json

HTTP/1.1 200 OK
Content-Type: application/json

{
  "id": 123,
  "name": "John Doe",
  "email": "<EMAIL>"
}
```

### 5. HATEOAS (Hypermedia as the Engine of Application State)

Clients interact with the application entirely through hypermedia provided dynamically by the server.

**Implementation**:
- Include links to related resources
- Provide actions that can be performed on resources
- Enable API discovery through navigation

**Example**:
```json
{
  "id": 123,
  "name": "John Doe",
  "email": "<EMAIL>",
  "_links": {
    "self": { "href": "/users/123" },
    "orders": { "href": "/users/123/orders" },
    "update": { "href": "/users/123", "method": "PUT" },
    "delete": { "href": "/users/123", "method": "DELETE" }
  }
}
```

## URL Design

### 1. Resource Naming

**Best Practices**:
- Use nouns, not verbs
- Use plural nouns for collections
- Use concrete names over abstract concepts
- Keep it simple and intuitive

**Examples**:

| Good | Bad |
|------|-----|
| `/users` | `/getUsers` |
| `/users/123/orders` | `/getUserOrders?userId=123` |
| `/articles/published` | `/getPublishedArticles` |

### 2. Hierarchy and Relationships

**Parent-Child Relationships**:
- `/resources/{resourceId}/sub-resources`
- `/users/123/orders`
- `/orders/456/items`

**Many-to-Many Relationships**:
- `/users/123/roles`
- `/roles/admin/users`

### 3. Query Parameters

Use query parameters for:
- Filtering: `/products?category=electronics`
- Sorting: `/products?sort=price_asc`
- Pagination: `/products?page=2&per_page=20`
- Search: `/products?q=smartphone`
- Field selection: `/products?fields=id,name,price`

### 4. API Versioning in URLs

While there are multiple versioning strategies (covered in detail in the [API Versioning](03_API_Versioning.md) section), URL versioning is common:

- `/v1/users`
- `/v2/users`

## HTTP Status Codes

Using appropriate HTTP status codes improves API clarity and helps clients handle responses correctly.

### Common Status Codes

**Success (2xx)**:
- `200 OK`: Request succeeded
- `201 Created`: Resource created successfully
- `202 Accepted`: Request accepted for processing
- `204 No Content`: Request succeeded with no response body

**Client Errors (4xx)**:
- `400 Bad Request`: Invalid request format or parameters
- `401 Unauthorized`: Authentication required
- `403 Forbidden`: Authenticated but not authorized
- `404 Not Found`: Resource doesn't exist
- `409 Conflict`: Request conflicts with current state
- `422 Unprocessable Entity`: Validation errors

**Server Errors (5xx)**:
- `500 Internal Server Error`: Unexpected server error
- `502 Bad Gateway`: Error from upstream service
- `503 Service Unavailable`: Server temporarily unavailable

### Status Code Selection Guidelines

- Be specific but don't overwhelm clients with too many codes
- Use standard codes over custom ones when possible
- Ensure error responses include helpful details

## Request and Response Design

### 1. JSON as the Default Format

JSON has become the standard format for REST APIs due to its simplicity, readability, and wide support.

**Example**:
```json
{
  "id": 123,
  "name": "Wireless Headphones",
  "price": 99.99,
  "in_stock": true,
  "categories": ["electronics", "audio"],
  "specifications": {
    "battery_life": "20 hours",
    "connectivity": "Bluetooth 5.0"
  }
}
```

### 2. Consistent Property Naming

**Conventions**:
- Use camelCase or snake_case consistently
- Be consistent with pluralization
- Use clear, descriptive names
- Avoid abbreviations unless widely understood

### 3. Error Responses

Provide structured error responses that help clients understand and resolve issues.

**Example**:
```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid request parameters",
    "details": [
      {
        "field": "email",
        "message": "Must be a valid email address"
      },
      {
        "field": "password",
        "message": "Must be at least 8 characters long"
      }
    ]
  }
}
```

### 4. Pagination

Implement consistent pagination for collection resources.

**Response Example**:
```json
{
  "data": [
    { "id": 1, "name": "Product 1" },
    { "id": 2, "name": "Product 2" }
    // ...more items
  ],
  "pagination": {
    "total_items": 100,
    "total_pages": 5,
    "current_page": 1,
    "per_page": 20,
    "next": "/products?page=2&per_page=20",
    "prev": null
  }
}
```

### 5. Field Selection

Allow clients to request only the fields they need to reduce payload size.

**Request**:
```
GET /users?fields=id,name,email
```

**Response**:
```json
[
  {
    "id": 1,
    "name": "John Doe",
    "email": "<EMAIL>"
  },
  {
    "id": 2,
    "name": "Jane Smith",
    "email": "<EMAIL>"
  }
]
```

## Richardson Maturity Model

The Richardson Maturity Model describes the maturity of RESTful APIs across four levels:

### Level 0: The Swamp of POX (Plain Old XML)

- Single URI for all operations
- Single HTTP method (typically POST)
- RPC-style interactions

**Example**:
```
POST /api HTTP/1.1
Content-Type: application/xml

<methodCall>
  <methodName>getUserInfo</methodName>
  <params>
    <param>
      <value><int>123</int></value>
    </param>
  </params>
</methodCall>
```

### Level 1: Resources

- Multiple URIs for different resources
- Still using a single HTTP method

**Example**:
```
POST /users/123 HTTP/1.1
Content-Type: application/json

{
  "action": "getInfo"
}
```

### Level 2: HTTP Verbs

- Proper use of HTTP methods (GET, POST, PUT, DELETE)
- Appropriate status codes

**Example**:
```
GET /users/123 HTTP/1.1
Accept: application/json

HTTP/1.1 200 OK
Content-Type: application/json

{
  "id": 123,
  "name": "John Doe",
  "email": "<EMAIL>"
}
```

### Level 3: Hypermedia Controls (HATEOAS)

- Links to related resources
- Discoverable actions

**Example**:
```
GET /users/123 HTTP/1.1
Accept: application/json

HTTP/1.1 200 OK
Content-Type: application/json

{
  "id": 123,
  "name": "John Doe",
  "email": "<EMAIL>",
  "_links": {
    "self": { "href": "/users/123" },
    "orders": { "href": "/users/123/orders" },
    "update": { "href": "/users/123", "method": "PUT" }
  }
}
```

Most modern APIs operate at Level 2, with some implementing aspects of Level 3.

## Practical Implementation

Let's design a RESTful API for a simple e-commerce system:

### Resource Identification

1. **Users**: People who use the system
2. **Products**: Items available for purchase
3. **Orders**: Purchases made by users
4. **Reviews**: User feedback on products

### Endpoint Design

**Users**:
- `GET /users` - List all users
- `POST /users` - Create a new user
- `GET /users/{id}` - Get a specific user
- `PUT /users/{id}` - Update a user
- `DELETE /users/{id}` - Delete a user
- `GET /users/{id}/orders` - Get orders for a user

**Products**:
- `GET /products` - List all products
- `POST /products` - Create a new product
- `GET /products/{id}` - Get a specific product
- `PUT /products/{id}` - Update a product
- `DELETE /products/{id}` - Delete a product
- `GET /products/{id}/reviews` - Get reviews for a product

**Orders**:
- `GET /orders` - List all orders
- `POST /orders` - Create a new order
- `GET /orders/{id}` - Get a specific order
- `PUT /orders/{id}` - Update an order
- `DELETE /orders/{id}` - Delete an order
- `GET /orders/{id}/items` - Get items in an order

**Reviews**:
- `GET /reviews` - List all reviews
- `POST /reviews` - Create a new review
- `GET /reviews/{id}` - Get a specific review
- `PUT /reviews/{id}` - Update a review
- `DELETE /reviews/{id}` - Delete a review

### Example Requests and Responses

**Create a Product**:
```
POST /products HTTP/1.1
Content-Type: application/json

{
  "name": "Wireless Headphones",
  "description": "High-quality wireless headphones with noise cancellation",
  "price": 99.99,
  "category": "electronics",
  "inventory_count": 100
}
```

**Response**:
```
HTTP/1.1 201 Created
Location: /products/456
Content-Type: application/json

{
  "id": 456,
  "name": "Wireless Headphones",
  "description": "High-quality wireless headphones with noise cancellation",
  "price": 99.99,
  "category": "electronics",
  "inventory_count": 100,
  "created_at": "2023-06-15T10:30:00Z",
  "_links": {
    "self": { "href": "/products/456" },
    "reviews": { "href": "/products/456/reviews" }
  }
}
```

**Get Products with Filtering and Pagination**:
```
GET /products?category=electronics&min_price=50&max_price=200&page=1&per_page=20 HTTP/1.1
Accept: application/json
```

**Response**:
```
HTTP/1.1 200 OK
Content-Type: application/json

{
  "data": [
    {
      "id": 456,
      "name": "Wireless Headphones",
      "price": 99.99,
      "category": "electronics",
      "average_rating": 4.5
    },
    // More products...
  ],
  "pagination": {
    "total_items": 45,
    "total_pages": 3,
    "current_page": 1,
    "per_page": 20,
    "next": "/products?category=electronics&min_price=50&max_price=200&page=2&per_page=20",
    "prev": null
  }
}
```

## Common REST API Patterns

### 1. Filtering, Sorting, and Pagination

**Filtering**:
- Single value: `/products?category=electronics`
- Multiple values: `/products?category=electronics&brand=sony`
- Range: `/products?min_price=50&max_price=200`

**Sorting**:
- Single field: `/products?sort=price`
- Direction: `/products?sort=price_desc`
- Multiple fields: `/products?sort=category_asc,price_desc`

**Pagination**:
- Offset-based: `/products?offset=20&limit=10`
- Page-based: `/products?page=2&per_page=10`
- Cursor-based: `/products?after=abc123&limit=10`

### 2. Bulk Operations

**Bulk Create**:
```
POST /products/bulk HTTP/1.1
Content-Type: application/json

{
  "products": [
    { "name": "Product 1", "price": 19.99 },
    { "name": "Product 2", "price": 29.99 }
  ]
}
```

**Bulk Update**:
```
PUT /products/bulk HTTP/1.1
Content-Type: application/json

{
  "products": [
    { "id": 1, "price": 24.99 },
    { "id": 2, "price": 34.99 }
  ]
}
```

### 3. Partial Updates with PATCH

```
PATCH /products/123 HTTP/1.1
Content-Type: application/json

{
  "price": 89.99,
  "inventory_count": 75
}
```

### 4. Search Endpoints

```
GET /products/search?q=wireless+headphones HTTP/1.1
Accept: application/json
```

### 5. Webhooks for Events

Register a webhook:
```
POST /webhooks HTTP/1.1
Content-Type: application/json

{
  "url": "https://example.com/webhook-receiver",
  "events": ["order.created", "order.updated", "order.fulfilled"]
}
```

## Interview Questions

### Question 1: What are the key principles of RESTful API design and why are they important?

**Key Points to Address**:

1. **Resource-Based Architecture**:
   - Organizing APIs around resources rather than actions
   - Clear, intuitive resource naming
   - Hierarchical structure reflecting relationships

2. **HTTP Method Semantics**:
   - Using appropriate HTTP methods for operations
   - Understanding idempotence and safety properties
   - Consistent method usage across resources

3. **Statelessness**:
   - No client session state stored on server
   - Each request contains all necessary information
   - Benefits for scalability and reliability

4. **Standard Response Codes**:
   - Using appropriate HTTP status codes
   - Consistent error handling
   - Clear communication of request outcomes

5. **Content Negotiation**:
   - Supporting multiple representations
   - Using proper headers for format specification
   - Versioning considerations

6. **HATEOAS (when applicable)**:
   - Providing navigation links in responses
   - Enabling API discoverability
   - Reducing client-side coupling to API structure

### Question 2: How would you design a RESTful API for a social media platform?

**Key Points**:

1. **Resource Identification**:
   - Users, posts, comments, likes, follows, etc.
   - Clear hierarchy and relationships
   - Consistent naming conventions

2. **Endpoint Design**:
   - `/users` - User management
   - `/posts` - Content creation and management
   - `/users/{id}/posts` - Posts by a specific user
   - `/posts/{id}/comments` - Comments on a specific post
   - `/users/{id}/followers` - Users following a specific user

3. **Query Parameters**:
   - Filtering: `/posts?type=image`
   - Sorting: `/posts?sort=recent`
   - Pagination: `/posts?page=2&per_page=20`
   - Search: `/posts/search?q=vacation`

4. **Authentication and Authorization**:
   - Token-based authentication
   - Permission scopes for different operations
   - Privacy considerations for content access

5. **Special Considerations**:
   - Real-time updates (webhooks or streaming)
   - Content moderation workflows
   - Rate limiting for public endpoints
   - Caching strategies for popular content

### Question 3: What are some common REST API anti-patterns and how would you avoid them?

**Key Points**:

1. **Verb-Based URLs**:
   - Anti-pattern: `/getUsers`, `/createOrder`
   - Better approach: Use HTTP methods on resource URLs (`GET /users`, `POST /orders`)

2. **Ignoring HTTP Method Semantics**:
   - Anti-pattern: Using POST for everything
   - Better approach: Use appropriate methods (GET, POST, PUT, DELETE)

3. **Inconsistent Response Formats**:
   - Anti-pattern: Different resources return differently structured data
   - Better approach: Consistent envelope format across all endpoints

4. **Exposing Database Structure**:
   - Anti-pattern: API endpoints mirror database tables exactly
   - Better approach: Design resources based on domain concepts

5. **Chatty APIs**:
   - Anti-pattern: Requiring multiple requests for common operations
   - Better approach: Composite resources or query parameters for including related data

6. **Ignoring Status Codes**:
   - Anti-pattern: Always returning 200 OK with error in body
   - Better approach: Use appropriate HTTP status codes

7. **Breaking Changes Without Versioning**:
   - Anti-pattern: Changing existing endpoints in backward-incompatible ways
   - Better approach: Proper versioning strategy

## Practical Exercise

### Exercise: Design a RESTful API for a Library Management System

Design a RESTful API for a library management system with the following requirements:

1. Manage books, authors, and patrons
2. Allow patrons to borrow and return books
3. Support searching and filtering of books
4. Track overdue books and fines
5. Support librarian administrative functions

**Requirements**:
- Identify key resources and their relationships
- Design URL structure and endpoints
- Specify HTTP methods for different operations
- Define request and response formats
- Include pagination, filtering, and sorting
- Consider authentication and authorization

## Next Steps

In the next section, we'll explore [GraphQL vs. REST](02_GraphQL_vs_REST.md), comparing these two API paradigms and understanding when to use each one.

## Resources

1. [REST API Tutorial](https://restfulapi.net/)
2. [Microsoft REST API Guidelines](https://github.com/microsoft/api-guidelines)
3. [RESTful Web Services Cookbook](https://www.oreilly.com/library/view/restful-web-services/9780596809140/)
4. [Roy Fielding's Dissertation](https://www.ics.uci.edu/~fielding/pubs/dissertation/top.htm)
5. [REST API Design Best Practices](https://stackoverflow.blog/2020/03/02/best-practices-for-rest-api-design/)
