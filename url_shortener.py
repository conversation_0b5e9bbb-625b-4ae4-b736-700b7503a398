"""
URL Shortener Implementation

This module implements a simple URL shortener service with the following features:
- Convert long URLs to short ones
- Redirect from short URLs to original long URLs
- Track basic analytics (number of clicks)
- Set expiration for links
"""
import random
import string
import time
from datetime import datetime, timedelta

class URLShortener:
    def __init__(self, domain="short.url/"):
        """
        Initialize the URL shortener with a base domain and empty storage.
        
        Args:
            domain (str): The base domain for shortened URLs
        """
        self.domain = domain
        self.url_mapping = {}  # Maps short codes to URL details
        self.custom_mapping = {}  # Maps custom codes to short codes
        
    def generate_short_code(self, length=6):
        """
        Generate a random short code of specified length.
        
        Args:
            length (int): Length of the short code
            
        Returns:
            str: A random string of specified length
        """
        characters = string.ascii_letters + string.digits
        return ''.join(random.choice(characters) for _ in range(length))
    
    def shorten_url(self, original_url, custom_code=None, expiration_days=None):
        """
        Shorten a URL and optionally set a custom code and expiration.
        
        Args:
            original_url (str): The original URL to shorten
            custom_code (str, optional): Custom code for the shortened URL
            expiration_days (int, optional): Number of days until the URL expires
            
        Returns:
            str: The shortened URL
        """
        # Check if URL already exists to avoid duplicates
        for code, details in self.url_mapping.items():
            if details['original_url'] == original_url and not details.get('expired', False):
                return f"{self.domain}{code}"
        
        # Generate a short code
        if custom_code:
            if custom_code in self.custom_mapping:
                return f"Custom code '{custom_code}' already in use."
            short_code = self.generate_short_code()
            self.custom_mapping[custom_code] = short_code
        else:
            short_code = self.generate_short_code()
            while short_code in self.url_mapping:
                short_code = self.generate_short_code()
        
        # Set expiration date if provided
        expiration_date = None
        if expiration_days:
            expiration_date = datetime.now() + timedelta(days=expiration_days)
        
        # Store the URL details
        self.url_mapping[short_code] = {
            'original_url': original_url,
            'creation_date': datetime.now(),
            'expiration_date': expiration_date,
            'clicks': 0,
            'expired': False
        }
        
        return f"{self.domain}{short_code if not custom_code else custom_code}"
    
    def get_original_url(self, short_code):
        """
        Get the original URL from a short code and track the click.
        
        Args:
            short_code (str): The short code part of the shortened URL
            
        Returns:
            str: The original URL or an error message
        """
        # Check if it's a custom code
        if short_code in self.custom_mapping:
            short_code = self.custom_mapping[short_code]
        
        # Check if the short code exists
        if short_code not in self.url_mapping:
            return "URL not found."
        
        url_details = self.url_mapping[short_code]
        
        # Check if the URL has expired
        if url_details.get('expiration_date') and datetime.now() > url_details['expiration_date']:
            url_details['expired'] = True
            return "URL has expired."
        
        # Increment click count
        url_details['clicks'] += 1
        
        return url_details['original_url']
    
    def get_url_stats(self, short_code):
        """
        Get statistics for a shortened URL.
        
        Args:
            short_code (str): The short code part of the shortened URL
            
        Returns:
            dict: Statistics for the URL or an error message
        """
        # Check if it's a custom code
        if short_code in self.custom_mapping:
            short_code = self.custom_mapping[short_code]
        
        # Check if the short code exists
        if short_code not in self.url_mapping:
            return "URL not found."
        
        url_details = self.url_mapping[short_code]
        
        # Check if the URL has expired
        if url_details.get('expiration_date') and datetime.now() > url_details['expiration_date']:
            url_details['expired'] = True
        
        return {
            'original_url': url_details['original_url'],
            'creation_date': url_details['creation_date'],
            'expiration_date': url_details.get('expiration_date', 'Never'),
            'clicks': url_details['clicks'],
            'expired': url_details.get('expired', False)
        }
    
    def list_all_urls(self):
        """
        List all shortened URLs and their details.
        
        Returns:
            dict: A dictionary of all shortened URLs and their details
        """
        result = {}
        for code, details in self.url_mapping.items():
            # Check if the URL has expired
            if details.get('expiration_date') and datetime.now() > details['expiration_date']:
                details['expired'] = True
            
            # Find any custom codes for this short code
            custom_codes = [c for c, sc in self.custom_mapping.items() if sc == code]
            
            result[code] = {
                'original_url': details['original_url'],
                'creation_date': details['creation_date'],
                'expiration_date': details.get('expiration_date', 'Never'),
                'clicks': details['clicks'],
                'expired': details.get('expired', False),
                'custom_codes': custom_codes
            }
        
        return result
