class Solution:
    def maxProfit(self, prices: List[int]) -> int:
        sell_list = []
        index_list = []
        for i, buy in enumerate(prices):
            j=i+1
            for sell in prices[i+1:]:
                sold = sell-buy
                if sold > 0:
                    sell_list.append(sold)
                    index_list.append((i,j))
                    j=j+1

        if sell_list:
            print("sell_list", sell_list)
            print("index_list", index_list)
            val = max(sell_list)
            ind = sell_list.index(val)
            res = index_list[ind]
            return res[1]+1
           
        else:
            print("sell_list here", sell_list)
            return 0
            
      
            

        