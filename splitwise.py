
class User():
    def __init__(self, id, Name = "", email = "", contact =None) -> None:
        for name, value in zip(("id","name","email","contact"), (id, Name, email, contact)):
            setattr(self, name, value)
        self.balance = 0

    def getBal(self):
        message = "Bal for user id {} with user name {} is {}".format(self.id, self.name, self.balance)
        return message


class ShowExpense():
    def __init__(self, userid = None, userids = [] ) -> None:
        self.message = "No expenses"
        self.userid = userid
        self.userids  = userids

    def show(self):
        if self.userid:
            print (self.userid.getBal())
        else:
            for users in self.userids:
                print (users.getBal())


class ExpenseCal():

    def __init__(self, type, paidid = None, amount = 0, share = [], percentage = [], paidfor = []):
        self.splittype = type
        self.paidid = paidid
        self.amount = amount
        self.share = share
        self.percentage = percentage
        self.paidfor = paidfor

    def split(self):

        if self.splittype == "Equal":
            self.ids = self.paidfor
            for uobjelse in self.ids:
                # uobjelse = User(uid)
                uobjelse.balance = uobjelse.balance + round(self.amount / len(self.ids))
        elif self.splittype == "Exact":
            i=0
            for uobjelse in self.paidfor:
                # uobjelse = User(uid)
                uobjelse.balance = uobjelse.balance + self.share[i]
                i+=1
        else:
            i=0
            self.ids = self.paidfor
            for uobjelse in self.ids:
                # uobjelse = User(uid)
                uobjelse.balance = uobjelse.balance + round(self.amount * 0.01*self.percentage[i])
                i+=1



u1ob = User('u1',"Peeyush","pdc.com",123)
u2ob = User('u2',"Nupur","ndc.com",245)
u3ob = User('u3', "Nushka","nudc.com",678)
u4ob = User('u4',"Iyana","idc.com",911)


userids = [u1ob,u2ob,u3ob,u4ob]

sob = ShowExpense()
# sob.show()

eob = ExpenseCal("Equal", u1ob, 100, [],[],[u1ob,u2ob,u3ob,u4ob])
eob.split()
sob1 = ShowExpense(u1ob)
sob1.show()
eob = ExpenseCal("Equal", u1ob, 200, [],[],[u1ob,u2ob,u3ob,u4ob])
eob.split()
sob.show()
eob = ExpenseCal("Exact", u1ob, 200, [100,50,50],[],[u2ob,u3ob,u4ob])
eob.split()
sob.show()
eob = ExpenseCal("Percentage", u1ob, 300, [],[25,25,50],[u2ob,u3ob,u4ob])
eob.split()
sob.show()








    