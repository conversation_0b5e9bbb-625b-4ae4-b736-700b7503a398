import random
import threading

class Vehicle():
    def __init__(self, params) -> None:
        self.type = params.get("type")
        self.regid = params.get("regid")
        self.color = params.get("color")
        self.parked = False

        

    def getStatus(self):
        return self.parked
    


class TicketSystem():

    _instance_lock = threading.Lock()
    _unique_instance = None
    tickets = []

    def __new__(cls):
        with cls._instance_lock:
            if cls._unique_instance is None:
                cls._unique_instance = super(TicketSystem, cls).__new__(cls)
        return cls._unique_instance
   
    
    def genticket(self, params):
        self.parkid = "P" + str(random.randint(1,50))
        self.ticket = ""
        self.floor = params.get("floor")
        self.type = params.get("type")
        self.ticket = self.parkid + "_" + self.floor + "_" + self.type
        self.tickets.append(self.ticket)
        return self.ticket
    
    @classmethod
    def gettickets(self):
        print(self.tickets)
        


class ParkingSlot():
    def __init__(self,params) -> None:
        self.floors = params.get("floors")
        self.slots  = params.get("slots")
        self.floorslots = {}

    def addslots(self):
        for floor in range(self.floors):
            self.floorslots[floor]  = self.slots
            

    def allotparking(self, vehicle):
        for floor, val in self.floorslots.items():
            print("floor")
            if vehicle.type in val.get("slottype"):
                if val["slottype"][vehicle.type] > 0:
                    params = {"floor":str(floor),"type":vehicle.type}
                    ts = TicketSystem()
                    tkt = ts.genticket(params)
                    val["slottype"][vehicle.type]-=1
                    vehicle.parked  = True
                    return tkt


    def floorstatus(self):
        for floor,val in self.floorslots.items():
            print("Floor status for {} is {}".format(floor, val))



parkingslot = {    "floors" : 3, 
                   "slots": 
                        {
                            "slottype": {"truck":1,"bike":2,"car":4}
                        }
                         
              }

v1 = Vehicle({"type":"truck", "regid":1212, "color":"black"})
v2 = Vehicle({"type":"car", "regid":1313, "color":"red"})
v3 = Vehicle({"type":"bike", "regid":1414, "color":"white"})

pk = ParkingSlot(parkingslot)
pk.addslots()
pk.floorstatus()
tkt = pk.allotparking(v1)
print("tkt is", tkt)
pk.floorstatus()
tkt = pk.allotparking(v2)
print("tkt is", tkt)
pk.floorstatus()
tkt = pk.allotparking(v3)
print("tkt is", tkt)
pk.floorstatus()

TicketSystem.gettickets()

tk = TicketSystem()
print(tk.tickets)