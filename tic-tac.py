import numpy as np

grid = np.array([[0, 0, 0],
                    [0, 0, 0],
                    [0, 0, 0]])
player_list =[1,2]

players_dict = {}

for player in player_list:
    players_dict[player] = []

poss_array = [ [(0,0), (0,1), (0,2)] , [(0,0),(1,1),(2,2)] , [(2,0), 
                            (1,1),(0,2)], [(0,0),(1,0),(2,0)],[(2,0),(2,1),(2,2)], [(0,2),(1,2),(2,2)] ]


    
def board():

    return grid
    

def findmove(player):
    indices =  np.random.randint(0, high=3, size=2)

    # Extract the row and column indices
    i = indices[0]
    j = indices[1]

    if grid[i,j] == 0:
        grid[i,j] = player
    else:
        return

    players_dict[player].append((i,j))

    print("grid", grid)
    print("==================")
    print("==================")


def check_win(player):
    print("player moves so far for player {}".format(player),players_dict[player])
    for i in poss_array:
        if all(a in players_dict[player] for a in i):
            return True
    return False
    


b= board()
print("initial grid", b)
print("==================")
while (True):
    flag = 0
    for player in player_list:
        findmove(player)
        if check_win(player) is True:
            print("Player {} has won the game".format(player))
            flag = 1
            break
    if flag == 1:
        break

    if np.any(grid) is True:
        print("no one won")
        break





