class logger():
    def __init__(self) -> None:
        self.messageMap = {}
   
    def shouldPrintMessage(self, timestamp,message):
        if message not in self.messageMap.keys():
            self.messageMap[message] = self.messageMap.get("message",0) + timestamp
            self.messageMap[message] +=10
            print("next allowed timestamp for message {} is {}".format(message, self.messageMap[message]))
            return True

        if timestamp == self.messageMap[message]:
            self.messageMap[message] += 10
            print("next allowed timestamp for message {} is {}".format(message, self.messageMap[message]))
            return True
        elif timestamp < self.messageMap[message]:
            return False
        else:
            self.messageMap[message] = timestamp + 10
            print("next allowed timestamp for message {} is {}".format(message, self.messageMap[message]))
            return True


lg = logger()

lg.shouldPrintMessage(1, "foo");  
# // return true, next allowed timestamp for "foo" is 1 + 10 = 11
lg.shouldPrintMessage(2, "bar");  
# // return true, next allowed timestamp for "bar" is 2 + 10 = 12
lg.shouldPrintMessage(3, "foo");  
# // 3 < 11, return false
lg.shouldPrintMessage(8, "bar"); 
# // 8 < 12, return false
lg.shouldPrintMessage(10, "foo"); 
# // 10 < 11, return false
lg.shouldPrintMessage(11, "foo");
# return true, next allowed timestamp for "foo" is 11 + 10 = 21