# from collections import OrderedDict

# mydict = OrderedDict()

# for k in mydict.items():
#   print(k)
# print('\n')
# mydict.pop('b')
# mydict['b'] = '2'
# for k in mydict.items():
#   print(k)

from collections import defaultdict

def defval():
  return 'default value'

mydict = defaultdict()
mydict['a'] = 1
mydict['b'] = 2
mydict['c'] = 3

for k in mydict.items():
  print(k)

print('\n') 

# if we try to get 'd' 
print(mydict['d']) 
# with a 'generic' dict this will raise a KeyError exception

print('\n') 

# it also add it to the dict
for k in mydict.items():
  print(k)