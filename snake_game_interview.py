from collections import deque

class SnakeG<PERSON>():
    def __init__(self, width, height, food) -> None:
        self.w = width
        self.h = height
        self.food = deque()
        for f in food:
            self.food.append(f)
        self.snake = deque()
        self.head = [0,0]
        self.score = 0
        self.snake.append([0,0])


    def move(self, direction):
        if direction == "U":
            self.head[0] = self.head[0] - 1

        elif direction == "D":
            self.head[0] = self.head[0] + 1

        elif direction == "L":
            self.head[1] = self.head[1] - 1

        else:
            self.head[1] = self.head[1] + 1

        if self.head[0] < 0 or self.head[1] > self.w -1 or self.head[0] > self.h -1 or self.head[1] < 0:
            return -1

        
        if len(self.food) > 0:
            food = self.food.popleft()
            new_r, new_C = self.head[0], self.head[1]
            if self.head == food:
                self.snake.append([new_r, new_C])
                self.score+=1
            else:
                self.food.appendleft(food)
                self.snake.append([new_r, new_C])
                self.snake.popleft()
        else:
            return -1
        
        
        return self.score
            
        

        



sg = SnakeGame(3,2,[[1,2],[0,1]])
score  =  sg.move("R")
score  =  sg.move("D")
score  =  sg.move("R")
score  =  sg.move("U")
score  =  sg.move("L")
score  =  sg.move("U")
