from threading import Thread, Lock
import time


lock = Lock()

class Hitcounter():
    def __init__(self) -> None:
        self.counterlist = []
        self.start_time = time.time()

    def check(self):
        while True:
            time.sleep(1)
            if time.time() - self.start_time > 10:
                print(time.time())
                self.start_time = time.time()
                if self.counterlist:
                    self.counterlist.pop(0)

    
    def hit(self, val):
        with lock:
            self.counterlist.append(val)

    def getHit(self):
        with lock:
            print("hit counter",len(self.counterlist))
    


 




