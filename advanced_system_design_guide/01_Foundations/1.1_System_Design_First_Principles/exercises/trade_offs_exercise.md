# Exercise: Analyzing Trade-offs in Real-world Systems

## Objective

In this exercise, you will analyze trade-offs in real-world systems and practice applying system design first principles to make informed decisions.

## Instructions

### Part 1: Analyzing Existing Systems

Choose two different systems from the following list:

1. A social media platform (e.g., Twitter, Instagram)
2. An e-commerce website (e.g., Amazon, eBay)
3. A video streaming service (e.g., Netflix, YouTube)
4. A ride-sharing application (e.g., Uber, Lyft)
5. A cloud storage service (e.g., Dropbox, Google Drive)

For each system:

1. Identify at least three key trade-offs the system designers likely had to make
2. Explain why these trade-offs were necessary
3. Describe how the system balances these competing concerns
4. Discuss how these trade-offs align with the system's primary goals and user expectations

**Example Trade-offs to Consider:**
- Consistency vs. Availability
- Latency vs. Throughput
- Performance vs. Maintainability
- Simplicity vs. Flexibility
- Cost vs. Reliability
- Security vs. Usability

### Part 2: Applying Separation of Concerns

Choose one of the systems you analyzed in Part 1 and:

1. Identify at least 5 major concerns that should be separated
2. For each concern:
   - Describe its primary responsibility
   - Explain why it should be separated from other concerns
   - Identify potential interfaces with other concerns
   - Discuss the benefits of this separation

### Part 3: Designing with Trade-offs

You are designing a new mobile payment application that allows users to:
- Send money to friends and family
- Pay merchants for goods and services
- Save payment methods and manage a digital wallet
- View transaction history and generate reports

For this application:

1. Identify at least 5 key trade-offs you would need to consider
2. For each trade-off:
   - Describe the competing concerns
   - Explain which side of the trade-off you would prioritize and why
   - Discuss how your decision aligns with the application's goals
   - Describe how you would mitigate the downsides of your choice

3. Apply the Single Responsibility Principle to identify at least 6 distinct components of the system
4. For each component:
   - Define its single responsibility
   - List its key functions
   - Describe how it would interact with other components

## Deliverables

For this exercise, create a document with the following sections:

1. **Existing Systems Analysis**
   - System 1 Trade-offs
   - System 2 Trade-offs

2. **Separation of Concerns Analysis**
   - System Components and Responsibilities
   - Component Interfaces
   - Benefits of Separation

3. **Mobile Payment App Design**
   - Trade-off Decisions and Rationale
   - Component Breakdown (applying SRP)
   - Component Interactions

## Evaluation Criteria

Your analysis will be evaluated based on:

1. **Depth of Understanding**: How well you identify and explain relevant trade-offs
2. **Practical Application**: How effectively you apply first principles to real-world scenarios
3. **Reasoning**: How clearly you justify your design decisions
4. **Completeness**: How thoroughly you address all parts of the exercise
5. **System Thinking**: How well you consider the system as a whole while analyzing its parts

## Extension (Optional)

If you want to challenge yourself further:

1. Create a high-level architecture diagram for your mobile payment application
2. Identify potential scaling challenges and how your design addresses them
3. Discuss how your design would evolve as the user base grows from 1,000 to 1 million to 100 million users
