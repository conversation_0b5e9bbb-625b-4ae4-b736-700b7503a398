# URL Shortener System Design

This example demonstrates how to apply system design first principles to a common interview problem: designing a URL shortening service like bit.ly or TinyURL.

## Problem Statement

Design a service that:
1. Takes a long URL as input and returns a shortened URL
2. When users access the shortened URL, redirects them to the original long URL
3. Provides basic analytics on URL usage
4. Handles high traffic volumes efficiently

## Applying First Principles

### Separation of Concerns

We'll divide our system into distinct components, each with a specific responsibility:

1. **URL Shortening Service**: Handles the creation of short URLs
2. **URL Redirection Service**: Manages redirects from short URLs to original URLs
3. **Analytics Service**: Tracks and reports on URL usage
4. **Storage Service**: Manages persistent storage of URL mappings
5. **Cache Service**: Provides fast access to frequently accessed URLs

### Single Responsibility Principle

Each component has a single, well-defined responsibility:

- **URL Shortening Service**: Only concerned with generating unique short codes
- **URL Redirection Service**: Only concerned with looking up and redirecting URLs
- **Analytics Service**: Only concerned with collecting and processing usage data

### Design for Scale, Performance, and Reliability

- **Read-Heavy Workload**: The system will have many more redirects than new URL creations
- **Caching Strategy**: Frequently accessed URLs are cached to reduce database load
- **Horizontal Scaling**: Stateless services can be scaled independently
- **Database Sharding**: URL mappings can be sharded based on the short code
- **Redundancy**: Multiple instances of each service for high availability

### Trade-offs

1. **Short URL Length vs. Uniqueness**:
   - Shorter URLs are more user-friendly but limit the number of unique URLs
   - We choose a 7-character alphanumeric code, providing 62^7 ≈ 3.5 trillion possibilities

2. **Custom URLs vs. System Simplicity**:
   - Allowing custom short URLs adds complexity but improves user experience
   - We support custom URLs as an optional feature

3. **Analytics Depth vs. Performance**:
   - More detailed analytics increases storage and processing requirements
   - We implement basic analytics (clicks, referrers, timestamps) with an option for more detailed tracking

4. **Consistency vs. Availability**:
   - In the rare case of a conflict, we prioritize availability over consistency
   - URL creation can have eventual consistency, but redirects must be highly available

## System Architecture

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  API Gateway    │────▶│  Load Balancer  │────▶│  Web Servers    │
│                 │     │                 │     │                 │
└─────────────────┘     └─────────────────┘     └────────┬────────┘
                                                         │
                                                         ▼
┌─────────────────┐                           ┌─────────────────────┐
│                 │                           │                     │
│  Analytics      │◀──────────────────────────│  URL Services       │
│  Service        │                           │  - Shortening       │
│                 │                           │  - Redirection      │
└─────────────────┘                           │                     │
                                              └──────────┬──────────┘
                                                         │
                        ┌─────────────────┐             │
                        │                 │             │
                        │  Cache Layer    │◀────────────┘
                        │  (Redis)        │             │
                        │                 │             │
                        └────────┬────────┘             │
                                 │                      │
                                 ▼                      ▼
                        ┌─────────────────┐    ┌─────────────────┐
                        │                 │    │                 │
                        │  Database       │    │  Database       │
                        │  (Primary)      │    │  (Replica)      │
                        │                 │    │                 │
                        └─────────────────┘    └─────────────────┘
```

## Component Details

### URL Shortening Service

**Responsibility**: Generate short URLs from long URLs

**Key Functions**:
- Generate a unique short code for a given URL
- Check for collisions with existing codes
- Handle custom URL requests
- Store the mapping in the database

**Algorithm Options**:
1. **Counter-based**: Use an auto-incrementing counter and convert to base62
2. **Hash-based**: Generate a hash of the long URL and take the first 7 characters
3. **Random Generation**: Generate random 7-character strings and check for collisions

We choose a counter-based approach for guaranteed uniqueness and sequential generation.

### URL Redirection Service

**Responsibility**: Redirect users from short URLs to original URLs

**Key Functions**:
- Look up the original URL for a given short code
- Perform the HTTP redirect
- Record analytics data for the access
- Handle invalid or expired URLs

**Optimization**:
- Cache frequently accessed URLs in Redis
- Use read replicas for the database to handle high read loads

### Analytics Service

**Responsibility**: Track and analyze URL usage

**Key Functions**:
- Record access timestamps
- Track referrer information
- Count clicks per URL
- Generate usage reports

**Implementation**:
- Use asynchronous processing to avoid impacting redirect performance
- Aggregate data periodically for reporting

### Storage Service

**Responsibility**: Persist URL mappings and analytics data

**Schema**:
```
urls:
  - short_code (PK)
  - original_url
  - user_id (if applicable)
  - creation_date
  - expiration_date (if applicable)
  - custom (boolean)

clicks:
  - id (PK)
  - short_code (FK)
  - timestamp
  - referrer
  - user_agent
  - ip_address (anonymized)
```

**Database Choice**:
- Primary database: PostgreSQL or MySQL for ACID properties
- Consider NoSQL (like Cassandra) for very high scale

### Cache Service

**Responsibility**: Provide fast access to frequently accessed URLs

**Implementation**:
- Redis for in-memory caching
- LRU eviction policy
- TTL for cache entries

## Scaling Considerations

1. **Horizontal Scaling**:
   - All services are stateless and can be scaled horizontally
   - Database read replicas for handling high read loads

2. **Database Sharding**:
   - Shard based on the first character(s) of the short code
   - Consistent hashing for distribution

3. **Geographic Distribution**:
   - CDN for static assets
   - Regional deployments for lower latency

4. **Rate Limiting**:
   - Prevent abuse of the URL creation API
   - Token bucket algorithm for rate limiting

## Failure Handling

1. **Service Redundancy**:
   - Multiple instances of each service
   - Automatic failover

2. **Database Redundancy**:
   - Primary-replica setup
   - Regular backups

3. **Graceful Degradation**:
   - If analytics service is down, still allow redirects
   - If custom URL creation fails, fall back to automatic generation

## Implementation Example

See the accompanying code files for a simplified implementation of this design:

- `url_shortener.py`: Core URL shortening logic
- `redirection_service.py`: URL redirection handling
- `storage.py`: Database interaction
- `cache.py`: Caching implementation
- `analytics.py`: Basic analytics tracking

## Conclusion

This URL shortener design demonstrates the application of system design first principles:

1. **Separation of Concerns**: Clear boundaries between different system responsibilities
2. **Single Responsibility Principle**: Each component has one well-defined purpose
3. **Design for Scale**: Considerations for handling high traffic and growth
4. **Trade-offs**: Explicit decisions about competing concerns

By applying these principles, we've created a design that is scalable, maintainable, and reliable.
