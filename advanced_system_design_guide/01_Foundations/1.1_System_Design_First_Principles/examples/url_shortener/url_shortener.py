"""
URL Shortener Implementation Example

This file demonstrates a simplified implementation of the URL shortening service
described in the README.md. It applies the principles of Separation of Concerns
and Single Responsibility Principle.
"""

import hashlib
import base64
import time
import redis
import sqlite3
from typing import Dict, Optional, Tu<PERSON>
from datetime import datetime, timedelta


# -------------------------------------------------------------------------
# Separation of Concerns: Each class handles a distinct aspect of the system
# -------------------------------------------------------------------------

class URLShortener:
    """Handles the generation of short URLs from long URLs."""
    
    def __init__(self, storage_service, counter_service):
        self.storage = storage_service
        self.counter = counter_service
        self.base62_chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz"
    
    def shorten_url(self, long_url: str, custom_code: Optional[str] = None) -> str:
        """Generate a short URL code for a given long URL."""
        if custom_code:
            # Check if custom code is available
            if self.storage.code_exists(custom_code):
                raise ValueError(f"Custom code '{custom_code}' is already in use")
            
            # Store the mapping and return the custom code
            self.storage.store_url_mapping(custom_code, long_url)
            return custom_code
        
        # Generate a short code using counter-based approach
        counter = self.counter.get_next_counter()
        short_code = self._encode_base62(counter)
        
        # Ensure the code is at least 7 characters
        short_code = short_code.zfill(7)
        
        # Store the mapping
        self.storage.store_url_mapping(short_code, long_url)
        
        return short_code
    
    def _encode_base62(self, number: int) -> str:
        """Convert a decimal number to base62 representation."""
        if number == 0:
            return self.base62_chars[0]
        
        result = ""
        base = len(self.base62_chars)
        
        while number > 0:
            number, remainder = divmod(number, base)
            result = self.base62_chars[remainder] + result
            
        return result


class RedirectionService:
    """Handles redirecting from short URLs to original URLs."""
    
    def __init__(self, storage_service, cache_service, analytics_service):
        self.storage = storage_service
        self.cache = cache_service
        self.analytics = analytics_service
    
    def get_original_url(self, short_code: str) -> Optional[str]:
        """Get the original URL for a given short code."""
        # Try to get the URL from cache first
        original_url = self.cache.get(short_code)
        
        if original_url:
            # Record analytics asynchronously
            self.analytics.record_access(short_code, cache_hit=True)
            return original_url
        
        # If not in cache, get from storage
        original_url = self.storage.get_original_url(short_code)
        
        if original_url:
            # Store in cache for future requests
            self.cache.set(short_code, original_url)
            
            # Record analytics asynchronously
            self.analytics.record_access(short_code, cache_hit=False)
            
        return original_url


class StorageService:
    """Manages persistent storage of URL mappings."""
    
    def __init__(self, db_path: str = "url_shortener.db"):
        self.db_path = db_path
        self._initialize_db()
    
    def _initialize_db(self):
        """Initialize the database schema if it doesn't exist."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Create tables if they don't exist
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS urls (
            short_code TEXT PRIMARY KEY,
            original_url TEXT NOT NULL,
            creation_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            user_id TEXT,
            expiration_date TIMESTAMP,
            custom BOOLEAN DEFAULT 0
        )
        ''')
        
        conn.commit()
        conn.close()
    
    def store_url_mapping(self, short_code: str, original_url: str, 
                          user_id: Optional[str] = None, 
                          expiration_days: Optional[int] = None,
                          custom: bool = False) -> bool:
        """Store a mapping between a short code and original URL."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            expiration_date = None
            if expiration_days:
                expiration_date = (datetime.now() + timedelta(days=expiration_days)).isoformat()
            
            cursor.execute(
                "INSERT INTO urls (short_code, original_url, user_id, expiration_date, custom) "
                "VALUES (?, ?, ?, ?, ?)",
                (short_code, original_url, user_id, expiration_date, 1 if custom else 0)
            )
            
            conn.commit()
            return True
            
        except sqlite3.Error as e:
            print(f"Database error: {e}")
            return False
            
        finally:
            conn.close()
    
    def get_original_url(self, short_code: str) -> Optional[str]:
        """Get the original URL for a given short code."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            # Get the URL and check if it's expired
            cursor.execute(
                "SELECT original_url, expiration_date FROM urls WHERE short_code = ?",
                (short_code,)
            )
            
            result = cursor.fetchone()
            
            if not result:
                return None
                
            original_url, expiration_date = result
            
            # Check if the URL has expired
            if expiration_date and datetime.fromisoformat(expiration_date) < datetime.now():
                return None
                
            return original_url
            
        except sqlite3.Error as e:
            print(f"Database error: {e}")
            return None
            
        finally:
            conn.close()
    
    def code_exists(self, short_code: str) -> bool:
        """Check if a short code already exists in the database."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            cursor.execute("SELECT 1 FROM urls WHERE short_code = ?", (short_code,))
            return cursor.fetchone() is not None
            
        except sqlite3.Error as e:
            print(f"Database error: {e}")
            return False
            
        finally:
            conn.close()


class CacheService:
    """Provides fast access to frequently accessed URLs."""
    
    def __init__(self, host: str = 'localhost', port: int = 6379, db: int = 0, ttl: int = 3600):
        """Initialize the cache service with Redis."""
        self.redis_client = redis.Redis(host=host, port=port, db=db)
        self.ttl = ttl  # Time-to-live in seconds (default: 1 hour)
    
    def get(self, key: str) -> Optional[str]:
        """Get a value from the cache."""
        try:
            value = self.redis_client.get(key)
            return value.decode('utf-8') if value else None
        except redis.RedisError as e:
            print(f"Redis error: {e}")
            return None
    
    def set(self, key: str, value: str, ttl: Optional[int] = None) -> bool:
        """Set a value in the cache with optional TTL."""
        try:
            return self.redis_client.set(key, value, ex=ttl or self.ttl)
        except redis.RedisError as e:
            print(f"Redis error: {e}")
            return False
    
    def delete(self, key: str) -> bool:
        """Delete a value from the cache."""
        try:
            return self.redis_client.delete(key) > 0
        except redis.RedisError as e:
            print(f"Redis error: {e}")
            return False


class AnalyticsService:
    """Tracks and reports on URL usage."""
    
    def __init__(self, db_path: str = "url_shortener.db"):
        self.db_path = db_path
        self._initialize_db()
    
    def _initialize_db(self):
        """Initialize the database schema if it doesn't exist."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Create tables if they don't exist
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS clicks (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            short_code TEXT NOT NULL,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            referrer TEXT,
            user_agent TEXT,
            ip_address TEXT,
            cache_hit BOOLEAN DEFAULT 0,
            FOREIGN KEY (short_code) REFERENCES urls (short_code)
        )
        ''')
        
        conn.commit()
        conn.close()
    
    def record_access(self, short_code: str, cache_hit: bool = False, 
                      referrer: Optional[str] = None, user_agent: Optional[str] = None,
                      ip_address: Optional[str] = None):
        """Record analytics data for a URL access."""
        # In a real system, this would be done asynchronously
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            cursor.execute(
                "INSERT INTO clicks (short_code, referrer, user_agent, ip_address, cache_hit) "
                "VALUES (?, ?, ?, ?, ?)",
                (short_code, referrer, user_agent, ip_address, 1 if cache_hit else 0)
            )
            
            conn.commit()
            
        except sqlite3.Error as e:
            print(f"Database error: {e}")
            
        finally:
            conn.close()
    
    def get_click_count(self, short_code: str) -> int:
        """Get the number of clicks for a specific short URL."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            cursor.execute("SELECT COUNT(*) FROM clicks WHERE short_code = ?", (short_code,))
            return cursor.fetchone()[0]
            
        except sqlite3.Error as e:
            print(f"Database error: {e}")
            return 0
            
        finally:
            conn.close()
    
    def get_top_urls(self, limit: int = 10) -> list:
        """Get the top most clicked URLs."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            cursor.execute(
                "SELECT urls.short_code, urls.original_url, COUNT(clicks.id) as click_count "
                "FROM urls LEFT JOIN clicks ON urls.short_code = clicks.short_code "
                "GROUP BY urls.short_code "
                "ORDER BY click_count DESC "
                "LIMIT ?",
                (limit,)
            )
            
            return cursor.fetchall()
            
        except sqlite3.Error as e:
            print(f"Database error: {e}")
            return []
            
        finally:
            conn.close()


class CounterService:
    """Manages the counter for generating sequential short codes."""
    
    def __init__(self, db_path: str = "url_shortener.db"):
        self.db_path = db_path
        self._initialize_db()
    
    def _initialize_db(self):
        """Initialize the database schema if it doesn't exist."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Create tables if they don't exist
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS counters (
            name TEXT PRIMARY KEY,
            value INTEGER NOT NULL
        )
        ''')
        
        # Initialize the URL counter if it doesn't exist
        cursor.execute(
            "INSERT OR IGNORE INTO counters (name, value) VALUES ('url_counter', 1000000)"
        )
        
        conn.commit()
        conn.close()
    
    def get_next_counter(self) -> int:
        """Get the next counter value and increment it."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            # Use a transaction to ensure atomicity
            conn.execute("BEGIN EXCLUSIVE TRANSACTION")
            
            # Get the current counter value
            cursor.execute("SELECT value FROM counters WHERE name = 'url_counter'")
            current_value = cursor.fetchone()[0]
            
            # Increment the counter
            cursor.execute(
                "UPDATE counters SET value = value + 1 WHERE name = 'url_counter'"
            )
            
            conn.commit()
            return current_value
            
        except sqlite3.Error as e:
            conn.rollback()
            print(f"Database error: {e}")
            # Return a random value as fallback (not ideal for production)
            return int(time.time() * 1000)
            
        finally:
            conn.close()


# -------------------------------------------------------------------------
# Example usage
# -------------------------------------------------------------------------

def main():
    """Demonstrate the URL shortener functionality."""
    # Initialize services
    storage = StorageService()
    cache = CacheService()
    analytics = AnalyticsService()
    counter = CounterService()
    
    # Create the main services
    url_shortener = URLShortener(storage, counter)
    redirection = RedirectionService(storage, cache, analytics)
    
    # Example 1: Create a short URL
    long_url = "https://example.com/very/long/url/that/needs/shortening?with=parameters&and=more"
    short_code = url_shortener.shorten_url(long_url)
    print(f"Shortened URL: {short_code} -> {long_url}")
    
    # Example 2: Create a custom short URL
    custom_url = "https://example.com/custom-page"
    try:
        custom_code = url_shortener.shorten_url(custom_url, custom_code="custom")
        print(f"Custom shortened URL: {custom_code} -> {custom_url}")
    except ValueError as e:
        print(f"Error creating custom URL: {e}")
    
    # Example 3: Redirect from short URL to original URL
    original_url = redirection.get_original_url(short_code)
    print(f"Redirecting {short_code} -> {original_url}")
    
    # Example 4: Get analytics
    click_count = analytics.get_click_count(short_code)
    print(f"Click count for {short_code}: {click_count}")
    
    # Example 5: Get top URLs
    top_urls = analytics.get_top_urls(5)
    print("Top URLs:")
    for code, url, count in top_urls:
        print(f"  {code}: {count} clicks -> {url}")


if __name__ == "__main__":
    # Note: In a real implementation, you would need Redis running
    # This example will fail if Redis is not available
    try:
        main()
    except redis.RedisError:
        print("Redis connection failed. Please ensure Redis is running.")
        print("For demonstration purposes, let's mock the Redis dependency:")
        
        # Mock implementation for demonstration
        class MockRedisClient:
            def __init__(self):
                self.data = {}
            
            def get(self, key):
                return self.data.get(key, None)
            
            def set(self, key, value, ex=None):
                self.data[key] = value
                return True
            
            def delete(self, key):
                if key in self.data:
                    del self.data[key]
                    return 1
                return 0
        
        # Monkey patch Redis for demonstration
        redis.Redis = lambda host, port, db: MockRedisClient()
        
        # Try again with mock Redis
        main()
