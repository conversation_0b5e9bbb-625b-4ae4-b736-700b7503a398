"""
E-Commerce System Example: Applying First Principles

This example demonstrates how to apply Separation of Concerns (SoC) and
Single Responsibility Principle (SRP) to an e-commerce system.
"""

# -------------------------------------------------------------------------
# Separation of Concerns: Each class handles a distinct aspect of the system
# -------------------------------------------------------------------------

class ProductCatalog:
    """Manages product information and search functionality."""
    
    def __init__(self, database_connection):
        self.db = database_connection
        
    def get_product(self, product_id):
        """Retrieve a product by its ID."""
        # In a real system, this would query the database
        return self.db.query(f"SELECT * FROM products WHERE id = {product_id}")
    
    def search_products(self, query, filters=None):
        """Search for products matching the query and filters."""
        # Implementation would include search logic
        base_query = f"SELECT * FROM products WHERE name LIKE '%{query}%'"
        if filters:
            # Add filter conditions to the query
            pass
        return self.db.query(base_query)
    
    def get_product_recommendations(self, product_id):
        """Get recommended products based on a product."""
        # This might use a recommendation algorithm
        return self.db.query(f"SELECT * FROM product_recommendations WHERE product_id = {product_id}")


class ShoppingCart:
    """Handles shopping cart operations."""
    
    def __init__(self, user_id, database_connection):
        self.user_id = user_id
        self.db = database_connection
        
    def add_item(self, product_id, quantity=1):
        """Add an item to the cart."""
        # Check if product exists in cart already
        existing_item = self.db.query(
            f"SELECT * FROM cart_items WHERE user_id = {self.user_id} AND product_id = {product_id}"
        )
        
        if existing_item:
            # Update quantity
            self.db.execute(
                f"UPDATE cart_items SET quantity = quantity + {quantity} "
                f"WHERE user_id = {self.user_id} AND product_id = {product_id}"
            )
        else:
            # Insert new item
            self.db.execute(
                f"INSERT INTO cart_items (user_id, product_id, quantity) "
                f"VALUES ({self.user_id}, {product_id}, {quantity})"
            )
    
    def remove_item(self, product_id):
        """Remove an item from the cart."""
        self.db.execute(
            f"DELETE FROM cart_items WHERE user_id = {self.user_id} AND product_id = {product_id}"
        )
    
    def get_items(self):
        """Get all items in the cart."""
        return self.db.query(f"SELECT * FROM cart_items WHERE user_id = {self.user_id}")
    
    def clear(self):
        """Clear all items from the cart."""
        self.db.execute(f"DELETE FROM cart_items WHERE user_id = {self.user_id}")


class OrderProcessor:
    """Handles order creation and processing."""
    
    def __init__(self, database_connection, payment_service, inventory_service):
        self.db = database_connection
        self.payment_service = payment_service
        self.inventory_service = inventory_service
    
    def create_order(self, user_id, cart_items, shipping_address):
        """Create a new order from cart items."""
        # Start a transaction
        self.db.begin_transaction()
        
        try:
            # Create order record
            order_id = self.db.execute(
                f"INSERT INTO orders (user_id, status, shipping_address) "
                f"VALUES ({user_id}, 'pending', '{shipping_address}') RETURNING id"
            )
            
            # Add order items
            total_amount = 0
            for item in cart_items:
                product = self.db.query(f"SELECT price FROM products WHERE id = {item['product_id']}")
                item_total = product['price'] * item['quantity']
                total_amount += item_total
                
                self.db.execute(
                    f"INSERT INTO order_items (order_id, product_id, quantity, price) "
                    f"VALUES ({order_id}, {item['product_id']}, {item['quantity']}, {product['price']})"
                )
            
            # Update order with total
            self.db.execute(f"UPDATE orders SET total_amount = {total_amount} WHERE id = {order_id}")
            
            # Commit transaction
            self.db.commit_transaction()
            
            return order_id
            
        except Exception as e:
            # Rollback in case of error
            self.db.rollback_transaction()
            raise e
    
    def process_payment(self, order_id, payment_method):
        """Process payment for an order."""
        order = self.db.query(f"SELECT * FROM orders WHERE id = {order_id}")
        
        # Process payment through payment service
        payment_result = self.payment_service.process_payment(
            amount=order['total_amount'],
            payment_method=payment_method,
            order_id=order_id
        )
        
        if payment_result['success']:
            # Update order status
            self.db.execute(f"UPDATE orders SET status = 'paid' WHERE id = {order_id}")
            return True
        else:
            # Handle payment failure
            self.db.execute(
                f"UPDATE orders SET status = 'payment_failed', "
                f"payment_error = '{payment_result['error']}' WHERE id = {order_id}"
            )
            return False
    
    def fulfill_order(self, order_id):
        """Fulfill an order by reserving inventory and scheduling shipping."""
        order_items = self.db.query(f"SELECT * FROM order_items WHERE order_id = {order_id}")
        
        # Check inventory availability
        for item in order_items:
            inventory_result = self.inventory_service.reserve_inventory(
                product_id=item['product_id'],
                quantity=item['quantity']
            )
            
            if not inventory_result['success']:
                # Handle inventory shortage
                self.db.execute(
                    f"UPDATE orders SET status = 'inventory_shortage', "
                    f"fulfillment_error = '{inventory_result['error']}' WHERE id = {order_id}"
                )
                return False
        
        # Update order status
        self.db.execute(f"UPDATE orders SET status = 'fulfilled' WHERE id = {order_id}")
        return True


# -------------------------------------------------------------------------
# Single Responsibility Principle: Each class has one reason to change
# -------------------------------------------------------------------------

# Instead of a monolithic UserService, we have separate services:

class AuthenticationService:
    """Handles user authentication."""
    
    def __init__(self, database_connection):
        self.db = database_connection
    
    def login(self, email, password):
        """Authenticate a user and create a session."""
        user = self.db.query(f"SELECT * FROM users WHERE email = '{email}'")
        
        if not user:
            return {'success': False, 'error': 'User not found'}
        
        # In a real system, we would use proper password hashing
        if user['password_hash'] != hash(password):
            return {'success': False, 'error': 'Invalid password'}
        
        # Create session
        session_id = self._create_session(user['id'])
        
        return {
            'success': True,
            'user_id': user['id'],
            'session_id': session_id
        }
    
    def logout(self, session_id):
        """End a user session."""
        self.db.execute(f"UPDATE sessions SET active = FALSE WHERE id = '{session_id}'")
        return {'success': True}
    
    def verify_session(self, session_id):
        """Verify if a session is valid."""
        session = self.db.query(
            f"SELECT * FROM sessions WHERE id = '{session_id}' AND active = TRUE "
            f"AND expires_at > CURRENT_TIMESTAMP"
        )
        
        return {'valid': bool(session), 'user_id': session['user_id'] if session else None}
    
    def _create_session(self, user_id):
        """Create a new session for a user."""
        # Generate a unique session ID
        import uuid
        session_id = str(uuid.uuid4())
        
        # Set expiration (e.g., 24 hours from now)
        import datetime
        expires_at = datetime.datetime.now() + datetime.timedelta(hours=24)
        
        # Store session in database
        self.db.execute(
            f"INSERT INTO sessions (id, user_id, active, expires_at) "
            f"VALUES ('{session_id}', {user_id}, TRUE, '{expires_at}')"
        )
        
        return session_id


class ProfileService:
    """Manages user profile information."""
    
    def __init__(self, database_connection):
        self.db = database_connection
    
    def get_profile(self, user_id):
        """Get a user's profile information."""
        return self.db.query(f"SELECT * FROM user_profiles WHERE user_id = {user_id}")
    
    def update_profile(self, user_id, profile_data):
        """Update a user's profile information."""
        # Build SET clause for SQL update
        set_clause = ", ".join([f"{key} = '{value}'" for key, value in profile_data.items()])
        
        self.db.execute(
            f"UPDATE user_profiles SET {set_clause} WHERE user_id = {user_id}"
        )
        
        return {'success': True}
    
    def get_addresses(self, user_id):
        """Get a user's saved addresses."""
        return self.db.query(f"SELECT * FROM user_addresses WHERE user_id = {user_id}")
    
    def add_address(self, user_id, address_data):
        """Add a new address for a user."""
        # Extract address fields
        fields = ", ".join(address_data.keys())
        values = ", ".join([f"'{value}'" for value in address_data.values()])
        
        self.db.execute(
            f"INSERT INTO user_addresses (user_id, {fields}) VALUES ({user_id}, {values})"
        )
        
        return {'success': True}


class PermissionService:
    """Handles role-based access control."""
    
    def __init__(self, database_connection):
        self.db = database_connection
    
    def get_user_roles(self, user_id):
        """Get all roles assigned to a user."""
        return self.db.query(f"SELECT role FROM user_roles WHERE user_id = {user_id}")
    
    def has_permission(self, user_id, permission):
        """Check if a user has a specific permission."""
        # Get user roles
        roles = self.get_user_roles(user_id)
        
        # Check if any role has the required permission
        for role in roles:
            role_permissions = self.db.query(
                f"SELECT * FROM role_permissions WHERE role = '{role}' AND permission = '{permission}'"
            )
            if role_permissions:
                return True
        
        return False
    
    def assign_role(self, user_id, role):
        """Assign a role to a user."""
        self.db.execute(
            f"INSERT INTO user_roles (user_id, role) VALUES ({user_id}, '{role}')"
        )
        
        return {'success': True}
    
    def revoke_role(self, user_id, role):
        """Revoke a role from a user."""
        self.db.execute(
            f"DELETE FROM user_roles WHERE user_id = {user_id} AND role = '{role}'"
        )
        
        return {'success': True}


# -------------------------------------------------------------------------
# Example usage: How these components work together
# -------------------------------------------------------------------------

def example_usage():
    """Demonstrate how these components work together."""
    # This is a simplified example - in a real system, you would use a proper DB connection
    db_connection = MockDatabaseConnection()
    
    # Initialize services
    product_catalog = ProductCatalog(db_connection)
    auth_service = AuthenticationService(db_connection)
    profile_service = ProfileService(db_connection)
    permission_service = PermissionService(db_connection)
    
    # User logs in
    login_result = auth_service.login("<EMAIL>", "password123")
    if not login_result['success']:
        return "Login failed: " + login_result['error']
    
    user_id = login_result['user_id']
    session_id = login_result['session_id']
    
    # Check if user has permission to place orders
    if not permission_service.has_permission(user_id, "place_orders"):
        return "User does not have permission to place orders"
    
    # Get user's addresses
    addresses = profile_service.get_addresses(user_id)
    shipping_address = addresses[0] if addresses else None
    
    if not shipping_address:
        return "User has no shipping address"
    
    # Search for products
    search_results = product_catalog.search_products("smartphone")
    
    # Add a product to cart
    cart = ShoppingCart(user_id, db_connection)
    cart.add_item(search_results[0]['id'], quantity=1)
    
    # Create and process order
    payment_service = MockPaymentService()
    inventory_service = MockInventoryService()
    order_processor = OrderProcessor(db_connection, payment_service, inventory_service)
    
    cart_items = cart.get_items()
    order_id = order_processor.create_order(user_id, cart_items, shipping_address)
    
    # Process payment
    payment_success = order_processor.process_payment(order_id, "credit_card")
    if not payment_success:
        return "Payment failed"
    
    # Fulfill order
    fulfillment_success = order_processor.fulfill_order(order_id)
    if not fulfillment_success:
        return "Order fulfillment failed"
    
    # Clear cart after successful order
    cart.clear()
    
    # User logs out
    auth_service.logout(session_id)
    
    return "Order placed successfully"


# Mock classes for demonstration purposes
class MockDatabaseConnection:
    """Mock database connection for demonstration."""
    def query(self, sql):
        # This would normally execute SQL and return results
        print(f"Executing query: {sql}")
        # Return mock data based on the query
        if "products WHERE name LIKE" in sql:
            return [{'id': 1, 'name': 'Smartphone X', 'price': 999.99}]
        elif "cart_items WHERE user_id" in sql:
            return [{'product_id': 1, 'quantity': 1}]
        elif "user_profiles WHERE user_id" in sql:
            return {'name': 'John Doe', 'email': '<EMAIL>'}
        elif "user_addresses WHERE user_id" in sql:
            return [{'street': '123 Main St', 'city': 'Anytown', 'zip': '12345'}]
        elif "users WHERE email" in sql:
            return {'id': 1, 'password_hash': hash("password123")}
        elif "role_permissions WHERE role" in sql:
            return [{'permission': 'place_orders'}]
        elif "user_roles WHERE user_id" in sql:
            return [{'role': 'customer'}]
        return None
    
    def execute(self, sql):
        # This would normally execute SQL and modify data
        print(f"Executing statement: {sql}")
        if "RETURNING id" in sql:
            return 1  # Return mock order ID
        return True
    
    def begin_transaction(self):
        print("Beginning transaction")
    
    def commit_transaction(self):
        print("Committing transaction")
    
    def rollback_transaction(self):
        print("Rolling back transaction")


class MockPaymentService:
    """Mock payment service for demonstration."""
    def process_payment(self, amount, payment_method, order_id):
        print(f"Processing {payment_method} payment of ${amount} for order {order_id}")
        return {'success': True}


class MockInventoryService:
    """Mock inventory service for demonstration."""
    def reserve_inventory(self, product_id, quantity):
        print(f"Reserving {quantity} units of product {product_id}")
        return {'success': True}


if __name__ == "__main__":
    result = example_usage()
    print(result)
