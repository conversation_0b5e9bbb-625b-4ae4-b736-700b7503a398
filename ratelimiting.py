import time
import threading

class Packets():
    def __init__(self, stream=[]) -> None:
        self.packets = stream

class bucketdata():

    bucket = []
    _instance_lock = threading.Lock()
    _unique_instance = None

    def __new__(cls):
        with cls._instance_lock:
            if cls._unique_instance is None:
                cls._unique_instance = super(bucketdata, cls).__new__(cls)
        return cls._unique_instance
    

    def forwardPackets(self,stream):
        self.bucket.append(stream)

    @classmethod
    def getbucketstatus(self):
        print("data in bucket is", self.bucket)

class Tokenbucket():
    def __init__(self, rate, tokens=0) -> None:
        self.tokens = tokens
        self.rate = rate #seconds
        self.t1 = time.time()
        # threading.Timer(self.rate, self.addTokens).start()

    def check(self):
        if time.time() - self.t1 > self.rate:
            print("here")
            self.t1 = time.time()
            self.addTokens()


    def addTokens(self):
        self.tokens += 5
        print("no of tokens added")

    def getStatus(self):
        print("no of tokens in the bucket ", self.tokens)

    def process(self, packets):
        if len(packets.packets) <= self.tokens:
            self.tokens = self.tokens - len(packets.packets)
            bt = bucketdata()
            print("data passed", packets.packets )
            bt.forwardPackets(packets.packets)
        else:
            bt = bucketdata()
            print("data dropped", packets.packets[0:self.tokens])
            bt.forwardPackets(packets.packets[0:self.tokens])
            self.tokens = 0




        



tb = Tokenbucket(5)
p1= Packets([1,2,5,8])
p2= Packets([6,7,8,9])

tb.check()
tb.process(p1)
tb.check()
tb.process(p2)
tb.check()
tb.getStatus()

    
