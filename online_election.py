class TopVotedCandidate:

    def __init__(self, persons: List[int], times: List[int]):
        self.times = times
        self.persons = persons
        

    def q(self, t: int) -> int:
        if t in self.times:
            index = [i for i,v in enumerate(self.times) if v == t][0]
            return self.persons[index]
        else:
            index = [i for i,v in enumerate(self.times) if v > t][0]
            return self.persons[index-1]
        # print("t", t)
        # print("index",index)
        # print("================")
        # return self.persons[index]
        


# Your TopVotedCandidate object will be instantiated and called as such:
# obj = TopVotedCandidate(persons, times)
# param_1 = obj.q(t)

# Input
# ["TopVotedCandidate", "q", "q", "q", "q", "q", "q"]
# [[[0, 1, 1, 0, 0, 1, 0], [0, 5, 10, 15, 20, 25, 30]], [3], [12], [25], [15], [24], [8]]
# Output
# [null, 0, 1, 1, 0, 0, 1]
