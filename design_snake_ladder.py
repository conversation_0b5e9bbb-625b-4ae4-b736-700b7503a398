from random import randint

class LadderGrid():
    def __init__(self,number,positions) -> None:
        self.numberLadder = number
        self.positions = positions

class SnakesGrid():
    def __init__(self, number,positions) -> None:
        self.numberSnakes = number
        self.positions = positions

class Rolldice():
    def __init__(self) -> None:
        pass

    def roll(self,player):
        self.player = player
        self.player.position = self.player.position + randint(1, 6)


class Player():
    def __init__(self, snakegrid, laddergrid, name = ""):
        self.name  = name
        self.position = 0
        self.won = False
        self.snakegrid = snakegrid
        self.laddergrid = laddergrid

    def getPostion(self):
        self.checkSnakesorLadder()
        res = self.gameResult()
        if res is True:
            self.won = True
        return self.position
    
    def checkSnakesorLadder(self):
        if self.position in self.snakegrid.keys():
            newVal = self.snakegrid.get(self.position)
            self.position = newVal
            print("bit by snake, new pos for player {} is".format(self.name), self.position)
        if self.position in self.laddergrid.keys():
            newVal = self.laddergrid.get(self.position)
            self.position = newVal
            print("found a ladder, new pos for player {} is".format(self.name), self.position)

    def gameResult(self):
        if self.position >= 100:
            print("player {}, won the game".format(self.name))
            return True
        return False








S= SnakesGrid(9, {62 : 5,
33 : 6,
49 : 9,
88 : 16,
41 :20,
56 : 53,
98 : 64,
93 : 73,
95  : 75})

L = LadderGrid(8, {2 : 37,
27  : 46,
10 : 32,
51 : 68,
61 : 79,
65 : 84,
71 : 91,
81 : 100}
)

p1 = Player(S.positions, L.positions, "Peeyush")
p2 = Player(S.positions, L.positions, "Nupur")

dice = Rolldice()

while (True):
    dice.roll(p1)
    dice.roll(p2)
    pos1 = p1.getPostion()
    pos2 = p2.getPostion()
    print("position for player 1 is", pos1)
    print("position for player 2 is", pos2)
    if p1.won is True:
        break
    if p2.won is True:
        break
    




