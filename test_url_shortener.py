"""
Tests for the URL Shortener implementation.
"""
import unittest
from url_shortener import URLShortener
from datetime import datetime, timedelta
import time

class TestURLShortener(unittest.TestCase):
    """Test cases for the URLShortener class."""
    
    def setUp(self):
        """Set up a new URLShortener instance for each test."""
        self.shortener = URLShortener(domain="test.com/")
    
    def test_shorten_url(self):
        """Test shortening a URL."""
        original_url = "https://example.com"
        shortened_url = self.shortener.shorten_url(original_url)
        
        # Check that the shortened URL starts with the domain
        self.assertTrue(shortened_url.startswith("test.com/"))
        
        # Check that the short code is 6 characters long
        short_code = shortened_url.replace("test.com/", "")
        self.assertEqual(len(short_code), 6)
    
    def test_get_original_url(self):
        """Test retrieving the original URL."""
        original_url = "https://example.com"
        shortened_url = self.shortener.shorten_url(original_url)
        short_code = shortened_url.replace("test.com/", "")
        
        # Check that we can retrieve the original URL
        retrieved_url = self.shortener.get_original_url(short_code)
        self.assertEqual(retrieved_url, original_url)
        
        # Check that the click count was incremented
        stats = self.shortener.get_url_stats(short_code)
        self.assertEqual(stats['clicks'], 1)
    
    def test_custom_code(self):
        """Test using a custom code."""
        original_url = "https://example.com"
        custom_code = "my-custom-code"
        shortened_url = self.shortener.shorten_url(original_url, custom_code)
        
        # Check that the shortened URL contains the custom code
        self.assertEqual(shortened_url, f"test.com/{custom_code}")
        
        # Check that we can retrieve the original URL using the custom code
        retrieved_url = self.shortener.get_original_url(custom_code)
        self.assertEqual(retrieved_url, original_url)
    
    def test_duplicate_custom_code(self):
        """Test using a duplicate custom code."""
        original_url1 = "https://example.com"
        original_url2 = "https://example.org"
        custom_code = "my-custom-code"
        
        # Shorten the first URL with the custom code
        self.shortener.shorten_url(original_url1, custom_code)
        
        # Try to shorten the second URL with the same custom code
        result = self.shortener.shorten_url(original_url2, custom_code)
        self.assertTrue("already in use" in result)
    
    def test_expiration(self):
        """Test URL expiration."""
        original_url = "https://example.com"
        # Set expiration to 0 days (immediate expiration for testing)
        shortened_url = self.shortener.shorten_url(original_url, expiration_days=0)
        short_code = shortened_url.replace("test.com/", "")
        
        # Force the URL to expire by manipulating the expiration date
        url_details = self.shortener.url_mapping[short_code]
        url_details['expiration_date'] = datetime.now() - timedelta(seconds=1)
        
        # Check that the URL has expired
        retrieved_url = self.shortener.get_original_url(short_code)
        self.assertEqual(retrieved_url, "URL has expired.")
        
        # Check that the URL is marked as expired in the stats
        stats = self.shortener.get_url_stats(short_code)
        self.assertTrue(stats['expired'])
    
    def test_nonexistent_url(self):
        """Test retrieving a nonexistent URL."""
        retrieved_url = self.shortener.get_original_url("nonexistent")
        self.assertEqual(retrieved_url, "URL not found.")
        
        stats = self.shortener.get_url_stats("nonexistent")
        self.assertEqual(stats, "URL not found.")
    
    def test_list_all_urls(self):
        """Test listing all URLs."""
        # Shorten some URLs
        self.shortener.shorten_url("https://example.com")
        self.shortener.shorten_url("https://example.org", "custom")
        
        # List all URLs
        urls = self.shortener.list_all_urls()
        
        # Check that we have 2 URLs
        self.assertEqual(len(urls), 2)
        
        # Check that one of them has a custom code
        for code, details in urls.items():
            if 'custom_codes' in details and details['custom_codes']:
                self.assertEqual(details['custom_codes'][0], "custom")
                break
        else:
            self.fail("No URL with custom code found")

if __name__ == "__main__":
    unittest.main()
