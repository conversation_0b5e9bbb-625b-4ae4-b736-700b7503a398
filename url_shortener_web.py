"""
Web interface for the URL Shortener using Flask.
"""
from flask import Flask, request, jsonify, redirect, render_template_string
from url_shortener import URLShortener
import os

app = Flask(__name__)
shortener = URLShortener(domain="http://localhost:5000/")

# HTML template for the home page
HOME_TEMPLATE = """
<!DOCTYPE html>
<html>
<head>
    <title>URL Shortener</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            background-color: #f9f9f9;
            border-radius: 5px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #333;
        }
        input, button {
            padding: 10px;
            margin: 5px 0;
        }
        input[type="text"], input[type="number"] {
            width: 100%;
            box-sizing: border-box;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
            border-radius: 3px;
        }
        button:hover {
            background-color: #45a049;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            background-color: #e9f7ef;
            border-radius: 3px;
        }
        .urls-list {
            margin-top: 30px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>URL Shortener</h1>
        
        <form id="shortenForm">
            <div>
                <label for="url">URL to shorten:</label>
                <input type="text" id="url" name="url" placeholder="https://example.com" required>
            </div>
            
            <div>
                <label for="custom_code">Custom code (optional):</label>
                <input type="text" id="custom_code" name="custom_code" placeholder="my-custom-code">
            </div>
            
            <div>
                <label for="expiration_days">Expiration days (optional):</label>
                <input type="number" id="expiration_days" name="expiration_days" placeholder="30">
            </div>
            
            <button type="submit">Shorten URL</button>
        </form>
        
        <div id="result" class="result" style="display: none;"></div>
        
        <div class="urls-list">
            <h2>Recent URLs</h2>
            <table>
                <thead>
                    <tr>
                        <th>Short URL</th>
                        <th>Original URL</th>
                        <th>Clicks</th>
                        <th>Expiration</th>
                    </tr>
                </thead>
                <tbody id="urlsList">
                    {% for code, details in urls.items() %}
                    <tr>
                        <td>
                            <a href="{{ shortener_domain }}{{ code }}" target="_blank">
                                {{ shortener_domain }}{{ code }}
                            </a>
                            {% if details.custom_codes %}
                            <br>
                            <small>Custom: 
                                {% for custom in details.custom_codes %}
                                <a href="{{ shortener_domain }}{{ custom }}" target="_blank">{{ custom }}</a>
                                {% endfor %}
                            </small>
                            {% endif %}
                        </td>
                        <td>{{ details.original_url }}</td>
                        <td>{{ details.clicks }}</td>
                        <td>
                            {% if details.expired %}
                            <span style="color: red;">Expired</span>
                            {% elif details.expiration_date != 'Never' %}
                            {{ details.expiration_date }}
                            {% else %}
                            Never
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    
    <script>
        document.getElementById('shortenForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const url = document.getElementById('url').value;
            const customCode = document.getElementById('custom_code').value;
            const expirationDays = document.getElementById('expiration_days').value;
            
            fetch('/api/shorten', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    url: url,
                    custom_code: customCode || null,
                    expiration_days: expirationDays ? parseInt(expirationDays) : null
                }),
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('result');
                resultDiv.style.display = 'block';
                
                if (data.error) {
                    resultDiv.innerHTML = `<p>Error: ${data.error}</p>`;
                } else {
                    resultDiv.innerHTML = `
                        <p>Shortened URL: <a href="${data.shortened_url}" target="_blank">${data.shortened_url}</a></p>
                    `;
                }
                
                // Reload the page to update the URLs list
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            })
            .catch(error => {
                console.error('Error:', error);
                const resultDiv = document.getElementById('result');
                resultDiv.style.display = 'block';
                resultDiv.innerHTML = `<p>Error: ${error.message}</p>`;
            });
        });
    </script>
</body>
</html>
"""

@app.route('/')
def home():
    """Render the home page."""
    urls = shortener.list_all_urls()
    return render_template_string(
        HOME_TEMPLATE, 
        urls=urls, 
        shortener_domain=shortener.domain
    )

@app.route('/<short_code>')
def redirect_to_url(short_code):
    """Redirect to the original URL."""
    original_url = shortener.get_original_url(short_code)
    if original_url.startswith('http'):
        return redirect(original_url)
    else:
        return jsonify({'error': original_url}), 404

@app.route('/api/shorten', methods=['POST'])
def shorten_url():
    """API endpoint to shorten a URL."""
    data = request.json
    url = data.get('url')
    custom_code = data.get('custom_code')
    expiration_days = data.get('expiration_days')
    
    if not url:
        return jsonify({'error': 'URL is required'}), 400
    
    try:
        shortened_url = shortener.shorten_url(url, custom_code, expiration_days)
        if shortened_url.startswith('Custom code'):
            return jsonify({'error': shortened_url}), 400
        return jsonify({'shortened_url': shortened_url})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/stats/<short_code>')
def get_stats(short_code):
    """API endpoint to get URL statistics."""
    stats = shortener.get_url_stats(short_code)
    if isinstance(stats, str):
        return jsonify({'error': stats}), 404
    return jsonify(stats)

@app.route('/api/urls')
def list_urls():
    """API endpoint to list all URLs."""
    urls = shortener.list_all_urls()
    return jsonify(urls)

if __name__ == '__main__':
    app.run(debug=True)
