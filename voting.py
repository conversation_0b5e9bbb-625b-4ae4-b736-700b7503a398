from collections import OrderedDict

class VotingSystem():
    def __init__(self, teams) -> None:
        self.voting_list = []
        self.countdict = OrderedDict()
        for team in teams:
            self.countdict[team] = []
        

    def vote(self,votefirst,votesecond,votethird):
        self.votefirst = votefirst
        self.votesecond = votesecond
        self.votethird = votethird

        templist  = self.votefirst + self.votesecond + self.votethird

        self.voting_list.append(templist)
        print("voting list",self.voting_list)
        return self.voting_list
        

    def rank(self, result):
        for votes in result:
            i=3
            for c in votes:
                self.countdict[c].append(i)
                i-=1
        print("countdict", self.countdict)
        return self.countdict
    




class ResultVoting():
    def __init__(self) -> None:
        pass

    def resultVote(self, res):
        result_dict = OrderedDict()
        for key, val in res.items():
            result_dict[key] = sum(val)



        result_dict = sorted(result_dict, key=result_dict.get, reverse=True)
        print("result dict", result_dict)
        return "".join(result_dict)




ob = VotingSystem(["A","B","C"])

res = ob.vote("A", "B","C")
res = ob.vote("A", "C","B")
res = ob.vote("A", "C","B")
res = ob.vote("A", "C","B")

ranked = ob.rank(res)

re = ResultVoting()
re.resultVote(ranked)
